{"name": "MediaTemplate", "version": "0.0.1", "private": true, "dependencies": {"@antscorp/ants-account-sharing": "1.0.43", "@antscorp/ants-account-support": "1.0.17", "@antscorp/antsomi-locales": "1.0.63", "@antscorp/antsomi-ui": "2.0.88-feedback-rating.1", "@antscorp/html-capture": "1.11.13-beta.1", "@antscorp/icons": "0.27.59", "@atlaskit/tree": "8.6.3", "@babel/core": "^7.16.0", "@emotion/react": "11.7.1", "@emotion/styled": "11.6.0", "@fortawesome/fontawesome-svg-core": "6.1.1", "@fortawesome/free-brands-svg-icons": "6.1.1", "@fortawesome/free-regular-svg-icons": "6.1.1", "@fortawesome/free-solid-svg-icons": "6.1.1", "@fortawesome/react-fontawesome": "0.1.18", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@reduxjs/toolkit": "1.7.1", "@storybook/router": "5.3.19", "@svgr/webpack": "^5.5.0", "@tanstack/react-query": "4.20.4", "@tanstack/react-query-devtools": "4.20.4", "@testing-library/jest-dom": "5.16.4", "@testing-library/react": "13.1.1", "@types/currency-formatter": "1.5.1", "ace-builds": "1.4.14", "animate.css": "4.1.1", "antd": "4.20.2", "axios": "0.25.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chalk": "4.1.2", "classnames": "2.3.1", "container-query-polyfill": "0.1.2", "core-js": "2.6.5", "cross-env": "7.0.3", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "currency-formatter": "1.5.9", "dayjs": "1.10.8", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-prettier": "8.3.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react-hooks": "4.3.0", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "font-awesome": "4.7.0", "fontfaceobserver": "2.1.0", "fs-extra": "^10.0.0", "html-to-image": "1.9.0", "html-webpack-plugin": "^5.5.0", "html2canvas": "1.4.1", "husky": "7.0.4", "i18next": "21.6.16", "i18next-browser-languagedetector": "6.1.2", "i18next-scanner": "3.1.0", "identity-obj-proxy": "^3.0.0", "immer": "9.0.21", "immutability-helper": "^3.1.1", "inquirer": "7.3.3", "inquirer-directory": "2.2.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-styled-components": "7.0.8", "jest-watch-typeahead": "^1.0.0", "lint-staged": "12.1.7", "lodash": "4.17.21", "mini-css-extract-plugin": "^2.4.5", "moment": "2.29.2", "node-plop": "0.26.2", "pako": "2.0.4", "plop": "2.7.4", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prettier": "2.5.1", "produce": "0.4.1", "prompts": "^2.4.2", "qs": "6.10.3", "re-resizable": "6.9.5", "react": "18.1.0", "react-ace": "9.5.0", "react-app-polyfill": "^3.0.0", "react-beautiful-dnd": "13.1.0", "react-color": "2.19.3", "react-dev-utils": "^12.0.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.1.0", "react-dragline": "1.1.0", "react-fast-compare": "3.2.0", "react-froala-wysiwyg": "4.0.16", "react-helmet-async": "1.3.0", "react-i18next": "11.16.7", "react-intersection-observer": "^9.10.3", "react-is": "17.0.2", "react-query": "3.39.1", "react-quill": "1.3.5", "react-redux": "7.2.6", "react-refresh": "^0.11.0", "react-resizable": "3.0.4", "react-router": "5.2.0", "react-router-dom": "5.3.1", "react-test-renderer": "17.0.2", "react-tracked": "1.7.10", "redux-injectors": "1.3.0", "redux-saga": "1.1.3", "redux-undo": "1.0.1", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "rimraf": "3.0.2", "sanitize.css": "13.0.0", "sass": "1.49.0", "sass-loader": "^12.3.0", "scope-css": "1.2.1", "semver": "^7.3.5", "serve": "13.0.2", "shelljs": "0.8.5", "source-map-loader": "^3.0.0", "stacktrace-js": "2.0.2", "style-loader": "^3.3.1", "styled-components": "5.3.5", "stylelint": "14.2.0", "stylelint-config-recommended": "6.0.0", "stylelint-config-styled-components": "0.1.1", "stylelint-processor-styled-components": "1.10.0", "swiper": "8.1.4", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "ts-node": "10.4.0", "tsconfig-paths-webpack-plugin": "3.5.2", "typescript": "4.6.4", "use-resize-observer": "9.0.2", "video-react": "0.15.0", "web-vitals": "2.1.2", "webfontloader": "1.6.28", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "scripts": {"prepare": "husky install", "start": "node ./scripts/start.js", "build": "cross-env NODE_ENV=development ASSET_PATH=//sandbox-st-media-template.antsomi.com node ./scripts/build.sandbox.js", "build:debug": "cross-env NODE_ENV=development ASSET_PATH=./ node ./scripts/build.debug.js", "build:production": "cross-env NODE_ENV=production ASSET_PATH=//st-media-template.antsomi.com node ./scripts/build.production.js", "build:staging": "cross-env NODE_ENV=production ASSET_PATH=//staging-st-media-template.antsomi.com node ./scripts/build.production.js", "test": "node ./scripts/test.js", "build:css": "postcss src/assets/css/tailwind.scss -o src/assets/css/global.scss", "watch:css": "postcss src/assets/css/tailwind.scss -o src/assets/css/global.scss", "start:prod": "yarn run build && serve -s build", "test:generators": "ts-node ./internals/testing/generators/test-generators.ts", "checkTs": "tsc --noEmit", "eslint": "eslint --ext js,ts,tsx", "lint": "cross-env NODE_ENV=production yarn run eslint src", "lint:fix": "cross-env NODE_ENV=production yarn run eslint --fix src", "lint:css": "cross-env NODE_ENV=production stylelint src/**/*.css", "generate": "plop --plopfile internals/generators/plopfile.ts", "cleanAndSetup": "ts-node ./internals/scripts/clean.ts", "prettify": "prettier --write", "extract-messages": "i18next-scanner --config=internals/extractMessages/i18next-scanner.config.js", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=14.x"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["cross-env NODE_ENV=production yarn run eslint --fix"], "*.{md,json}": ["prettier --write"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*/*.d.ts", "!src/**/*/Loadable.{js,jsx,ts,tsx}", "!src/**/*/messages.ts", "!src/**/*/types.ts", "!src/index.tsx"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": ["/Users/<USER>/code/structure/react-boilerplate/src"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy", "^app(.*)$": "<rootDir>/src/app$1", "^locales(.*)$": "<rootDir>/src/locales$1", "^utils(.*)$": "<rootDir>/src/utils$1", "^assets(.*)$": "<rootDir>/src/assets$1"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true, "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}}, "resolutions": {"quill": "1.3.7"}, "devDependencies": {"@babel/plugin-transform-react-jsx-source": "7.18.6", "@commitlint/cli": "17.5.1", "@commitlint/config-conventional": "17.4.4", "@storybook/addon-actions": "^6.4.19", "@storybook/addon-essentials": "^6.4.19", "@storybook/addon-interactions": "^6.4.19", "@storybook/addon-links": "^6.4.19", "@storybook/addon-postcss": "2.0.0", "@storybook/builder-webpack5": "^6.4.19", "@storybook/manager-webpack5": "^6.4.19", "@storybook/react": "^6.4.19", "@storybook/testing-library": "^0.0.9", "@types/fontfaceobserver": "2.1.0", "@types/jest": "27.4.0", "@types/lodash": "4.14.178", "@types/node": "16.11.32", "@types/pako": "2.0.0", "@types/react": "18.0.8", "@types/react-beautiful-dnd": "13.1.2", "@types/react-color": "3.0.6", "@types/react-dom": "18.0.3", "@types/react-redux": "7.1.22", "@types/react-router-dom": "5.3.3", "@types/react-test-renderer": "17.0.1", "@types/rimraf": "3.0.2", "@types/shelljs": "0.8.11", "@types/styled-components": "5.1.25", "@types/testing-library__jest-dom": "5.14.2", "@types/webfontloader": "1.6.34", "@types/webpack": "5.28.0", "@types/webpack-env": "1.16.3", "autoprefixer": "10.4.2", "babel-plugin-macros": "3.1.0", "babel-plugin-styled-components": "2.0.7", "customize-cra": "1.0.0", "husky": "7.0.4", "less": "4.2.0", "less-loader": "12.2.0", "postcss-cli": "9.1.0", "react-app-rewired": "2.1.11", "twin.macro": "2.8.2", "typedoc": "0.25.4"}, "packageManager": "yarn@1.22.22"}