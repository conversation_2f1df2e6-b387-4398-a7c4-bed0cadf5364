# sandbox-st-media-template.antsomi.com
server {
        client_max_body_size 50m;
        listen 8080;
        server_name sandbox-st-media-template.antsomi.com;
        access_log /data/www/logs/sandbox-st-media-template.antsomi.com;
        access_log on;
        error_log /data/www/logs/sandbox-st-media-template.antsomi.com;
        root /data/www/public_html/sandbox-st-media-template.antsomi.com;
        include /etc/nginx/conf.d/v1.error_page;

        location /base64-img {
                      location ~* ^.+\.(png)$ {
                                  add_header Cache-Control no-cache;
                                  add_header Access-Control-Allow-Origin *;
                      }
        }

        location /image {
                      location ~* ^.+\.(png)$ {
                                  add_header Cache-Control no-cache;
                                  add_header Access-Control-Allow-Origin *;
                      }
        }

        location / {
                        location ~* ^.+\.(png|gif|jpg|jpeg|css|js|swf|ico|json)$ {
                                  expires 1y;
                                  add_header Cache-Control public;
                                  add_header P3P 'policyref="/w3c/p3p.xml", CP="IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT", CP="CAO PSA OUR"';
                                  add_header Access-Control-Allow-Origin *;
                        }

                        expires 1y;
                        add_header Access-Control-Allow-Origin *;
                        add_header Cache-Control public;
                        add_header P3P 'policyref="/w3c/p3p.xml", CP="IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT", CP="CAO PSA OUR"';
        }

        location ~ /\. {
                        deny  all;
        }

}
# end sandbox-st-media-template.antsomi.com
