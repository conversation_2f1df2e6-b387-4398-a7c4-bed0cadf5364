module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  prefix: 'ants-',
  theme: {
    extend: {
      borderColor: {
        alto: '#E0E0E0',
        grey1: '#abb7ce',
        grey2: '#D2D2D2',
        mystic: 'var(--mystic-color)',
      },
      borderRadius: {
        box: 'var(--box-border-radius)',
        button: 'var(--btn-border-radius)',
        'box-3': 'var(--box-border-radius-3)',
        'box-5': 'var(--box-border-radius-5)',
      },
      boxShadow: {
        'cus-default': '0px 4px 10px 0 rgba(0, 0, 0, 0.2)',
        'cus-sm': '0 2px 3px 0 rgba(15, 19, 23, 0.1)',
        'cus-md': '0 3px 0 0 rgba(15, 19, 23, 0.05)',
        'cus-xl': '0 0 10px 0 rgba(0, 0, 0, 0.2)',
        'cus-input': '0 1px 0 0 #e0e0e0',
        'cus-input-active': '0 1px 0 0 #194e8d',
      },
      fontFamily: {
        roboto: ['Roboto'],
      },
      colors: {
        background: 'var(--background-color)',
        'background-primary': 'var(--background-primary-color)',
        'background-warning': 'var(--background-warning-color)',
        'background-transparent-03': 'var(--background-transparent-03)',
        primary: 'var(--primary-color)',
        red: 'var(--red-color)',
        'red-2': 'var(--red-2-color)',
        black: 'var(--black-color)',
        gray: 'var(--gray-color)',
        'gray-2': 'var(--gray-2-color)',
        'gray-3': 'var(--gray-3-color)',
        'gray-4': 'var(--gray-4-color)',
        'gray-5': 'var(--gray-5-color)',
        'gray-6': 'var(--gray-6-color)',
        'gray-7': 'var(--gray-7-color)',
        'gray-athens': 'var(--gray-athens-color)',
        gallery: 'var(--gallery-color)',
        mercury: 'var(--mercury-color)',
        nobel: 'var(--nobel-color)',
        'blue-2': 'var(--blue-2-color)',
        'blue-3': 'var(--blue-3-color)',
        'blue-4': 'var(--blue-4-color)',
        'blue-second-2': 'var(--blue-second-2-color)',
        'blue-second': 'var(--blue-second-color)',
        'blue-active': 'var(--blue-active-color)',
        'white-ice': 'var(--white-ice-color)',
        'button-hover': 'var(--btn-hover-color)',
        'button-primary-hover': 'var(--btn-primary-hover-color)',
        'button-active': 'var(--btn-active-color)',
        'button-disabled': 'var(--btn-disable-color)',
        'button-primary-active': 'var(--btn-primary-active-color)',
        'button-outline-border': 'var(--btn-outline-border-color)',
        'accent-1': 'var(--accent-1)',
        'accent-2': 'var(--accent-2)',
        'accent-3': 'var(--accent-3)',
        'accent-4': 'var(--accent-4)',
        'accent-5': 'var(--accent-5)',
        'accent-6': 'var(--accent-6)',
        'accent-7': 'var(--accent-7)',
        box: 'var(--border-box-color)',
        divider: 'var(--divider-color)',
        'divider-2': 'var(--divider-2-color)',
        'table-header-bg-color': 'var(--table-header-bg-color)',
        'table-header-border-color': 'var(--table-header-border-color)',
        'table-body-border-color': 'var(--table-body-border-color)',
      },
      fontSize: {
        normal: 'var(--text-normal-font-size)',
        medium: 'var(--text-medium-font-size)',
        large: 'var(--text-large-font-size)',
      },
      spacing: {
        '1px': '1px',
        '3px': '3px',
        '5px': '5px',
        '10px': '10px',
        '15px': '15px',
        '25px': '25px',
        '28px': '28px',
        '30px': '30px',
        '35px': '35px',
        '50px': '50px',
        '100px': '100px',
        '120px': '120px',
        '343px': '343px',
        '250px': '250px',
        '400px': '400px',
        '500px': '500px',
        '650px': '650px',
      },
      textColor: {
        'cus-base': 'var(--text-base-color)',
        'cus-primary': 'var(--text-primary-color)',
        'cus-second': 'var(--text-second-color)',
        'cus-dark': 'var(--text-dark-color)',
        'cus-disable': 'var(--text-disable-color)',
        'cus-link': 'var(--text-link-color)',
        'cus-error': 'var(--text-error-color)',
        'cus-warning': 'var(--text-warning-color)',
      },
      width: {
        100: '100%',
      },
      height: {
        36: '36px',
      },
      zIndex: {
        1: '1',
        2: '2',
        3: '3',
      },
    },
  },
  plugins: [],
  // corePlugins: {
  //   preflight: false,
  // },
};
