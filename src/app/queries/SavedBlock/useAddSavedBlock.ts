// Libraries
import { useMutation } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { createSavedBlock } from 'app/services/MediaTemplateDesign/SavedBlock';
import { createSavedBlock as createMediaJsonSavedBlock } from 'app/services/MediaJsonDesign/SavedBlock';

// Models
import { SavedBlock } from 'app/models';

// Query client
import { queryClient } from 'index';

export const useAddSavedBlock = () => {
  return useMutation(createSavedBlock, {
    onMutate: (_payload: Partial<SavedBlock>) => {
      const previousSavedBlock = queryClient.getQueryData([QUERY_KEYS.GET_SAVED_BLOCKS]);

      // queryClient.setQueryData([QUERY_KEYS.GET_SAVED_BLOCKS], (oldSavedBlocks: any) => {
      //   const savedBlockPayload = new SavedBlock(payload);

      //   console.log([savedBlockPayload, ...oldSavedBlocks]);

      //   return [savedBlockPayload, ...oldSavedBlocks];
      // });

      return { previousSavedBlock };
    },
    onError: (_error, _payload, context) => {
      queryClient.setQueryData([QUERY_KEYS.GET_SAVED_BLOCKS], context?.previousSavedBlock);
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVED_BLOCKS], {
        exact: true,
      });
    },
  });
};

export const useAddMediaJsonSavedBlock = () => {
  return useMutation(createMediaJsonSavedBlock, {
    onMutate: (_payload: Partial<SavedBlock>) => {
      const previousSavedBlock = queryClient.getQueryData([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK]);

      // queryClient.setQueryData([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK], (oldSavedBlocks: any) => {
      //   const savedBlockPayload = new SavedBlock(payload);

      //   console.log([savedBlockPayload, ...oldSavedBlocks]);

      //   return [savedBlockPayload, ...oldSavedBlocks];
      // });

      return { previousSavedBlock };
    },
    onError: (_error, _payload, context) => {
      queryClient.setQueryData([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK], context?.previousSavedBlock);
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK], {
        exact: true,
      });
    },
  });
};
