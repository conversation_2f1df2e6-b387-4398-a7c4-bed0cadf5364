import { ListEvent } from 'app/models/BusinesObject';
import { getListEvents } from 'app/services/MediaTemplateDesign/BusinessObject';
import { QUERY_KEYS } from 'constants/queries';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

export const useGetListEventBySource = <T = ListEvent[]>(
  sourceId: string,
  queryOptions?: Omit<UseQueryOptions<ListEvent[], any, T>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery<ListEvent[], any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_EVENT_BY_SOURCE, sourceId],
    queryFn: async () => {
      const data = await getListEvents({ source_id: sourceId });

      return data.map((row: any) => new ListEvent(row));
    },
    ...queryOptions,
  });
};
