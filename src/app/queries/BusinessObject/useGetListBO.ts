// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Services
import { getListBusinessObject } from 'app/services/MediaTemplateDesign/BusinessObject';

// Constants
import { QUERY_KEYS } from 'constants/queries';

type TDataFnQuery = Record<string, any>;

export const useGetListBO = <T = TDataFnQuery>(queryOptions?: UseQueryOptions<TDataFnQuery, any, T>) => {
  return useQuery<TDataFnQuery, any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_BO],
    queryFn: async () => {
      const { rows: businessObjects } = await getListBusinessObject();

      return businessObjects;
    },
    ...queryOptions,
  });
};
