// Libraries
import { isEmpty } from 'lodash';
import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Types
import { RichMenu } from 'richMenu/containers/Design/types';

// Components
import { Icon, Typography } from '@antscorp/antsomi-ui';
import { Actions, StyledRichMenuItem } from 'richMenu/containers/Design/components/LeftSidePanel/RichMenuItem/styled';
import { WarningIcon } from 'app/components/icons';

// Slice
import { richMenuDesignActions } from '../../../slice';
import { selectGeneral } from '../../../slice/selectors';

interface RichMenuItemProps extends React.HTMLAttributes<HTMLDivElement> {
  richMenu: RichMenu;
  index: number;
  isActive: boolean;
  isCollapsed?: boolean;
}

export const RichMenuItem: React.FC<RichMenuItemProps> = props => {
  const { richMenu, index, isActive, isCollapsed, ...restProps } = props;
  const { menuName, isDefault } = richMenu;
  const { duplicateRichMenu, removeRichMenu, resetLayout } = richMenuDesignActions;
  const dispatch = useDispatch();

  const { errors, isShowError } = useSelector(selectGeneral);

  const isError = useMemo(() => {
    return !isEmpty(errors?.richMenus?.[richMenu.id]);
  }, [errors, richMenu.id]);

  const renderCollapseIcon = () => {
    switch (true) {
      case isCollapsed && isError && isShowError: {
        return (
          <WarningIcon
            style={{
              width: 18,
              height: 14,
            }}
          />
        );
      }
      case isCollapsed && isDefault: {
        return <Icon type="icon-ants-home" />;
      }

      default: {
        return (
          <Typography.Text ellipsis={{ tooltip: true }}>{`${index + 1}${
            !isCollapsed ? `. ${menuName}` : ''
          }`}</Typography.Text>
        );
      }
    }
  };

  return (
    <StyledRichMenuItem $isCollapsed={!!isCollapsed} $isActive={isActive} {...restProps}>
      {renderCollapseIcon()}

      {!isCollapsed && (
        <Actions align="center" gap={4} onClick={e => e.stopPropagation()}>
          {isError && isShowError && (
            <WarningIcon
              style={{
                width: 18,
                height: 14,
              }}
            />
          )}
          {isDefault && <Icon type="icon-ants-home" />}
          <Icon type="icon-ants-restore" size={17} onClick={() => dispatch(resetLayout({ richMenuId: richMenu.id }))} />
          <Icon
            type="icon-ants-material-outline-content-copy"
            size={16}
            onClick={() => dispatch(duplicateRichMenu({ richMenuId: richMenu.id }))}
          />
          <Icon
            type="icon-ants-remove-trash"
            size={16}
            onClick={() => dispatch(removeRichMenu({ richMenuId: richMenu.id }))}
          />
        </Actions>
      )}
    </StyledRichMenuItem>
  );
};
