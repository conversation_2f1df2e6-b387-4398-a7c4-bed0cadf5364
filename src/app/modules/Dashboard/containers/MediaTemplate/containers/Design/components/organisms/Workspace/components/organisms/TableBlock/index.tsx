// Libraries
import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';
import produce from 'immer';
import { useDispatch } from 'react-redux';
import { NumberSize, Resizable, ResizableProps } from 're-resizable';
import isNumber from 'lodash/isNumber';
import sum from 'lodash/sum';
import omit from 'lodash/omit';

// Hooks
import useResizeObserver from 'use-resize-observer';

// Components
import { TableBlockWrapper, TableRow, Table, TableBody } from './styled';

// Atoms
import { Skeleton } from 'app/components/atoms';

// Types
import {
  BlockProps,
  TColumnTableBlock,
  TTableSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Selectors

// Translations

// Ations
import { mediaTemplateDesignActions } from '../../../../../../slice';

// Query
import { useGetDataTableBO, useGetListBO } from 'app/queries/BusinessObject';

// Constants
import { SIDE_PANEL_TYPE, TABLE_MAX_COL } from '../../../../../../constants';

// Utils
import { handleError } from 'app/utils/handleError';
import {
  getTableMissingDataLabelDisplay,
  parseAlignItemsToVerticalAlign,
  sortListDataTableBOByPropertyName,
} from '../../../utils';
import { parseColWidth } from './utils';

interface TableBlockProps extends BlockProps {}

interface TCellContentResizeProps {
  children?: React.ReactNode;
  columns: TColumnTableBlock[];
  columnsWidth: number[];
  resizedColIndex: number;
  tableSize: Size;
  wrapText: boolean;
  padding: string | number;
  onResize?: (delta: NumberSize) => void;
  onResizeStop?: () => void;
  onResizeStart?: () => void;
  isPreviewMode?: boolean;
  style?: React.CSSProperties;
  as?: ResizableProps['as'];
}

type Size = {
  width: number;
  height: number;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/organisms/TableBlock/index.tsx';

const TableCellHeadResizable = ({
  children,
  columns,
  columnsWidth,
  resizedColIndex,
  onResize,
  onResizeStop,
  onResizeStart,
  style,
  wrapText = false,
  tableSize,
  isPreviewMode,
  padding,
  as,
}: TCellContentResizeProps) => {
  const colWidth = columnsWidth[resizedColIndex];

  const hasNextCol = columns[resizedColIndex + 1] !== undefined;
  const nextColWidth = hasNextCol ? columnsWidth[resizedColIndex + 1] : 0;

  const minWidth = tableSize.width * (1 / TABLE_MAX_COL);
  const maxWidth = colWidth + (hasNextCol ? nextColWidth - minWidth : 0);

  return (
    <Resizable
      style={{
        fontWeight: 'inherit',
        ...style,
      }}
      size={{
        width: colWidth,
        height: '100%',
      }}
      maxWidth={maxWidth}
      minWidth={minWidth}
      enable={{
        right: hasNextCol && !isPreviewMode,
      }}
      handleStyles={{
        right: {
          visibility: 'visible',
          height: tableSize.height,
        },
      }}
      onResize={(_even, _direction, _ref, delta) => {
        onResize && onResize(delta);
      }}
      onResizeStop={onResizeStop}
      onResizeStart={onResizeStart}
      as={as}
    >
      <div
        style={{
          padding,
          ...(wrapText
            ? {
                wordBreak: 'break-word',
              }
            : {
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                // 20: padding cell * 2,  2 margin cell * 2
                maxWidth: colWidth - 2,
              }),
        }}
      >
        {children}
      </div>
    </Resizable>
  );
};

export const TableBlock = (props: TableBlockProps) => {
  const dispatch = useDispatch();

  const { namespace, id, isPreviewMode } = props;

  const settings = props.settings as TTableSettings;
  const { table, tableHeader, tableBody, columns } = settings;

  const { updateBlockFieldsSelected, setSidePanel, updateBlockFieldsById } = mediaTemplateDesignActions;

  const [columnsOriginWidth, setColumnsOriginWidth] = useState<number[]>([]);
  const [columnsWidth, setColumnsWidth] = useState<number[]>(columnsOriginWidth);

  // On initial mount the ResizeObserver will take a little time to report on the actual size
  // Until the hook receives the first measurement, it returns undefined for width and height by default.
  const {
    ref: tableWrapperResizeObserverRef,
    width: tableWrapperWidth,
    height: tableHeight,
  } = useResizeObserver<HTMLDivElement>();

  const previousTableWidth = useRef<number | null>(null);

  const { isFetching: isLoadingGetListBo } = useGetListBO();

  const { data: dataTableBO, isFetching: isLoadingDataTableBo } = useGetDataTableBO({
    itemTypeId: settings.boId,
  });

  const sumColWidth = useMemo(() => sum(columnsOriginWidth), [columnsOriginWidth]);

  const sortedData = useMemo(
    () => sortListDataTableBOByPropertyName(dataTableBO?.data ?? [], settings.sortBy.columnId, settings.sortBy.order),
    [dataTableBO, settings.sortBy],
  );

  const updateColumnsWidthWhenTableElChange = useCallback(
    (oldWidth: number, newWidth: number) => {
      const dataUpdate: any[] = [];

      columns.forEach((col, index) => {
        const updatedWidth = (parseFloat(col.width) * newWidth) / oldWidth;

        if (!Number.isNaN(updatedWidth)) {
          dataUpdate.push({
            fieldPath: `settings.columns[${index}].width`,
            data: updatedWidth + 'px',
          });
        }
      });

      dispatch(
        updateBlockFieldsById({
          blockId: id,
          dataUpdate,
          ignoreUndoAction: true,
        }),
      );
    },
    [columns, dispatch, id, updateBlockFieldsById],
  );

  // When change desgin mode Desktop <=> Mobile component will be anmount,
  // previousTableWidth value will null => not updated columnsWidth to resize correctly
  useLayoutEffect(() => {
    if (!tableWrapperWidth) return;

    if (previousTableWidth.current === null) {
      updateColumnsWidthWhenTableElChange(sumColWidth, tableWrapperWidth);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableWrapperWidth]);

  // Re-calculate columnsWidth when tableWidth change by
  // outside wrapper block width, column width
  useLayoutEffect(() => {
    if (!tableWrapperWidth) return;

    const timeOut = setTimeout(() => {
      if (previousTableWidth.current !== null && previousTableWidth.current !== tableWrapperWidth) {
        updateColumnsWidthWhenTableElChange(previousTableWidth.current, tableWrapperWidth);
      }

      previousTableWidth.current = tableWrapperWidth;
    }, 200);

    return () => clearTimeout(timeOut);
  }, [updateColumnsWidthWhenTableElChange, tableWrapperWidth]);

  // Reset all column width in case exist column with width "NaNpx"
  useEffect(() => {
    if (tableWrapperWidth && columns.some(col => Number.isNaN(parseColWidth(col.width)))) {
      const updatedWidth = tableWrapperWidth / columns.length;

      dispatch(
        updateBlockFieldsById({
          blockId: id,
          dataUpdate: columns.map((_, index) => ({
            fieldPath: `settings.columns[${index}].width`,
            data: updatedWidth + 'px',
          })),
          ignoreUndoAction: true,
        }),
      );
    }
  }, [columns, tableWrapperWidth, dispatch, id, updateBlockFieldsById]);

  useLayoutEffect(() => {
    setColumnsWidth(columnsOriginWidth);
  }, [columnsOriginWidth]);

  useLayoutEffect(() => {
    setColumnsOriginWidth(columns.map(column => parseColWidth(column.width)));
  }, [columns]);

  const onColumnResizeHandle = useCallback(
    (delta: NumberSize, colIndex: number) => {
      try {
        setColumnsWidth(
          produce(columnsWidth, draft => {
            draft[colIndex] = columnsOriginWidth[colIndex] + delta.width;
            draft[colIndex + 1] = columnsOriginWidth[colIndex + 1] - delta.width;
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onColumnResizeHandle',
          args: {},
        });
      }
    },
    [columnsWidth, columnsOriginWidth],
  );

  const onColumnResizeStopHandle = useCallback(
    (resizedColIndex: number) => {
      try {
        dispatch(
          updateBlockFieldsSelected({
            dataUpdate: [
              {
                fieldPath: `settings.columns[${resizedColIndex}].width`,
                data: columnsWidth[resizedColIndex] + 'px',
              },
              {
                fieldPath: `settings.columns[${resizedColIndex + 1}].width`,
                data: columnsWidth[resizedColIndex + 1] + 'px',
              },
            ],
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onColumnResizeStopHandle',
          args: {},
        });
      }
    },
    [columnsWidth, dispatch, updateBlockFieldsSelected],
  );

  const tableHeaderContent = useMemo(() => {
    if (!tableWrapperWidth) return null;

    if (!!columnsWidth.length) {
      return (
        <thead
          style={{
            ...tableHeader.styles,
            ...(!tableHeader.settings.showHeader && {
              visibility: 'collapse',
            }),
          }}
        >
          <TableRow
            style={{
              backgroundColor: table.settings.headerBackground,
            }}
          >
            {columns.map((colHead, index) => (
              <TableCellHeadResizable
                key={colHead.id}
                resizedColIndex={index}
                columnsWidth={columnsWidth}
                columns={columns}
                tableSize={{ width: tableWrapperWidth, height: tableHeight ?? 0 }}
                onResize={delta => onColumnResizeHandle(delta, index)}
                onResizeStop={() => onColumnResizeStopHandle(index)}
                onResizeStart={() => {
                  dispatch(
                    setSidePanel({
                      blockSelectedId: id,
                      type: SIDE_PANEL_TYPE.TABLE.name,
                      activeTab: 'content',
                      activePanel: '',
                    }),
                  );
                }}
                isPreviewMode={isPreviewMode}
                padding={table.settings.cellPadding}
                style={{
                  verticalAlign: parseAlignItemsToVerticalAlign(table.settings.cellAlignItems),
                  textAlign: colHead.placement,
                }}
                wrapText={tableHeader.settings.wrapText}
                as="th"
              >
                {colHead.label}
              </TableCellHeadResizable>
            ))}
          </TableRow>
        </thead>
      );
    }

    return null;
  }, [
    isPreviewMode,
    id,
    tableHeight,
    tableWrapperWidth,
    columns,
    tableHeader,
    table.settings,
    onColumnResizeHandle,
    columnsWidth,
    onColumnResizeStopHandle,
    dispatch,
    setSidePanel,
  ]);

  const tableBodyContent = useMemo(() => {
    const rowBody: React.ReactNode[] = [];
    const cellMissingDisplay = getTableMissingDataLabelDisplay(tableBody.settings.showMissingDataType);

    if (!sortedData.length || !columns.length) return null;

    for (let rowIndex = 0; rowIndex < table.settings.showTop && rowIndex < sortedData.length; rowIndex++) {
      rowBody.push(
        <TableRow key={`row-${rowIndex + 1}`}>
          {columns.map((col, colIndex) => {
            let cellText = sortedData[rowIndex][col.value];

            if (col.colType === 'index') cellText = rowIndex + 1;

            if (!isNumber(cellText) && !cellText) {
              cellText = cellMissingDisplay;
            }

            return (
              <td
                key={col.id}
                style={{
                  verticalAlign: parseAlignItemsToVerticalAlign(table.settings.cellAlignItems),
                  ...(col.placement && {
                    textAlign: col.placement,
                  }),
                }}
              >
                <div
                  style={{
                    padding: table.settings.cellPadding,
                    ...(tableBody.settings.wrapText
                      ? {
                          wordBreak: 'break-word',
                        }
                      : {
                          maxWidth: columnsWidth[colIndex] - 2,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }),
                  }}
                >
                  {cellText}
                </div>
              </td>
            );
          })}
        </TableRow>,
      );
    }

    return (
      <TableBody {...tableBody.settings} style={tableBody.styles}>
        {rowBody}
      </TableBody>
    );
  }, [table.settings, columns, tableBody, columnsWidth, sortedData]);

  return (
    <TableBlockWrapper
      className={`${namespace}-te-content ${namespace}-${settings.component}--content table-block__wrapper`}
      style={{
        width: table.styles.width,
      }}
      ref={tableWrapperResizeObserverRef}
    >
      {isLoadingDataTableBo || isLoadingGetListBo || !columns?.length ? (
        <Skeleton
          paragraph={{
            width: '100%',
            rows: table.settings.showTop,
          }}
          active
        />
      ) : (
        <Table
          {...table.settings}
          cellPadding={0}
          className={classnames({
            'design-mode': !isPreviewMode,
          })}
          style={omit(table.styles, ['width'])}
        >
          {tableHeaderContent}
          {tableBodyContent}
        </Table>
      )}
    </TableBlockWrapper>
  );
};
