// Libraries
import React, { useState, useEffect, ReactNode, createElement, memo, useCallback, useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

// Atoms
import { Alert, Button, Divider, Input, Space, Spin, Switch, Text } from 'app/components/atoms';
import { LoadingOutlined } from '@ant-design/icons';

// Locales
import { translations } from 'locales/translations';

// Molecules
import { InputSearch, Modal, Select, SliderWithUnit, message } from 'app/components/molecules';
import { DynamicSetting } from '../../molecules/DynamicSetting';
import { SliderWithInputNumber } from 'app/components/molecules/SliderWithInputNumber';
import {
  AlignSetting,
  SettingWrapper,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules';
import TicketApp from 'app/components/molecules/TicketApp';

// Types
import { TAlign, TStylesSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { LAYOUT_TEMPLATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

//Util
import { handleError } from 'app/utils/handleError';
import { isEmail } from 'app/utils/common';

//Selector
import {
  selectCurrentViewPage,
  selectTemplate,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import { BUTTON_ACTION, BUTTON_SIZE, TO_FONT_SIZE, serialData, mapCurrentPage, getNextView } from './constants';
import { REGEX } from 'constants/regex';
import { APP_CONFIG } from 'constants/appConfig';

// services
import {
  createProvider,
  deleteProvider,
  getListProvider,
  updateProvider,
} from 'app/services/MediaTemplateDesign/ProviderInfo';
import * as networkInfoServices from 'app/services/Network';

// styled
import { StyledDrawer, WrapperContent, WrapperContentProvider, WrapperDynamicField, WrapperNoProvider } from './styled';

// hooks
import { useDebounce, useDeepCompareEffect } from 'app/hooks';
import { ListBlockSquare } from '../../../../../molecules/ListBlockSquare';
import { handleValidateData, initDefaultDataConfig, mapDataToApi, resetValidateField } from './utils';
import { Row } from 'antd';
import { useFetchFieldVendor } from './useFetchFieldVendor';
import { get } from 'lodash';

// Img
import ServiceProviderImg from 'assets/images/icons/service-provider.png';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/ButtonGroupSetting/index.tsx';
interface ButtonGroupSettingProps {
  buttonSettings: any;
  buttonStyles: any;
  ButtonElement: any;
  blockStyles: any;
  dynamicSetting?: any;
  onChange: (
    styleSettings: TStylesSettings,
    styles: React.CSSProperties,
    elementOptions: any,
    blockStyles: any,
    element: any,
    dynamicSetting?: any,
    ignoreUndoAction?: boolean,
  ) => void;
  excludeActions?: string[];
}

const initStateProvider = () => ({
  listProvider: [],
  searchProvider: '',
  openListProvider: false,
  isOpenContentProvider: false,
  isOpenDeleteProvider: false,
  isLoadingListProvider: false,
});

const InputComponent = memo((props: any) => (
  <Row className="ants-flex ants-items-center">
    {typeof props.componentEl === 'function' ? props.componentEl(props) : null}
  </Row>
));

export const ButtonGroupSetting: React.FC<ButtonGroupSettingProps> = props => {
  // Props
  const {
    buttonSettings,
    onChange,
    buttonStyles,
    ButtonElement,
    blockStyles,
    excludeActions = [],
    dynamicSetting = {},
  } = props;

  // Selector
  const currentViewPage = useSelector(selectCurrentViewPage);
  const mediaTemplate = useSelector(selectTemplate);

  // State
  const [isShowMessage, setShowMessage] = useState(false);

  const [stateProvider, setStateProvider] = useState<any>(initStateProvider());

  const [{ data }, { setData }] = useFetchFieldVendor();

  const {
    listProvider,
    searchProvider,
    openListProvider,
    isOpenContentProvider,
    isOpenDeleteProvider,
    isLoadingListProvider,
    isLoadingContentProvider,
  } = stateProvider;

  // debounce
  const debounceSearchProvider = useDebounce(searchProvider, 500);
  //I18n
  const { t } = useTranslation();

  // Memos
  const goToViewSelections = useMemo(() => {
    return Object.values(mapCurrentPage(currentViewPage));
  }, [currentViewPage]);

  // Effect

  useEffect(() => {
    const listActions = Object.values(BUTTON_ACTION).filter(item => !excludeActions.includes(item.value));
    if (!listActions.some(item => item.value === ButtonElement.type) && ButtonElement.type !== '') {
      onUpdateButtonSettings({ element: { type: listActions[0].value }, ignoreUndoAction: true });
    }
  }, [excludeActions]);

  useEffect(() => {
    if (!goToViewSelections.some(option => option.value === ButtonElement.options.name)) {
      onUpdateButtonSettings({
        elementOptions: { name: getNextView(currentViewPage?.id || '') },
        ignoreUndoAction: true,
      });
    }
  }, [goToViewSelections]);

  useEffect(() => {
    if (ButtonElement.type === BUTTON_ACTION.SEND_OTP.value) {
      let isUpdateProvider = true;
      if (ButtonElement.providerInfo) {
        isUpdateProvider = false;
      }
      handleFetchListProvider(isUpdateProvider);
    }
  }, [ButtonElement.type]);

  const handleFetchListProvider = (isUpdateNewProvider: boolean = false) => {
    updateStateProvider({ isLoadingListProvider: true });
    getListProvider({ columns: ['catalog_id', 'destination_setting', 'logo_url'] }).then(res => {
      if (res.code === 200) {
        const providers = res.data.body.map((provider: any) => ({
          ...provider,
          value: provider.destinationId,
          label: provider.destinationName,
          imageUrl: provider.logoUrl || '',
        }));
        updateStateProvider({ listProvider: providers || [] });
        if (providers.length && isUpdateNewProvider) {
          const currentProvider = providers[0];
          onUpdateButtonSettings({ element: { providerInfo: currentProvider } });
        }
      }
      updateStateProvider({ isLoadingListProvider: false });
    });
  };

  const updateStateProvider = (newState: any) => {
    if (typeof newState === 'object') {
      Object.keys(newState).forEach(key => {
        setStateProvider(prev => ({
          ...prev,
          [key]: newState[key],
        }));
      });
    }
  };

  //OnChange
  const onUpdateButtonSettings = ({
    settings = {},
    styles = {},
    elementOptions = {},
    newBlockStyles = {},
    element = {},
    dynamicSetting = {},
    ignoreUndoAction = false,
  }) => {
    try {
      // Callback onChange
      onChange(
        {
          ...buttonSettings,
          ...settings,
        },

        {
          ...buttonStyles,
          ...styles,
        },

        {
          ...ButtonElement.options,
          ...elementOptions,
        },
        {
          ...blockStyles,
          ...newBlockStyles,
        },
        {
          ...ButtonElement,
          ...element,
        },
        {
          ...props.dynamicSetting,
          ...dynamicSetting,
        },
        ignoreUndoAction,
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  useEffect(() => {
    if (setData && typeof setData === 'function') {
      if (isOpenContentProvider) {
        setData(prev => ({
          ...prev,
          triggerFetchData: prev.triggerFetchData + 1,
        }));
      } else {
        setData(initDefaultDataConfig());
      }
    }
  }, [isOpenContentProvider]);

  useEffect(() => {
    if (data.isInitDone) {
      updateStateProvider({ isLoadingContentProvider: false });
    }
  }, [data.isInitDone]);

  const handleOnChangeField = name => value => {
    resetValidateField(data, setData);
    if (setData && typeof setData === 'function') {
      setData(prev => ({
        ...prev,
        [name]: {
          ...prev[name],
          value,
        },
      }));
    }
  };

  const handleDeleteProvider = () => {
    const providerSelected = data.blockProviderSelected;
    if (providerSelected && providerSelected.type === 'REMOVE') {
      // will delete
      deleteProvider({
        id: providerSelected.value.destinationId,
        status: 2,
      }).then(res => {
        if (res.code === 200) {
          message.success('Remove provider success.');
          let isReUpdateProvider = false;
          const destinationIdSelected = +providerSelected.value.destinationId;
          if (ButtonElement?.providerInfo && destinationIdSelected === +ButtonElement.providerInfo.destinationId) {
            isReUpdateProvider = true;
          }
          handleFetchListProvider(isReUpdateProvider);
        } else {
          message.error('Something went wrong.');
        }
      });
      updateStateProvider({ isOpenDeleteProvider: false });
    }
  };

  const renderCloseCampaign = () => {
    try {
      if (mediaTemplate.type !== LAYOUT_TEMPLATE.INLINE.name) {
        return (
          <SettingWrapper label={`${t(translations.closeCampaign.title)}?`} labelClassName="!ants-text-gray-4">
            <Switch
              checked={ButtonElement.options.close}
              onChange={checked => onUpdateButtonSettings({ elementOptions: { close: checked } })}
            ></Switch>
          </SettingWrapper>
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderCloseCampaign',
        args: {},
      });
    }
  };

  const renderTypeOfClickAction = () => {
    try {
      switch (ButtonElement.type) {
        case BUTTON_ACTION.GO_TO_VIEW.value:
          return (
            <Select
              label={t(translations.goView.title)}
              options={goToViewSelections}
              value={
                goToViewSelections.some(option => option.value === ButtonElement.options.name)
                  ? ButtonElement.options.name
                  : getNextView(currentViewPage?.id || '')
              }
              onChange={value => onUpdateButtonSettings({ elementOptions: { name: value } })}
            />
          );
        case BUTTON_ACTION.TO_URL.value:
          return (
            <DynamicSetting
              label={t(translations.redirectUrl.title)}
              settings={dynamicSetting.url}
              onChange={setting => {
                onUpdateButtonSettings({ dynamicSetting: { url: setting } });
              }}
              renderStatic={() => (
                <>
                  <Input
                    placeholder={t(translations.buttonText.placeholder)}
                    value={ButtonElement.options.url}
                    onChange={e => onUpdateButtonSettings({ elementOptions: { url: e.target.value } })}
                    onBlur={() => setShowMessage(true)}
                  />
                  {isShowMessage && !REGEX.HTTP_HTTPS.test(ButtonElement.options.url) && (
                    <Alert
                      type="warning"
                      className="ants-mt-2.5"
                      message={<Trans i18nKey={translations.redirectUrl.warning} />}
                    />
                  )}
                </>
              )}
            />
          );
        case BUTTON_ACTION.EMAIL_CLIENT.value:
          return (
            <Space size={5} direction="vertical">
              <Text className="!ants-text-gray-4">{t(translations.emaiTo.title)}</Text>
              <Input
                placeholder={t(translations.buttonText.placeholder)}
                value={ButtonElement.options.email}
                onChange={e => onUpdateButtonSettings({ elementOptions: { email: e.target.value } })}
                onBlur={() => setShowMessage(true)}
              />
              {isShowMessage && !isEmail(ButtonElement.options.email) && (
                <Alert type="error" className="ants-mt-2.5" message={<Trans i18nKey={translations.emaiTo.error} />} />
              )}
            </Space>
          );
        case BUTTON_ACTION.TO_CALL.value:
          return (
            <>
              <Space size={5} direction="vertical">
                <Text className="!ants-text-gray-4">{t(translations.phoneNumber.title)}</Text>
                <Input
                  placeholder={t(translations.buttonText.placeholder)}
                  value={ButtonElement.options.phone}
                  onChange={e => onUpdateButtonSettings({ elementOptions: { phone: e.target.value } })}
                />
              </Space>
              {renderCloseCampaign()}
            </>
          );
        case BUTTON_ACTION.TO_COPY_TEXT.value:
          return (
            <>
              <Space size={5} direction="vertical">
                <Text className="!ants-text-gray-4">{t(translations.clipBoard.title)}</Text>
                <Input
                  placeholder={t(translations.buttonText.placeholder)}
                  value={ButtonElement.options.copy}
                  onChange={e => onUpdateButtonSettings({ elementOptions: { copy: e.target.value } })}
                />
              </Space>
              {renderCloseCampaign()}
            </>
          );
        case BUTTON_ACTION.OPEN_WINDOW.value:
          return (
            <>
              <Space size={5} direction="vertical">
                <DynamicSetting
                  label={t(translations.newWindow.title)}
                  settings={dynamicSetting.url}
                  onChange={setting => {
                    onUpdateButtonSettings({ dynamicSetting: { url: setting } });
                  }}
                  renderStatic={() => (
                    <>
                      <Input
                        placeholder={t(translations.buttonText.placeholder)}
                        value={ButtonElement.options.url}
                        onChange={e => onUpdateButtonSettings({ elementOptions: { url: e.target.value } })}
                        onBlur={() => setShowMessage(true)}
                      />
                      {isShowMessage && !REGEX.HTTP_HTTPS.test(ButtonElement.options.url) && (
                        <Alert
                          type="warning"
                          className="ants-mt-2.5"
                          message={<Trans i18nKey={translations.redirectUrl.warning} />}
                        />
                      )}
                    </>
                  )}
                />
              </Space>
              {renderCloseCampaign()}
            </>
          );
        case BUTTON_ACTION.SEND_OTP.value: {
          const MAP_PROVIDER_OPTIONS = {};
          listProvider.forEach((provider: any) => {
            if (provider.label.includes(debounceSearchProvider)) {
              MAP_PROVIDER_OPTIONS[provider.value] = provider;
            }
          });
          return (
            <>
              <Select
                label={t(translations.selectProvider.title)}
                options={Object.values(MAP_PROVIDER_OPTIONS)}
                value={ButtonElement.providerInfo?.value}
                onChange={value => onUpdateButtonSettings({ element: { providerInfo: MAP_PROVIDER_OPTIONS[value] } })}
                dropdownRender={menu => renderContentDropdownProvider(menu)}
              />
              <Select
                label={t(translations.goView.title)}
                options={Object.values(mapCurrentPage(currentViewPage))}
                value={ButtonElement.options.name}
                onChange={value => onUpdateButtonSettings({ elementOptions: { name: value } })}
              />
            </>
          );
        }
        default:
          return null;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderTypeOfClickAction',
        args: {},
      });
    }
  };

  const renderContentDropdownProvider = (menu: ReactNode) => {
    return (
      <WrapperContentProvider>
        <InputSearch
          className="ants-w-100"
          onChange={e => updateStateProvider({ searchProvider: e.target.value })}
          placeholder={`${t(translations.search.title)}`}
        />
        {menu}
        <Divider className="!ants-my-0" />
        <Button className="ants-w-100" type="text" onClick={() => updateStateProvider({ openListProvider: true })}>
          {t(translations.serviceProviderManagement.title)}
        </Button>
      </WrapperContentProvider>
    );
  };

  const callbackComponent = (type, value) => {
    if (setData && typeof setData === 'function') {
      switch (type) {
        case 'CREATE':
        case 'EDIT': {
          updateStateProvider({
            isOpenContentProvider: true,
            isLoadingContentProvider: true,
          });
          setData(prev => ({
            ...prev,
            blockProviderSelected: {
              type,
              value,
            },
          }));
          break;
        }
        case 'REMOVE': {
          // console.log(type, value);
          updateStateProvider({ isOpenDeleteProvider: true });
          setData(prev => ({
            ...prev,
            blockProviderSelected: {
              type,
              value,
            },
          }));
          break;
        }
        default:
          break;
      }
    }
  };

  const handleSaveProvider = () => {
    const { isValid } = handleValidateData(data, setData);
    if (isValid) {
      const params = mapDataToApi(data);
      if (data.blockProviderSelected.type === 'EDIT') {
        const destinationId = get(data.blockProviderSelected, 'value.destinationId', '');
        updateProvider({ id: destinationId, ...params }).then(res => {
          if (res.code === 200) {
            handleFetchListProvider();
            message.success('Update provider success.');
            updateStateProvider({ isOpenContentProvider: false });
          } else {
            message.error('Something went wrong.');
          }
        });
      } else {
        createProvider(params)
          .then(res => {
            if (res.code === 200) {
              handleFetchListProvider();
              message.success('Create provider success.');
              updateStateProvider({ isOpenContentProvider: false });
            } else {
              message.error('Something went wrong.');
            }
          })
          .catch(error => {
            handleError(error, {
              path: PATH,
              name: 'handleSaveProvider',
              args: {},
            });
          });
      }
    }
  };

  const renderNoProvider = () => {
    const handleOpenTicket = async () => {
      const portalId = APP_CONFIG.NETWORK_ID.toString();
      const isOnDisplayIframe = window.self !== window.top;
      const { data: networkData } = await networkInfoServices.getList({ app_id: APP_CONFIG.NETWORK_ID });
      const networkInfo = get(networkData, 'data.networkInfo', {});

      const messageContent = {
        type: 'open_antsomi_help',
        helpType: 'ISSUE',
        helpInfo: {
          feature: isOnDisplayIframe ? 'customer_journey' : 'media_template',
          title: 'No service provider available',
          message: `I want to create a new OTP Service Provider but there is no available connectors. <br>Portal: ${networkInfo.networkName} - ${portalId}`,
          files: [],
          referenceUrl: '',
        },
        dataImage: {
          imageName: '',
          dataUrl: '',
        },
      };

      window.postMessage(messageContent);
    };
    return (
      <WrapperNoProvider>
        <img className="img-provider" src={ServiceProviderImg} alt="service-provider" />
        <WrapperContent>
          <div className="title-provider">{t(translations.noProvider.label.title)}</div>
          <div className="content-provider">
            <span>{t(translations.noProvider.content.title)} </span>
            <span className="text-click-here" onClick={handleOpenTicket}>
              {'here'}
            </span>
          </div>
        </WrapperContent>
        <TicketApp />
      </WrapperNoProvider>
    );
  };

  return (
    <Space size={20} direction="vertical">
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">{t(translations.buttonText.title)}</Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={buttonSettings.buttonValue}
          onAfterChange={value => onUpdateButtonSettings({ settings: { buttonValue: value } })}
        />
      </Space>
      <Select
        label={t(translations.buttonClickAction.title)}
        options={Object.values(BUTTON_ACTION).filter(item => !excludeActions.includes(item.value))}
        value={ButtonElement.type}
        onChange={value => {
          setShowMessage(false);
          onUpdateButtonSettings({ element: { type: value } });
        }}
      />

      {/* Render type per button action */}
      {renderTypeOfClickAction()}

      {/* <SettingWrapper label={t(translations.conversionTracking.title)} labelClassName="!text-gray-4">
        <Switch
          checked={ButtonElement.options.track}
          onChange={checked => onUpdateButtonSettings({ elementOptions: { track: checked } })}
        ></Switch>
      </SettingWrapper> */}
      <SliderWithUnit
        label={t(translations.width.title)}
        labelClassName="!ants-text-gray-4"
        unit={buttonSettings.widthSuffix || '%'}
        min={0}
        max={(buttonSettings.widthSuffix || '%') === 'px' ? 1000 : 100}
        value={parseFloat(buttonStyles.width)}
        onAfterChange={value =>
          onUpdateButtonSettings({
            styles: { width: `${value}${buttonSettings.widthSuffix || '%'}` },
          })
        }
        onChangeUnit={value => {
          const width = parseFloat(buttonStyles.width) || 100;
          let newWidth = '';

          newWidth = width + value;

          if (value === '%' && width > 100) {
            newWidth = '100%';
          }

          if (value === 'auto') {
            newWidth = 'auto';
          }

          onUpdateButtonSettings({
            styles: {
              width: newWidth,
            },
            settings: {
              widthSuffix: value,
            },
          });
        }}
      />
      <AlignSetting
        label={t(translations.align.title)}
        align={buttonStyles.textAlign as TAlign}
        onChange={value => onUpdateButtonSettings({ styles: { textAlign: value } })}
      />
      <Select
        label={t(translations.buttonSize.title)}
        options={Object.values(BUTTON_SIZE)}
        value={buttonSettings.buttonSize}
        onChange={value =>
          onUpdateButtonSettings({ settings: { buttonSize: value }, styles: TO_FONT_SIZE[serialData(value)] })
        }
      />
      <StyledDrawer
        width="85%"
        title={t(translations.serviceProviderManagement.title)}
        onClose={() => updateStateProvider({ openListProvider: false })}
        visible={openListProvider}
        footer={
          <Button type="default" onClick={() => updateStateProvider({ openListProvider: false })}>
            {t(translations.close.title)}
          </Button>
        }
      >
        <Spin spinning={isLoadingListProvider}>
          <ListBlockSquare
            createLabel={t(translations.addNewProvider.title)}
            data={listProvider}
            activeId={ButtonElement.providerInfo?.value}
            callback={(type?: string, value?: any) => callbackComponent(type, value)}
          />
        </Spin>
      </StyledDrawer>
      {isOpenContentProvider && (
        <StyledDrawer
          width="85%"
          title={
            data.blockProviderSelected?.type === 'EDIT'
              ? t('Edit Connection')
              : t(translations.addNewServiceProvider.title)
          }
          onClose={() => updateStateProvider({ isOpenContentProvider: false })}
          visible={isOpenContentProvider}
          footer={
            data.isEmptyServiceProvider ? null : (
              <div className="ants-flex ants-gap-3">
                <Button type="primary" onClick={handleSaveProvider}>
                  {t(translations.save.title)}
                </Button>
                <Button type="default" onClick={() => updateStateProvider({ isOpenContentProvider: false })}>
                  {t(translations.cancel.title)}
                </Button>
              </div>
            )
          }
        >
          {data.isEmptyServiceProvider ? (
            renderNoProvider()
          ) : (
            <Spin spinning={isLoadingContentProvider}>
              <WrapperDynamicField>
                {data.infoFields.map(field => (
                  <InputComponent {...data[field]} key={field} onChange={handleOnChangeField} />
                ))}
                {data.configFields.map(field => (
                  <InputComponent {...data[field]} key={field} onChange={handleOnChangeField} />
                ))}
                {data.moreInfoFields.map(field => (
                  <InputComponent {...data[field]} key={field} onChange={handleOnChangeField} />
                ))}
              </WrapperDynamicField>
            </Spin>
          )}
        </StyledDrawer>
      )}
      <Modal
        title="Delete Vendor"
        okText={t(translations.delete.title)}
        visible={isOpenDeleteProvider}
        onCancel={() => updateStateProvider({ isOpenDeleteProvider: false })}
        onOk={handleDeleteProvider}
      >
        {t(translations.warnDeleteProvider.title)}
      </Modal>
    </Space>
  );
};

ButtonGroupSetting.defaultProps = {
  dynamicSetting: {
    url: {
      isDynamic: false,
      field: '',
      index: 1,
    },
  },
};
