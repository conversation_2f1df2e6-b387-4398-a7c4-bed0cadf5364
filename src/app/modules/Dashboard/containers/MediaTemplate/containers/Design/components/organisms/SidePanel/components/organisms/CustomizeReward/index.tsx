// Libraries
import React, { useCallback, useMemo, useEffect } from 'react';
import produce from 'immer';
import { useTranslation } from 'react-i18next';
import { groupBy, omit, pick } from 'lodash';
import classnames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';

// Locales
import { translations } from 'locales/translations';

// Translations
import { getTranslateMessage } from 'utils/messages';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from 'app/hooks';

// Atoms
import { Button, Icon, Input, Switch } from 'app/components/atoms';

// Molecules
import { InputNumber, Modal, Select } from 'app/components/molecules';

// Styled
import { StyledCellWrapper, StyledTable } from './styled';

// Components
import SelectCouponCode from './components/SelectCouponCode';
import LimitSpinning, { TLimitSpinning } from './components/LimitSpinning';

// Types
import { TUpdateBlockFieldsSelectedPayload } from '../../../../../../slice/types';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Error Handler
import { handleError } from 'app/utils/handleError';

// Utils
import { getObjSafely, random, randomColor } from 'app/utils/common';
import {
  OBJECT_VIEWS_PAGES,
  calculateWinChance,
  generateInternalCode,
  getCappingLevelOptions,
  getFrequencyOptions,
  roundNumber,
  sortValidateSectionsResults,
  validateSections,
} from './utils';

// Queries
import { useGetListPromotionPool } from 'app/queries/PromotionPool';
import { useGetListPromotionCodeAttr } from 'app/queries/BusinessObject';

// Selectors
import {
  selectViewPageSelected,
  selectViewPages,
  selectWorkspaceErrors,
  selectWorkspaceWarnings,
} from '../../../../../../slice/selectors';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/CustomizeReward/index.tsx';

export type TSectionValidate = {
  id: string;
  key: keyof TWheelItem;
  msg: string;
  status: 'warning' | 'error';
};

type TOptions = {
  label: string;
  value?: string;
};

export interface TWheelItem {
  sectionId: string;
  label: string;
  backgroundColor: string;
  couponCode: string;
  canWin: boolean;
  pool: boolean;
  winChance: number;
  saved: boolean;
  couponCodeAttr: string;
  internalCode: string;
  limitSpinning: TLimitSpinning;
  cappingLevel: 'journey' | 'campaign' | 'variant' | null;
  frequency: 'this hour' | 'this day' | 'this week' | 'this month' | 'lifetime' | null;
  goToView: 'success' | 'yes no' | 'optin' | null;
  validateInfo: Omit<TSectionValidate, 'index'>[];
}

interface CustomizeRewardProps {
  blockSelected: any;
  sections: any;
  isShowSectionValidate?: boolean;
  setIsShowSectionValidate: Function;
  openCustomizeWheel: boolean;
  closeCustomizeWheel: Function;
  actionKey?: string;
}

const CustomizeReward = (props: CustomizeRewardProps) => {
  const {
    blockSelected,
    sections,
    isShowSectionValidate,
    setIsShowSectionValidate,
    openCustomizeWheel,
    closeCustomizeWheel,
    actionKey = 'CouponWheelElement',
  } = props;

  const dispatch = useDispatch();

  // Actions
  const { updateBlockFieldsSelected, setData } = mediaTemplateDesignActions;

  // I18n
  const { t } = useTranslation();

  // Queries
  const { data: promotionPools = { rows: [], total: 0 } } = useGetListPromotionPool();
  const { data: promotionAttrs = [] } = useGetListPromotionCodeAttr<any[]>();

  // Selectors
  const pageSelected = useSelector(selectViewPageSelected);
  const viewPages = useSelector(selectViewPages);
  const workspaceErrors = useSelector(selectWorkspaceErrors);
  const workspaceWarnings = useSelector(selectWorkspaceWarnings);

  useEffect(() => {
    if (promotionPools?.total) {
      dispatch(
        setData({
          promotionPool: promotionPools,
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotionPools]);

  useDeepCompareEffect(() => {
    if (sections.length) {
      const validateResult = validateSections(sections, {
        promotionPools: promotionPools.rows,
        promotionErrors: workspaceErrors?.promotionPool,
        promotionWarnings: workspaceWarnings?.promotionPool,
      });

      const validateById = groupBy(validateResult, 'id');

      const dataUpdate: TUpdateBlockFieldsSelectedPayload['dataUpdate'] = [];

      sections.forEach((s, idx) => {
        dataUpdate.push({
          fieldPath: `settings.sections[${idx}].validateInfo`,
          data: validateById[s.sectionId]
            ? sortValidateSectionsResults(validateById[s.sectionId]).map(sValidate => omit(sValidate, 'id'))
            : [],
        });
      });

      if (dataUpdate.length)
        dispatch(
          updateBlockFieldsSelected({
            dataUpdate,
            ignoreUndoAction: true,
          }),
        );

      if (validateResult.length === 0) {
        setIsShowSectionValidate(false);
      }
    }
  }, [
    sections.map(s => omit(s, 'validateInfo')),
    promotionPools.rows,
    dispatch,
    updateBlockFieldsSelected,
    workspaceErrors,
    workspaceWarnings,
  ]);

  // memos
  const memoOptionsViewPage = useMemo(() => {
    const options: TOptions[] = [];
    try {
      if (viewPages && !!viewPages.length && pageSelected) {
        viewPages.forEach(page => {
          if (page.id !== pageSelected) {
            options.push({
              value: getObjSafely(() => page.id),
              label: getObjSafely(() => page.title),
            });
          }
        });
      }
      return options;
    } catch (error) {
      return options;
    }
  }, [pageSelected, viewPages]);

  // Callbacks
  const onChangeTableBlock = useCallback(
    (type: string, value: any) => {
      try {
        const dataUpdate: TUpdateBlockFieldsSelectedPayload['dataUpdate'] = [];

        if (!blockSelected.settings.actions[actionKey]) {
          dataUpdate.push({
            fieldPath: 'settings.actions',
            data: {
              [actionKey]: {
                event: 'wheel',
                scripts: '',
                type: '',
                options: {
                  name: '',
                  track: false,
                  url: '',
                  pass: false,
                  close: false,
                  phone: '',
                  copy: '',
                  spinDuration: 1e4,
                },
              },
            },
          });
        }
        switch (type) {
          case 'sectionColors':
            dataUpdate.push(
              {
                fieldPath: 'settings.sections',
                data: blockSelected.settings.sections.map((item, idx) => ({
                  ...item,
                  backgroundColor: value[idx],
                })),
              },
              {
                fieldPath: 'settings.sectionColors',
                data: value,
              },
            );
            break;
          case 'sections':
            dataUpdate.push(
              { fieldPath: 'settings.sections', data: value },
              { fieldPath: 'settings.sectionColors', data: value.map(item => item.backgroundColor) },
            );
            break;
          case 'icon':
            dataUpdate.push(
              { fieldPath: 'settings.innerWheelIcon.icon', data: value },
              { fieldPath: 'settings.innerWheelImage.previewUrl', data: '' },
            );
            break;
          case 'image':
            dataUpdate.push(
              { fieldPath: 'settings.innerWheelImage.previewUrl', data: value.url },
              { fieldPath: 'settings.innerWheelIcon.icon', data: '' },
            );
            break;
          case 'iconColor':
            dataUpdate.push({ fieldPath: 'settings.innerWheelIcon.iconColor', data: value });
            break;
          case 'errorMessage':
            dataUpdate.push({
              fieldPath: 'settings.errorMessage',
              data: {
                ...(blockSelected.settings.errorMessage || {
                  position: 'bottom',
                  message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
                }),
                ...value,
              },
            });
            break;
        }
        if (dataUpdate.length) {
          dispatch(updateBlockFieldsSelected({ dataUpdate, ignoreUndoAction: false }));
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeIconColor',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockFieldsSelected],
  );

  const winChangeRemain = useDeepCompareMemo(() => {
    const sum = sections.reduce((result, cur) => (cur.winChance ? result + cur.winChance : result), 0);

    return roundNumber(100 - sum);
  }, [sections.map(s => pick(s, ['winChance']))]);

  const genWheelItem = (item: TWheelItem | null = null, idx: number) => {
    let randomLabel = `Section 1`;
    let randomInternalCode = `section_1`;

    let i = 1;
    // eslint-disable-next-line no-loop-func
    while (sections.some(section => section.label === randomLabel || section.internalCode === randomInternalCode)) {
      ++i;
      randomLabel = `Section ${i}`;
      randomInternalCode = `section_${i}`;
    }

    const sectionId = item?.sectionId || random(5);

    return {
      key: sectionId,
      sectionId,
      idx,
      no: `${idx + 1}`,
      saved: item?.saved ?? false,
      internalCode: item ? item.internalCode : randomInternalCode,
      label: item ? item.label || '' : randomLabel,
      backgroundColor: item ? item.backgroundColor : randomColor(),
      winChance: item ? +item.winChance || 0 : winChangeRemain,
      pool: item?.pool ?? true,
      couponCode: item ? item.couponCode || '' : '',
      couponCodeAttr: item ? item.couponCodeAttr || '' : '',
      delete: idx,
      limitSpinning: item?.limitSpinning || { type: 'out_of_code' },
      cappingLevel: item?.cappingLevel || null,
      frequency: item?.frequency || null,
      validateInfo: item?.validateInfo || [],
      goToView:
        item?.goToView === pageSelected || !item?.goToView ? OBJECT_VIEWS_PAGES[pageSelected || ''] : item?.goToView,
      canWin: true,
    };
  };

  const onChangeTable = (key: string, value: any, item: any) => {
    try {
      const { idx } = item;

      const newSections = produce(sections, draft => {
        const oldValueByKey = draft[idx][key];

        draft[idx][key] = key === 'winChance' ? roundNumber(value) : value;

        if (key === 'label' && !draft[idx]['saved']) {
          draft[idx]['internalCode'] = generateInternalCode(
            value,
            sections.map(s => s.internalCode),
          );
        }

        if (key === 'pool') {
          draft[idx]['couponCode'] = '';
          draft[idx]['limitSpinning'] = value ? { type: 'out_of_code' } : { type: 'unlimited' };
          draft[idx]['cappingLevel'] = null;
          draft[idx]['frequency'] = null;
          draft[idx]['couponCodeAttr'] = '';
        }

        if (key === 'couponCode') {
          draft[idx]['couponCode'] = value.pool;
          draft[idx]['couponCodeAttr'] = value.attr;
        }

        if (key === 'limitSpinning' && value?.type === 'set_value' && value?.type !== oldValueByKey.type) {
          draft[idx]['cappingLevel'] = 'journey';
          draft[idx]['frequency'] = 'lifetime';
        }
      });

      onChangeTableBlock('sections', newSections);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTable',
        args: {},
      });
    }
  };

  const columns: any[] = [
    {
      title: 'No.',
      dataIndex: 'no',
      key: 'no',
      width: '42px',
      className: 'no',
      render: value => (
        <StyledCellWrapper>
          <div className="no-label">{value}</div>
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Rewards',
      dataIndex: 'label',
      key: 'label',
      className: 'label',
      render: (value, item) => (
        <StyledCellWrapper>
          <Input
            value={value}
            maxLength={255}
            debounce={700}
            onAfterChange={value => onChangeTable('label', value, item)}
            disableUndo
          />
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Internal code',
      className: 'internal-code',
      dataIndex: 'internalCode',
      render: (value: string, item: ReturnType<typeof genWheelItem>) => {
        const helperValidateProps: Record<string, any> = {};
        const couponCodeValidate = item.validateInfo.filter(v => v.key === 'internalCode');

        if (couponCodeValidate.length) {
          helperValidateProps.status = couponCodeValidate[0].status;
          helperValidateProps.errorMsg = couponCodeValidate[0].msg;
        }

        return (
          <StyledCellWrapper>
            <Input
              value={value}
              disabled={!!item.saved}
              maxLength={255}
              onAfterChange={value => onChangeTable('internalCode', value, item)}
              debounce={700}
              required
              disableUndo
              {...helperValidateProps}
            />
          </StyledCellWrapper>
        );
      },
    },
    // {
    //   title: 'Can Win?',
    //   dataIndex: 'canWin',
    //   key: 'canWin',
    //   width: 75,
    //   className: 'can-win',
    //   render: (value, item) => (
    //     <StyledCellWrapper>
    //       <Switch className="switch-btn" checked={value} onChange={checked => onChangeTable('canWin', checked, item)} />
    //     </StyledCellWrapper>
    //   ),
    // },
    {
      title: 'Win Chance (%)',
      dataIndex: 'winChance',
      key: 'winChance',
      width: 110,
      className: 'win-chance',
      render: (value, item) => (
        <StyledCellWrapper>
          <div className="ants-flex ants-flex-row-reverse ants-gap-[7px] ants-items-center">
            <InputNumber
              value={value}
              className={'!ants-w-[60px]'}
              type="number"
              max={value + winChangeRemain}
              min={0}
              onChange={value => onChangeTable('winChance', value, item)}
              disableUndo
            />
            {/* {item.canWin ? '%' : null} */}
          </div>
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Pool',
      dataIndex: 'pool',
      key: 'pool',
      width: 60,
      className: 'pool',
      render: (value, item) => (
        <StyledCellWrapper>
          <Switch
            className="switch-btn"
            checked={value}
            disabled={item.winChance === 0}
            onChange={checked => onChangeTable('pool', checked, item)}
          />
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Coupon code',
      dataIndex: 'couponCode',
      key: 'couponCode',
      className: 'coupon-code',
      render: (_value: string, item: ReturnType<typeof genWheelItem>) => {
        const helperValidateProps: Record<string, any> = {};
        const couponCodeValidate = item.validateInfo.filter(v => v.key === 'couponCode' || v.key === 'couponCodeAttr');

        if (couponCodeValidate.length) {
          helperValidateProps.status = couponCodeValidate[0].status;
          helperValidateProps.helperMsg = couponCodeValidate[0].msg;

          // if (
          //   couponCodeValidate[0].msg ===
          //   getTranslateMessage(translations.messageError.promotionPoolDeactivated.message)
          // ) {
          //   helperValidateProps.forceShowHelperMsg = true;
          // }
          if (couponCodeValidate[0].msg) {
            helperValidateProps.forceShowHelperMsg = true;
          }
        }

        return (
          <StyledCellWrapper>
            <SelectCouponCode
              usePool={item.pool}
              promotionAttrs={promotionAttrs}
              promotionPools={promotionPools.rows}
              poolValue={item.couponCode}
              attrValue={item.couponCodeAttr}
              onChange={values => {
                onChangeTable('couponCode', values, item);
              }}
              disabled={item.winChance === 0}
              forceShowHelperMsg={isShowSectionValidate}
              {...helperValidateProps}
            />
          </StyledCellWrapper>
        );
      },
    },
    {
      title: t(translations.limitRandom.title),
      dataIndex: 'limitSpinning',
      key: 'limitSpinning',
      className: 'limit-spinning',
      width: 200,
      render: (value, item: ReturnType<typeof genWheelItem>) => (
        <StyledCellWrapper>
          <LimitSpinning
            usePool={item.pool}
            values={item.limitSpinning}
            showOptions={item.pool ? ['set_value', 'out_of_code'] : ['unlimited', 'set_value']}
            onChange={values => onChangeTable('limitSpinning', values, item)}
            disabled={item.winChance === 0}
          />
        </StyledCellWrapper>
      ),
    },
    {
      title: t(translations.cappingLevel.title),
      dataIndex: 'cappingLevel',
      key: 'cappingLevel',
      className: 'capping-level',
      render: (value, item: ReturnType<typeof genWheelItem>) => {
        const options = getCappingLevelOptions();

        return (
          <StyledCellWrapper>
            {item.limitSpinning.type === 'set_value' ? (
              <Select
                options={options}
                value={item.cappingLevel}
                onChange={value => onChangeTable('cappingLevel', value, item)}
                disabled={item.winChance === 0}
              />
            ) : (
              <Input disabled />
            )}
          </StyledCellWrapper>
        );
      },
    },
    {
      title: t(translations.frequency.title),
      dataIndex: 'frequency',
      key: 'frequency',
      className: 'frequency',
      render: (value, item: ReturnType<typeof genWheelItem>) => {
        const options = getFrequencyOptions();

        return (
          <StyledCellWrapper>
            {item.limitSpinning.type === 'set_value' ? (
              <Select
                options={options}
                value={item.frequency}
                onChange={value => onChangeTable('frequency', value, item)}
                disabled={item.winChance === 0}
              />
            ) : (
              <Input disabled />
            )}
          </StyledCellWrapper>
        );
      },
    },
    {
      title: t(translations.goToView.title),
      dataIndex: 'goToView',
      key: 'goToView',
      className: 'goToView',
      render: (value, item: ReturnType<typeof genWheelItem>) => {
        return (
          <StyledCellWrapper>
            <Select
              options={memoOptionsViewPage}
              value={item.goToView}
              onChange={value => onChangeTable('goToView', value, item)}
            />
          </StyledCellWrapper>
        );
      },
    },
    {
      title: '',
      dataIndex: 'delete',
      key: 'delete',
      className: 'delete',
      render: (value, item) => (
        <StyledCellWrapper>
          <Icon
            disabled={sections.length <= 2}
            onClick={
              sections.length > 2
                ? () => {
                    const newSections = [...sections];
                    newSections.splice(value, 1);
                    onChangeTableBlock('sections', calculateWinChance(newSections));
                  }
                : undefined
            }
            type="icon-ants-outline-delete"
            className="icon-delete-section ants-block ants-text-primary ants-cursor-pointer"
            size={20}
          />
        </StyledCellWrapper>
      ),
      width: 55,
    },
  ];

  const addSection = () => {
    const newSections = calculateWinChance([...sections, genWheelItem(null, sections.length)]);
    onChangeTableBlock('sections', newSections);
  };

  const dataSource = sections.map((item, idx) => genWheelItem(item, idx));

  const footer = () => (
    <div className="ants-flex ants-justify-between ants-bg-white ants-w-100">
      <Button type="text" onClick={addSection}>
        + {t(translations.addReward.title)}
      </Button>
    </div>
  );

  return (
    <>
      <Modal
        wrapClassName="icons-selection-modal"
        title={<div className="ants-flex ants-gap-2 ants-items-center">{t(translations.customizeGiftBox.title)}</div>}
        headerStyle={{
          padding: '20px 20px 0',
          border: 'none',
        }}
        bodyStyle={{
          padding: '20px 0 0',
        }}
        width="min(90%, 1300px)"
        footer={null}
        // getContainer={}
        visible={openCustomizeWheel}
        onCancel={closeCustomizeWheel as any}
        destroyOnClose
      >
        <StyledTable
          className="ants-w-full"
          bordered
          footer={footer}
          pagination={false}
          scroll={{ y: 350, x: 1150 }}
          dataSource={dataSource}
          columns={columns}
          rowClassName={(record: ReturnType<typeof genWheelItem>, index) =>
            classnames({
              'use-pool': record.pool,
              'coupon-code-has-value': record.couponCode || record.couponCodeAttr,
              'limit-spinning-set-value': record.limitSpinning.type === 'set_value',
            })
          }
        />
      </Modal>
    </>
  );
};

export default CustomizeReward;
