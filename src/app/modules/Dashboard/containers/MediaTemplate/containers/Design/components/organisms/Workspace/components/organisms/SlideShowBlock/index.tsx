// Libraries
import React, { useEffect, useState } from 'react';
import {
  Navigation,
  Autoplay,
  EffectFade,
  EffectCube,
  EffectCoverflow,
  EffectFlip,
  EffectCards,
  EffectCreative,
  Mousewheel,
} from 'swiper';
import { SwiperSlide } from 'swiper/react';
import classNames from 'classnames';
import { useSelector } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

// Swiper css
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import 'swiper/css/effect-fade';
import 'swiper/css/effect-cube';
import 'swiper/css/effect-coverflow';
import 'swiper/css/effect-flip';
import 'swiper/css/effect-cards';
import 'swiper/css/effect-creative';

// Molecules
import BlockWrapper from '../../molecules/BlockWrapper';
import { DragBlockHere } from '../../molecules/DragBlockHere';

// Organisms
import {
  GroupBlock,
  TextBlock,
  ImageBlock,
  ButtonBlock,
  CountdownBlock,
  CouponWheelBlock,
  DividerBlock,
  HtmlBlock,
  IconsBlock,
  OptinFieldsBlock,
  SpacerBlock,
  VideoBlock,
  YesNoBlock,
  RatingBlock,
  TableBlock,
  TableBlockExport,
} from '..';

// Styled
import { StyledSlideBlock, SlideShowBlockWrapper, StyledSlide, SwiperSlideContent } from './styled';

// Hooks
import { useDeepCompareMemo } from 'app/hooks';

// Types
import {
  BlockProps,
  TSettings,
  TSlideShowSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Slice
import {
  selectChildrenBlockById,
  selectCurrentBlocks,
  selectCurrentTree,
  selectNamespace,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import {
  PREFIX_EL_NAME,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Utils
import { handleError } from 'app/utils/handleError';
import { generateBlockContentStyle, getDisplayConditionIndex, MAP_DISPLAY_CONDITION } from '../../../utils';
import { buildDisplayConditionData } from '../../../../SidePanel/components/organisms/DisplayCondition/utils';

// Queries
import { getAllChildrenBlockId, getDataTrackingItem } from '../../../../../../slice/utils';

interface SlideShowBlockProps extends BlockProps {}

interface SlideBlockProps extends BlockProps {
  displayItems: number;
  countNextSlide: number;
  slideShowSettings: TSettings<TSlideShowSettings>;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/organisms/SlideShowBlock/index.tsx';

export const SlideBlock: React.FC<SlideBlockProps> = props => {
  // Payload
  const { isPreviewMode, id: slideId, settings: slideSettings, slideShowSettings } = props;

  // Selectors
  const namespace = useSelector(selectNamespace);
  const elements = useSelector(selectChildrenBlockById(slideId));
  const tree = useSelector(selectCurrentTree);

  // Memos
  const dataTrackingItem = useDeepCompareMemo(() => {
    return getDataTrackingItem({ blocks: elements });
  }, [elements]);

  // Variables
  const isEmpty = !elements.length;

  const renderElement = ({ id, type, settings, savedBlockId, slideId, idx }) => {
    try {
      const elementProps = {
        parentId: slideId,
        id,
        type,
        settings,
        idx,
        isPreviewMode,
        namespace,
      };

      const settingDC = settings.blockStylesSettings.displayCondition || {};

      const renderContent = () => {
        switch (type) {
          case STANDARDS_BLOCKS.TEXT.name:
            return <TextBlock {...elementProps} />;

          case STANDARDS_BLOCKS.IMAGE.name:
            return <ImageBlock {...elementProps} />;

          case STANDARDS_BLOCKS.BUTTON.name:
            return <ButtonBlock {...elementProps} />;

          case STANDARDS_BLOCKS.COUNT_DOWN.name:
            return <CountdownBlock {...elementProps} />;

          case STANDARDS_BLOCKS.COUPON_WHEEL.name:
            return <CouponWheelBlock {...elementProps} />;

          case STANDARDS_BLOCKS.DIVIDER.name:
            return <DividerBlock {...elementProps} />;

          case STANDARDS_BLOCKS.HTML.name:
            return <HtmlBlock {...elementProps} />;

          case STANDARDS_BLOCKS.ICON.name:
            return <IconsBlock {...elementProps} />;

          case STANDARDS_BLOCKS.OPTIN_FIELDS.name:
            return <OptinFieldsBlock {...elementProps} />;

          case STANDARDS_BLOCKS.SPACER.name:
            return <SpacerBlock {...elementProps} />;

          case STANDARDS_BLOCKS.VIDEO.name:
            return <VideoBlock {...elementProps} />;

          case STANDARDS_BLOCKS.YES_NO.name:
            return <YesNoBlock {...elementProps} />;

          case STANDARDS_BLOCKS.RATING.name:
            return <RatingBlock {...elementProps} />;

          case STANDARDS_BLOCKS.GROUP.name:
            return <GroupBlock {...elementProps} />;

          case STANDARDS_BLOCKS.TABLE.name:
            return isPreviewMode ? <TableBlockExport {...elementProps} /> : <TableBlock {...elementProps} />;

          default:
            break;
        }
      };

      return (
        <div
          className={`${namespace}-element ${namespace}-el-${idx + 1} Element ${PREFIX_EL_NAME}-element ants-m-0.5`}
          {...buildDisplayConditionData(settingDC)}
        >
          <BlockWrapper
            {...{
              ...elementProps,
              savedBlockId,
              draggableId: `${type}-${id}`,
              isPortal: tree[id]?.length != null ? false : true,
            }}
          >
            {renderContent()}
          </BlockWrapper>
        </div>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  return (
    <SwiperSlideContent
      id={
        !!slideShowSettings.slideStylesSettings.customId
          ? slideShowSettings.slideStylesSettings.customId
          : `${namespace}-${slideSettings.component || ''}--wrapper--${slideId}`
      }
      className={slideShowSettings.slideStylesSettings?.customClass}
      style={{
        ...generateBlockContentStyle(slideShowSettings.slideStyles),
        height: `100%`,
      }}
      hoverStyle={
        slideShowSettings.slideHoverStylesSettings?.editHover
          ? generateBlockContentStyle(slideShowSettings.slideHoverStyles)
          : {}
      }
      {...(!!dataTrackingItem &&
        typeof dataTrackingItem === 'object' && {
          'data-tracking-item': JSON.stringify(dataTrackingItem),
        })}
    >
      <StyledSlideBlock
        className={classNames(`Slide__content`, {
          '!ants-border-none': isPreviewMode,
        })}
      >
        {isEmpty ? (
          !isPreviewMode && <DragBlockHere blockId={slideId} blockType={'slide'} />
        ) : (
          <>
            {elements.map((element, idx) => {
              const { id, type, settings, savedBlockId } = element;

              return (
                <React.Fragment key={id + idx}>
                  {renderElement({
                    id,
                    type,
                    settings,
                    savedBlockId,
                    slideId,
                    idx,
                  })}
                </React.Fragment>
              );
            })}
          </>
        )}
      </StyledSlideBlock>
    </SwiperSlideContent>
  );
};

export const SlideShowBlock: React.FC<SlideShowBlockProps> = props => {
  // State
  const [countNextSlide, setCountNextSlide] = useState(0);

  // Props
  const { id: slideShowId, isPreviewMode, settings } = props;
  const {
    displayItems,
    slideDuration,
    skipItems,
    slideDirection,
    displayStyle,
    columnGap,
    navigationSettings,
    slideTransition,
    navigationStyles,
    slideLoop,
    blockStylesSettings,
  } = settings || {};
  const displayCondition = blockStylesSettings.displayCondition || {};

  // Selectors
  const namespace = useSelector(selectNamespace);
  const blocks = useSelector(selectCurrentBlocks);
  const tree = useSelector(selectCurrentTree);
  const slides = useSelector(selectChildrenBlockById(slideShowId));

  // State
  const [slideHeight, setSlideHeight] = useState(0);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const slideEls = document.querySelectorAll(`#swiper-${slideShowId} .swiper-slide`);

    setTimeout(() => {
      const slideHeightArray = Array.from(slideEls).map(slideEl => {
        return slideEl.clientHeight;
      });

      if (slideEls) {
        setSlideHeight(Math.max(...slideHeightArray));
      }
    });
  });

  const memoizedDisplayConditions = useDeepCompareMemo<any[]>(() => {
    const draftDisplayConditions: any[] = [];

    slides.forEach((slide, idx) => {
      const childrenBlockIds = getAllChildrenBlockId({ tree, blockId: slide.id });
      const childrenBlocks = childrenBlockIds.map(id => blocks[id] || {});

      const displayConditionIndex = getDisplayConditionIndex({
        blocks: childrenBlocks,
        fieldSelected: displayCondition.field,
        isHasNoIndex: true,
      });

      draftDisplayConditions.push(
        buildDisplayConditionData({
          ...displayCondition,
          ...(typeof displayConditionIndex === 'boolean' && displayConditionIndex
            ? { isHasNoIndex: displayConditionIndex }
            : { index: displayConditionIndex }),
          condition: MAP_DISPLAY_CONDITION[displayCondition.condition || ''] || '',
        }),
      );
    });

    return draftDisplayConditions;
  }, [displayCondition, blocks]);

  return (
    <SlideShowBlockWrapper
      id={`${namespace}-${STANDARDS_BLOCKS.SLIDE_SHOW.actionKey}--${slideShowId}`}
      navigationSettings={navigationSettings}
      navigationStyles={navigationStyles}
      slideDirection={slideDirection}
      isPreviewMode={isPreviewMode}
    >
      <StyledSlide
        id={`swiper-${slideShowId}`}
        style={{
          ...(slideDirection === 'vertical' && { height: slideHeight * displayItems + columnGap * (displayItems - 1) }),
        }}
        modules={[
          Mousewheel,
          Navigation,
          Autoplay,
          EffectFade,
          EffectCube,
          EffectCoverflow,
          EffectFlip,
          EffectCards,
          EffectCreative,
        ]}
        // navigation
        direction={slideDirection}
        navigation={{
          disabledClass: 'custom-button-disabled',
          nextEl: `.button-next-${slideShowId}`,
          prevEl: `.button-prev-${slideShowId}`,
        }}
        grabCursor={true}
        cubeEffect={{
          shadow: true,
          slideShadows: true,
          shadowOffset: 20,
          shadowScale: 0.94,
        }}
        creativeEffect={{
          prev: {
            shadow: true,
            translate: [0, 0, -400],
          },
          next: {
            translate: ['100%', 0, 0],
          },
        }}
        // effect={slideTransition}
        simulateTouch={isPreviewMode}
        spaceBetween={columnGap}
        slidesPerGroup={skipItems}
        // autoplay={{ delay: autoSlide ? slideDelay * 1000 : 100000000 }}
        speed={slideDuration * 1000}
        loop={isPreviewMode ? slideLoop : false}
        centeredSlides={displayStyle === 'full' || !['slide', 'coverflow'].includes(slideTransition) ? false : true}
        slidesPerView={!['slide', 'coverflow'].includes(slideTransition) ? 1 : displayItems}
        mousewheel={{
          forceToAxis: true,
        }}
        onSlideChange={payload => {
          setCountNextSlide(payload.realIndex);
        }}
      >
        {slides.map((slide, index) => {
          const { id: slideId, settings: slideSettings } = slide;

          if (slideSettings.blockStylesSettings?.hidden) return null;

          return (
            <SwiperSlide key={slideId} {...memoizedDisplayConditions[index]} data-slide-idx={index}>
              <SlideBlock
                key={slideId}
                isPreviewMode={isPreviewMode}
                parentId={slideShowId}
                id={slideId}
                idx={index}
                settings={slideSettings}
                slideShowSettings={settings}
                type={'slide'}
                displayItems={displayItems}
                countNextSlide={countNextSlide}
              />
            </SwiperSlide>
          );
        })}
      </StyledSlide>
      <div className={`custom-button-next button-next-${slideShowId}`}>
        <FontAwesomeIcon
          icon={['fas', slideDirection === 'vertical' ? 'angle-down' : 'angle-right']}
          style={{
            fontSize: navigationStyles.fontSize,
            height: navigationStyles.fontSize,
            width: navigationStyles.fontSize,
          }}
        />
      </div>
      <div className={`custom-button-prev button-prev-${slideShowId}`}>
        <FontAwesomeIcon
          icon={['fas', slideDirection === 'vertical' ? 'angle-up' : 'angle-left']}
          style={{
            color: navigationStyles.color,
            fontSize: navigationStyles.fontSize,
            height: navigationStyles.fontSize,
            width: navigationStyles.fontSize,
          }}
        />
      </div>
    </SlideShowBlockWrapper>
  );
};
