// Libraries
import React, { Fragment, useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { omit, isEmpty, flatMapDeep, set, get, isNaN } from 'lodash';
import classnames from 'classnames';
import { AutoComplete } from 'antd';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from 'app/hooks';

// Components
import { TreeNode } from 'rc-tree-select';
import { Button, Checkbox, Text } from 'app/components/atoms';
import { Form, Modal, Select, TreeSelect } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Query
import {
  useGetListEventAttr,
  useGetListPromotionCodeAttr,
  useGetListBO,
  useGetListAllEvents,
  useGetListSourceByEvent,
} from 'app/queries/BusinessObject';
import { useGetDynamicContentAttr } from 'app/queries/DynamicContentAttribute';
import { useGetListPromotionPool } from 'app/queries/PromotionPool';

// Selectors
import {
  selectContentSources,
  selectJourneySettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Types
import { TNumberFormatSettings } from './components/FormatNumber';
import { TDataType } from '../../../../../../types';
import { dateExample, TDatetimeFormatSetting } from './components/FormatDatetime';
import DisplayFormat, { TDisplayFormat } from './components/DisplayFormat';

// Constants
import {
  DYNAMIC_CONTENT_SETTING_KEY,
  DYNAMIC_CONTENT_TYPE,
  DYNAMIC_CONTENT_ATTR_DF_TYPE,
  DATA_TYPE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { ATTRIBUTE_TYPE } from 'constants/variables';

// Utils
import { handleError } from 'app/utils/handleError';
import {
  formatDatetimeDF,
  formatNumberDF,
  getAvailableAttrs,
  getStringFormatDateDF,
  regexCSType,
  serilizeBOAttr,
  serializeCSSelectedString,
  serilizeDynamicContentAttr,
  serilizeEventAttr,
  serilizePromotionPool,
  CUSTOM_TYPE,
} from './utils';
import { isCheckStatusAttr } from '../../molecules/DynamicSetting/constants';
import {
  buildOptionAttrArchive,
  checkStatusAttr,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';
import { useGetListAttributeBO } from 'app/queries/BusinessObject/useGetListAttributeBO';
import { useGetEventTrackingAttributes } from 'app/queries/ThirdParty';
import { buildMappingFields } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import { queryClient } from 'index';
import { QUERY_KEYS } from 'constants/queries';
// Style
import { InputSuggestion, ModalCustom } from './styled';
import { EditorScript } from 'app/components/molecules/EditorScript';

// Hooks queries
import { useGetCustomFunction, useAddSavedCSFunction, useUpdateCSFunction } from 'app/queries/CustomFunction';
import { serializeLabelToCode } from 'app/utils/common';
export interface LabeledValue {
  label: string;
  value: string;
  dataType?: TDataType;
  disabled?: boolean;
  status?: any;
}
export interface LabeledTreeValue extends LabeledValue {
  [key: string]: any;
  children: LabeledTreeValue[];
  disableCheckbox?: boolean;
  selectable?: boolean;
  checkable?: boolean;
  type: number | null;
  status?: any;
}

interface AddDynamicContentProps {
  defaultDynamicIndex?: number;
  journeySettings: Record<string, any>;
  contentSources: Record<string, any>;
  additionalAttrProperties?: string[];
  defaultData?: Record<string, any>;
  visible: boolean;
  showDisplayFormat?: boolean;
  showIndex?: boolean;
  onCancel?: () => void;
  onOk?: (value: Record<string, any>) => void;
  modalTitle?: string;
  isShowCustomFunction?: boolean;
}

const getMembers = (member: any) => {
  if (!member.children || !member.children.length) {
    return member;
  }

  return [member, member.children];
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/AddDynamicLink/index.tsx';

const initNumberDFSettings: TNumberFormatSettings = {
  grouping: ',',
  decimal: '.',
  decimalPlaces: 2,
  isCompact: false,
  currencyCode: 'USD',
  prefixType: 'code',
};

const initDatetimeDFSettings: TDatetimeFormatSetting = {
  hasDateFormat: true,
  hasTimeFormat: true,
  dateParseOption: 'medium',
  timeParseOption: 'medium',
  dateParseFormat: 'MM/DD/YYYY',
  timeParseFormat: '12hour',
};

const defaultDynamicTypes = [...Object.values(DYNAMIC_CONTENT_TYPE)]
  .filter(i => i.value !== 'bo-settings')
  .sort((a, b) => a.index - b.index);
const optionDataType = [...Object.values(DATA_TYPE)];

export const AddDynamicContent: React.FC<AddDynamicContentProps> = props => {
  //* Props
  const {
    defaultData = {},
    defaultDynamicIndex = 1,
    visible,
    showDisplayFormat,
    showIndex,
    journeySettings,
    contentSources,
    isShowCustomFunction = false,
  } = props;
  const { onCancel, onOk, additionalAttrProperties = [] } = props;
  // Hook Queries
  const { mutate: addSavedCSFunction, data: addSaveFunction } = useAddSavedCSFunction();
  const { data: customFunction } = useGetCustomFunction();
  const { mutate: updateCSFunction } = useUpdateCSFunction();

  //* Hooks
  const { i18n, t } = useTranslation();

  //* Selectors
  const { triggerEvent } = journeySettings;

  const [itemTypeId, setItemTypeId] = useState<number | null>(null);

  // Form states
  const [form] = Form.useForm();

  const selectedDynamicContentType = Form.useWatch<string | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE,
    form,
  );

  const selectedAttr = Form.useWatch<LabeledTreeValue | undefined>(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE, form);

  const selectedDFType = Form.useWatch<TDisplayFormat | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE,
    form,
  );

  const selectedEventSource = Form.useWatch<string | undefined>(DYNAMIC_CONTENT_SETTING_KEY.EVENT, form);

  const selectedSource = Form.useWatch<string | undefined>(DYNAMIC_CONTENT_SETTING_KEY.SOURCE, form);

  const selectedBoIndex = Form.useWatch<string | undefined>(DYNAMIC_CONTENT_SETTING_KEY.INDEX, form);

  const selectedPromotionPool = Form.useWatch<string | undefined>(DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL, form);

  const [formSubmitDisabled, setFormSubmitDisabled] = useState<boolean>(true);
  const [saveAsTemplate, setsaveAsTemplate] = useState<boolean>(false);
  const [hasError, setHasError] = useState<Record<string, boolean>>({
    [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.PERNAME]: false,
  });
  // Queries
  const { data: listBO } = useGetListBO();

  // defaultSettings
  const [defaultAttrDFSetings, setDefaultAttrDFSetings] = useState<{ itemPropertyName: string; settings: any } | null>(
    null,
  );

  // Variables
  const [eventActionId, eventCategoryId] = selectedEventSource?.split(':').map(Number) || [];

  // Selection Options states
  const [listDynamicContentTypes, setListDynamicContentTypes] = useState<LabeledValue[]>(defaultDynamicTypes);
  const [listCustomerAttributes, setListCustomerAttributes] = useState<LabeledValue[]>([]);
  const [listVisitorAttributes, setListVisitorAttributes] = useState<LabeledValue[]>([]);
  const [listCustomTemplates, setlistCustomTemplates] = useState<any[]>([]);
  const [listCustomTemplatesSearch, setlistCustomTemplatesSearch] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  // Expanded keys in tree select
  const [expandedEventAttributes, setExpandedEventAttributes] = useState<string[]>([]);
  const [isActionArchive, setActionArchive] = useState<boolean>(false);
  const [errorMessageAttr, setErrorMessageAttr] = useState<string>();
  // Number Display format
  const [showDetailEditDF, setShowDetailEditDF] = useState<boolean>(false);
  const [formatedAttr, setFormatedAttr] = useState<string>('');
  const [attrDFSettings, setAttrDFSettings] = useState<TNumberFormatSettings | TDatetimeFormatSetting | undefined>(
    undefined,
  );
  const [templateName, setTemplateName] = useState<string>('');
  const [attrDFOptions, setAttrDFOptions] = useState<LabeledValue[]>([]);
  // Template Custom
  const [templateId, settemplateId] = useState<number[]>();
  const {
    data: listBoAttributes = [],
    isFetching: isFetchingListBoAttr,
    isError: isErrorBoAttr,
  } = useGetListAttributeBO<LabeledValue[]>({
    itemTypeIds: itemTypeId ? [itemTypeId] : [],
    options: {
      onSuccess: data => {
        setHasError({
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false,
          [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: false,
        });

        if (data.length && selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
          if (
            form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE) &&
            form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.INDEX)
          )
            return;

          form.setFieldsValue({
            [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: data[0],
            [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex,
          });
        }
      },
      select: data => serilizeBOAttr(data, additionalAttrProperties),
      onError: error => {
        setHasError({
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true,
          [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: true,
        });

        handleError(error, {
          component: PATH,
          action: 'useGetListAttributeBO',
          errorMessage: error?.message,
        });
      },
      enabled: !!itemTypeId,
    },
  });

  const {
    data: dynamicContentAttr,
    isFetching: isFetchingDynamicContentAttr,
    isError: isErrorDynamicContentAttr,
  } = useGetDynamicContentAttr({
    onSuccess: () => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false,
      });
    },
    onError: (error: any) => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true,
      });

      handleError(error, {
        component: PATH,
        action: 'useGetDynamicContentAttr',
        errorMessage: error?.message,
      });
    },
  });

  const { data: listSources, isFetching: isFetchingSourceBO } = useGetListSourceByEvent({
    eventActionId,
    eventCategoryId,
    options: {
      onSuccess: () => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: false });
      },
      select: data => {
        const { rows } = data || {};

        return rows.map((row: any) => {
          return {
            label: row.label,
            value: row.value,
          };
        });
      },
      onError: error => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: true });
        handleError(error, {
          component: PATH,
          action: 'useGetListSourceBO',
          errorMessage: error?.message,
        });
      },
    },
  });

  const { data: listEvents, isFetching: isFetchingEventBySource } = useGetListAllEvents({
    options: {
      onSuccess: data => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: false });

        if (!isEmpty(data) && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
          form.setFieldsValue({
            [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: data[0].value,
          });
        }
      },
      onError: error => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: true });

        handleError(error, {
          component: PATH,
          action: 'useGetListEventBySource',
          errorMessage: error?.message,
        });
      },
      select: data => {
        const { rows = [] } = data || {};
        return rows.map(event => {
          return {
            label: event.label,
            value: `${event.eventActionId}:${event.eventCategoryId}`,
          };
        });
      },
    },
  });

  const {
    data: listEventAttributes,
    isFetching: isFetchingEventAttr,
    isError: isErrorEventAttr,
  } = useGetListEventAttr<LabeledTreeValue[]>(
    selectedEventSource || '',
    Array.isArray(selectedSource) ? selectedSource.join(',') : selectedSource || '',
    {
      onSuccess: () => setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false }),
      onError: error => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true });

        handleError(error, {
          component: PATH,
          action: 'useGetListEventAttr',
          errorMessage: error?.message,
        });
      },
      select: data => serilizeEventAttr(data, i18n, additionalAttrProperties),
    },
  );

  const { data: listPromotionPools, isFetching: isFetchingPromotionPool } = useGetListPromotionPool<LabeledValue[]>({
    onSuccess: () => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]: false,
      });
    },
    select: data => serilizePromotionPool(data, additionalAttrProperties),
    onError: error => {
      setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true });

      handleError(error, {
        component: PATH,
        action: 'useGetListPromotionPool',
        errorMessage: error?.message,
      });
    },
  });

  const {
    data: listPromotionCodeAttributes,
    isFetching: isFetchingPromotionCodeAttr,
    isError: isErrorListPromotionCodeAttr,
  } = useGetListPromotionCodeAttr<LabeledValue[]>({
    onSuccess: () => {
      setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false });
    },
    select: data => {
      return data
        .filter(attr => +attr?.status === 1)
        .map(attr => ({
          label:
            attr.propertyDisplayMultilang[i18n.language] ||
            attr.propertyDisplayMultilang[get(attr, "propertyDisplayMultilang['DEFAULT_LANG']", 'EN')],
          value: attr.itemPropertyName,
          dataType: attr.dataType,
          disabled: parseInt(attr.status) === 4,
          status: attr.status,
        }));
    },
    onError: (error: any) => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true,
      });

      handleError(error, {
        component: PATH,
        action: 'useGetPromotionCodeAttr',
        errorMessage: error?.message,
      });
    },
  });

  const { data: eventTrackingAttributes } = useGetEventTrackingAttributes({
    eventActionId,
    eventCategoryId,
  });

  // Memo
  const isShowEventIndexField = useDeepCompareMemo(() => {
    if (selectedAttr && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      if (!!selectedAttr.itemTypeId) {
        return !!get(selectedAttr, 'eventPropertySyntax', '').includes('items');
      }

      return selectedAttr.type === ATTRIBUTE_TYPE.ITEM;
    }

    return false;
  }, [selectedAttr, selectedDynamicContentType, eventTrackingAttributes?.mainObjects]);

  const itemTypeName = useMemo(() => {
    let draftItemTypeName = '';

    if (itemTypeId) {
      draftItemTypeName = listBO?.find(bo => bo.id === itemTypeId)?.name || '';
    }

    return draftItemTypeName;
  }, [itemTypeId, listBO]);

  const isValidatePromotionSelected = useDeepCompareMemo(() => {
    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value && selectedPromotionPool) {
      return (listPromotionPools || []).some(
        attr => attr.value === selectedPromotionPool || attr.label === selectedPromotionPool,
      );
    } else {
      return true;
    }
  }, [selectedDynamicContentType, selectedPromotionPool, listPromotionPools]);

  // Add BO settings (if exists) to list Dynamic content Type, or removed from the previous states
  useEffect(() => {
    const temp = [...defaultDynamicTypes];

    contentSources.groups.forEach(g => {
      if (!g.itemTypeId) return;

      temp.push({
        index: temp.length,
        label: `${g.groupName} (${g.itemTypeDisplay})`,
        value: `content-source::${g.groupId}::${g.itemTypeId}`,
      });
    });
    if (isShowCustomFunction) {
      temp.push({
        ...CUSTOM_TYPE,
        index: temp.length,
      });
    }

    setListDynamicContentTypes(temp);
  }, [contentSources.groups, isShowCustomFunction]);
  useEffect(() => {
    if (Array.isArray(customFunction) && customFunction.length) {
      const temp: any[] = [];
      customFunction.forEach(item => {
        temp.push({
          ...item,
          label: item.template_name,
          value: item.template_name,
        });
      });
      setlistCustomTemplates(temp);
    }
  }, [customFunction, visible]);
  useDeepCompareEffect(() => {
    if (dynamicContentAttr) {
      const { customer, visitor } = dynamicContentAttr;
      const dynamicContentType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
      const parseCustomers: LabeledValue[] = serilizeDynamicContentAttr(customer, i18n, additionalAttrProperties);
      const parseVisitors: LabeledValue[] = serilizeDynamicContentAttr(visitor, i18n, additionalAttrProperties);

      setListCustomerAttributes(parseCustomers);
      setListVisitorAttributes(parseVisitors);

      if (form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)) return;

      if (dynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(parseVisitors)[0],
        });
      }

      if (dynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(parseCustomers)[0],
        });
      }
    }
  }, [dynamicContentAttr, form, i18n.language]);

  useDeepCompareEffect(() => {
    if (listSources && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      if (!isEmpty(form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.SOURCE))) return;

      const defaultValue = !!listSources[0] ? [listSources[0].value] : [];

      form.setFieldsValue({
        [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: defaultValue,
      });
    }
  }, [selectedEventSource, listSources, form, selectedDynamicContentType]);

  useDeepCompareEffect(() => {
    if (listEventAttributes && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      const initEventAttr = listEventAttributes[0]?.children.length
        ? getAvailableAttrs(listEventAttributes[0]?.children)[0]
        : listEventAttributes[0];

      if (form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)) {
        const eventAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

        setExpandedEventAttributes([eventAttr.value.split('.')[0]]);

        return;
      }

      form.setFieldsValue({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: initEventAttr,
      });

      setExpandedEventAttributes([initEventAttr.value.split('.')[0]]);
    }
  }, [selectedSource, selectedEventSource, listEventAttributes, form, selectedDynamicContentType]);

  useDeepCompareEffect(() => {
    if (listPromotionCodeAttributes && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value) {
      if (form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)) return;

      form.setFieldsValue({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listPromotionCodeAttributes)[0],
      });
    }
  }, [selectedDynamicContentType, listPromotionCodeAttributes, form]);

  // Update form field data with default data/ or reset it to initial states
  const resetStateAndFormValueDF = useCallback(() => {
    setShowDetailEditDF(false);
    setAttrDFSettings(undefined);
    setAttrDFOptions([]);
    setFormatedAttr('');
    setsaveAsTemplate(false);
    setTemplateName('');
    setSearchValue('');
    form.setFieldsValue({
      [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: undefined,
      [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: undefined,
      [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: undefined,
      [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: optionDataType[0].value,
    });
  }, [form]);

  useEffect(() => {
    if (!visible) {
      setTimeout(() => {
        resetStateAndFormValueDF();
      }, 200);
    }
  }, [visible, resetStateAndFormValueDF]);

  useEffect(() => {
    let listTmp: any[] = [];
    switch (true) {
      case selectedDynamicContentType && regexCSType.test(selectedDynamicContentType):
        listTmp = [...listBoAttributes];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
        listTmp = listPromotionCodeAttributes ? [...listPromotionCodeAttributes] : [];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
        listTmp = [...listCustomerAttributes];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
        listTmp = [...listVisitorAttributes];
        break;
      default:
        break;
    }
    const { errorMessage, isDisable } = checkStatusAttr({
      listBO: listBO as any,
      itemTypeId: itemTypeId || 0,
      listAttribute: listTmp,
      field: selectedAttr?.value,
    });

    setActionArchive(isDisable);
    setErrorMessageAttr(errorMessage);
  }, [selectedAttr]);

  useEffect(() => {
    if (!visible) {
      return;
    }

    if (Object.keys({ ...defaultData })?.length) {
      let draftDefaultData = { ...defaultData };
      if (draftDefaultData.type !== 'custom') {
        if (triggerEvent?.eventActionId) {
          draftDefaultData = omit(draftDefaultData, ['event', 'source']);
        }

        form.setFieldsValue(draftDefaultData);

        const defaultDMType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
        const defaultAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

        if (defaultDMType && regexCSType.test(defaultDMType)) {
          const { itemTypeId } = serializeCSSelectedString(defaultDMType);

          if (!isNaN(itemTypeId)) setItemTypeId(itemTypeId);
        }
        if (defaultAttr) {
          const defaultAttr = draftDefaultData[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE];

          if (!defaultAttr.hasOwnProperty('dataType')) return;

          let key = '';

          if (defaultAttr.dataType === 'number') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS;
          }
          if (defaultAttr.dataType === 'datetime') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS;
          }

          if (key && defaultAttr[key]) {
            const { type, ...settingValues } = defaultAttr[key];

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: type,
            });

            setDefaultAttrDFSetings({
              itemPropertyName: defaultAttr.value,
              settings: settingValues,
            });
            setAttrDFSettings(settingValues);
            setAttrDFOptions(DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === defaultAttr.dataType));
          }
        }
      } else {
        const { customFunction } = draftDefaultData;
        const { outputFormat } = customFunction;
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE]: draftDefaultData.type,
          [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: outputFormat.type,
          [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: customFunction.outputDataType,
          [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: customFunction.customFunction,
        });
        setTemplateName(customFunction.templateName);
        setAttrDFOptions(
          DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === customFunction.outputDataType),
        );
        setShowDetailEditDF(true);
        setAttrDFSettings(customFunction.outputDataType === 'string' ? undefined : outputFormat.config);
      }
      // Check if trigger event of journey has event then don's set current event and source
    } else {
      // Hard reset to Visitor attr
      if (listVisitorAttributes) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE]: DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value,
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: listVisitorAttributes[0],
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultData, visible, form]);
  useDeepCompareEffect(() => {
    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      const { eventActionId, eventCategoryId, insightPropertyIds } = triggerEvent || {};

      if (!!eventActionId && !!eventCategoryId) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: `${eventActionId}:${eventCategoryId}`,
        });
      }

      if (Array.isArray(insightPropertyIds) && insightPropertyIds.length) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: insightPropertyIds,
        });
      }
    }
  }, [triggerEvent, selectedDynamicContentType]);

  // Disable Apply button if there's any error
  useLayoutEffect(() => {
    setFormSubmitDisabled(Object.values(hasError).some(error => error));
  }, [hasError]);

  useLayoutEffect(() => {
    if (
      selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value ||
      selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value
    ) {
      setFormSubmitDisabled(isFetchingDynamicContentAttr);
    }

    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      setFormSubmitDisabled(isFetchingSourceBO || isFetchingEventBySource || isFetchingEventAttr);
    }

    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value) {
      setFormSubmitDisabled(isFetchingPromotionPool || isFetchingPromotionCodeAttr);
    }

    if (selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
      setFormSubmitDisabled(isFetchingListBoAttr);
    }
  }, [
    isFetchingDynamicContentAttr,
    selectedDynamicContentType,
    isFetchingSourceBO,
    isFetchingEventBySource,
    isFetchingEventAttr,
    isFetchingPromotionPool,
    isFetchingPromotionCodeAttr,
    isFetchingListBoAttr,
  ]);

  useLayoutEffect(() => {
    let formatedDFValue: string = '';
    let settings = attrDFSettings;
    let selectedAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);
    let dataType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE);
    let dynamicType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
    if ((Object.keys(defaultData).length > 0 && defaultData.type !== 'custom') || dynamicType !== 'custom') {
      if (selectedAttr && selectedAttr.dataType && ['datetime', 'number'].includes(selectedAttr.dataType)) {
        /* when change select  if attr selected is previous selected default attr, set settings as default settings */
        if (!settings && defaultAttrDFSetings?.itemPropertyName === (selectedAttr.value as string)) {
          // Visitor and Customer don't have additional field
          let equalDefaultAttr: boolean = true;

          // Event need check additional field source, event by soucre
          if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
            const source = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.SOURCE);
            const event = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.EVENT);

            if (
              source !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.SOURCE] ||
              event !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.EVENT]
            )
              equalDefaultAttr = false;
          }

          // Promotion need check additional field promotion pool
          if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value) {
            const pool = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL);

            if (pool !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]) equalDefaultAttr = false;
          }

          // BO settings need check additional field index
          if (selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
            if (selectedBoIndex !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.INDEX]) equalDefaultAttr = false;
          }

          if (equalDefaultAttr) {
            settings = defaultAttrDFSetings.settings;
          }
        }
        // *********** //

        // Numberic Attribute DF
        if (selectedAttr?.dataType === 'number') {
          settings = settings ?? initNumberDFSettings;
          formatedDFValue = formatNumberDF(
            1234.56789,
            selectedDFType as 'number' | 'currency' | 'percentage',
            settings as TNumberFormatSettings,
          );
        }

        // Datatime Attribute DF
        if (selectedAttr?.dataType === 'datetime') {
          settings = settings ?? { ...initDatetimeDFSettings, language: i18n.language };
          formatedDFValue = formatDatetimeDF(dateExample, settings as TDatetimeFormatSetting);
        }

        if (!attrDFOptions.length) {
          setAttrDFOptions(DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === selectedAttr.dataType));
        }
        setAttrDFSettings(settings);
        setFormatedAttr(formatedDFValue);
      }
    }
    if (dataType && ['datetime', 'number'].includes(dataType) && dynamicType === 'custom') {
      // Numberic Attribute DF
      if (dataType === 'number') {
        settings = settings ?? initNumberDFSettings;
        formatedDFValue = formatNumberDF(
          1234.56789,
          selectedDFType as 'number' | 'currency' | 'percentage',
          settings as TNumberFormatSettings,
        );
      }
      // Datatime Attribute DF
      if (dataType === 'datetime') {
        settings = settings ?? { ...initDatetimeDFSettings, language: i18n.language };
        formatedDFValue = formatDatetimeDF(dateExample, settings as TDatetimeFormatSetting);
      }
      if (!attrDFOptions.length) {
        setAttrDFOptions(DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === dataType));
      }

      setAttrDFSettings(settings);
      setFormatedAttr(formatedDFValue);
    }
  }, [
    attrDFSettings,
    selectedDFType,
    attrDFOptions.length,
    i18n.language,
    defaultAttrDFSetings,
    form,
    selectedAttr,
    defaultData,
    selectedDynamicContentType,
    selectedBoIndex,
  ]);

  /** Check Promotion Pool Selected */

  //* Handlers
  const handleOnCancelModal = () => {
    if (onCancel) {
      onCancel();
    }
  };
  const onChangeDataInput = value => {
    setTemplateName(value);
    let tempState = [...listCustomTemplates];
    if (value.length > 0) {
      tempState = tempState.filter(item => item.label.toLowerCase().includes(value.trim().toLowerCase()));
    }
    setlistCustomTemplatesSearch(tempState);
    setSearchValue(value);
  };

  const handleSelect = data => {
    const dataTmp = listCustomTemplates.filter(each => each.value === data);
    settemplateId(dataTmp[0].template_id);
    let dataFormat = dataTmp[0].output_format.type.toLowerCase();
    switch (dataTmp[0].output_format.type) {
      case 'DATE_AND_TIME':
        dataFormat = 'date_and_time';
        break;
      case 'RAW_STRING':
        dataFormat = 'raw_string';
        break;
      default:
        break;
    }
    // const dataFormat =
    //   dataTmp[0].output_format.type.toLowerCase() === 'date_and_time'
    //     ? 'datetime'
    //     : dataTmp[0].output_format.type.toLowerCase();
    form.setFieldsValue({
      [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: dataTmp[0].custom_function,
      [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: dataTmp[0].output_data_type,
      [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: dataFormat === 'date_and_time' ? 'datetime' : dataFormat,
    });
    setTemplateName(dataTmp[0].template_name);
    setAttrDFOptions(DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === dataTmp[0].output_data_type));
    const format = dataTmp[0].output_format.config;
    switch (dataFormat) {
      case 'currency': {
        setAttrDFSettings({
          ...initNumberDFSettings,
          grouping: format.group || ',',
          decimal: format.decimal,
          decimalPlaces: format.decimalPlace,
          isCompact: format.isCompactNumber || false,
          currencyCode: format.currency || 'USD',
          prefixType: format.prefixType || 'code',
        });
        break;
      }
      case 'number':
      case 'percentage': {
        setAttrDFSettings({
          ...initNumberDFSettings,
          grouping: format.group || ',',
          decimal: format.decimal,
          decimalPlaces: format.decimalPlace,
          isCompact: format.isCompactNumber || false,
        });
        break;
      }
      case 'date_and_time': {
        setAttrDFSettings({
          ...initDatetimeDFSettings,
          language: i18n.language,
          hasDateFormat: format.date.check || true,
          hasTimeFormat: format.time.check || true,
          dateParseOption: format.date.value || 'medium',
          timeParseOption: format.time.value || 'medium',
          dateParseFormat: format.format || 'MM/DD/YYYY',
          timeParseFormat: `${format.time.timeFormat || 12}hour`,
        });

        break;
      }
      case 'raw_string': {
        setAttrDFSettings(undefined);

        break;
      }
      default:
        break;
    }
  };
  // const handleSearch = data => {
  //   console.log('onSelect', data);
  // };
  const handleOnOkModal = () => {
    if (isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      return;
    }
    form.validateFields().then((values: Record<string, any>) => {
      values = omit(values, 'dfType');

      if (onOk) {
        const type = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
        if (type === 'custom') {
          let outputFormat = {};
          const perName = templateName;
          const functionCustom = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.FUNCTION);
          const dataType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE);
          const displayFormat = form.getFieldValue([DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]);
          if (templateId && !saveAsTemplate) {
            try {
              updateCSFunction({
                templateId,
                name: perName,
                dataType,
                functionCustom,
                dataFormat: attrDFSettings,
                displayFormat,
                outputFormat,
              });
            } catch (error) {
              handleError(error, {
                path: PATH,
                name: 'onClickSaveDynamicContent',
                args: {},
              });
            }
          } else if (saveAsTemplate) {
            try {
              addSavedCSFunction({
                name: perName,
                dataType,
                functionCustom,
                dataFormat: attrDFSettings,
                displayFormat,
                outputFormat,
              });
            } catch (error) {
              handleError(error, {
                path: PATH,
                name: 'onClickSaveDynamicContent',
                args: {},
              });
            }
          }
          if (!showIndex) {
            values = omit(values, 'index');
          }
          const config: any = {
            ...attrDFSettings,
          };
          if (dataType === 'datetime') {
            config.dateFormatString = getStringFormatDateDF(attrDFSettings as TDatetimeFormatSetting);
          }
          onOk({
            ...values,
            customFunction: {
              outputFormat: {
                type: displayFormat,
                config,
              },
              templateCode: serializeLabelToCode(perName),
              templateName: perName,
              templateType: 'custom',
              customFunction: functionCustom,
              outputDataType: dataType,
            },
            attribute: {
              label: perName,
              value: serializeLabelToCode(perName),
            },
            mappingFields: `#{custom.${serializeLabelToCode(perName)}}`,
          });
        } else {
          let key = '';
          let otherField: any = {};

          const selectedAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

          if (selectedAttr?.dataType === 'number') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS;
          }
          if (selectedAttr?.dataType === 'datetime') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS;
            otherField.dateFormatString = getStringFormatDateDF(attrDFSettings as TDatetimeFormatSetting);
          }

          if (key && showDisplayFormat) {
            values[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE][key] = {
              ...attrDFSettings,
              ...otherField,
              type: selectedDFType,
            };
          }

          // Handle Merge tag for event attributes
          if (values.type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
            if (values.index != null && !get(values, 'attribute.itemTypeName', null)) {
              const itemTypeName =
                listBO?.find(({ id }) =>
                  get(eventTrackingAttributes, 'mainObjects', []).some(({ itemTypeId }) => itemTypeId === id),
                )?.name || null;

              if (!!itemTypeName) {
                set(values, 'attribute.itemTypeName', itemTypeName);
              }
            }
          }

          // Set itemTypeName if Selecting content source
          if (regexCSType.test(values.type) && !!itemTypeName) {
            set(values, 'attribute.itemTypeName', itemTypeName);
          }

          if (!showIndex) {
            values = omit(values, 'index');
          }

          const mappingFields = buildMappingFields(values);

          onOk({
            ...values,
            ...(mappingFields && { mappingFields }),
          });
        }
      }
    });
  };

  const handleOnClickParentNode = (item: LabeledTreeValue) => {
    if (item.children.length) {
      if (expandedEventAttributes.includes(item.value)) {
        // close expand
        const newExpandedKeys = expandedEventAttributes.filter(key => key !== item.value);
        handleTreeExpand(newExpandedKeys);
      } else {
        // open expand
        handleTreeExpand((prev: any) => [...prev, item.value]);
      }
    }
  };

  const handleTreeExpand = (expandedKeys: any) => {
    setExpandedEventAttributes(expandedKeys);
  };
  const handleSaveAsTemplate = () => {
    setsaveAsTemplate(!saveAsTemplate);
  };
  const handleFormValuesChanges = (changedValues: any, _allValues: any) => {
    const [fieldName, fieldValue] = Object.entries(changedValues)[0] as [string, any];
    switch (fieldName) {
      case DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE:
        resetStateAndFormValueDF();
        switch (true) {
          case fieldValue === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listCustomerAttributes)[0],
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorDynamicContentAttr });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listVisitorAttributes)[0],
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorDynamicContentAttr });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
            if (!listEvents?.length) break;

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: listEvents[0].value,
              [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: undefined,
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorEventAttr });

            break;
          case fieldValue === CUSTOM_TYPE.value:
            if (!listEvents?.length) break;
            let formatedDFValue: string = '';
            let settings = attrDFSettings;
            settings = settings ?? initNumberDFSettings;
            formatedDFValue = formatNumberDF(
              1234.56789,
              selectedDFType as 'number' | 'currency' | 'percentage',
              settings as TNumberFormatSettings,
            );
            setFormatedAttr(formatedDFValue);
            setAttrDFSettings(settings);
            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: optionDataType[0].value,
              [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: optionDataType[0].value,
            });
            setAttrDFOptions(
              DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === optionDataType[0].value),
            );
            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorEventAttr });
            setShowDetailEditDF(true);
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
            if (!listPromotionCodeAttributes || !listPromotionPools) break;

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]: listPromotionPools[0]?.value,
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listPromotionCodeAttributes)[0],
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorListPromotionCodeAttr });
            break;
          case regexCSType.test(fieldValue): {
            const { itemTypeId: selectedItemTypeId } = serializeCSSelectedString(String(fieldValue));

            if (isNaN(selectedItemTypeId)) break;

            setItemTypeId(Number(selectedItemTypeId));

            const key = [QUERY_KEYS.GET_EVENT_ATTRIBUTE_BO, [selectedItemTypeId]];
            const listAttBOByItemTypeId = serilizeBOAttr(queryClient.getQueryData(key));

            if (listAttBOByItemTypeId.length) {
              form.setFieldsValue({
                [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: listAttBOByItemTypeId[0],
                [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex,
              });
            }

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorBoAttr });
            break;
          }
        }

        break;

      case DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE:
        let listAttributes: any[] = [];

        switch (true) {
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
            if (!listEventAttributes) break;

            let newSelectEventAttr = listEventAttributes.find(row => {
              if (row?.children.length) {
                return row.children.find(item => item.value === fieldValue.value);
              }

              return row.value === fieldValue.value;
            });

            if (newSelectEventAttr?.children.length) {
              newSelectEventAttr = newSelectEventAttr.children.find(item => item.value === fieldValue.value);
            }

            listAttributes = flatMapDeep(listEventAttributes, getMembers);

            form.setFieldsValue({ [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex });

            break;
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
            listAttributes = listCustomerAttributes;
            break;
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
            listAttributes = listPromotionCodeAttributes ?? [];
            break;
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
            listAttributes = listVisitorAttributes;
            break;
          case selectedDynamicContentType && regexCSType.test(selectedDynamicContentType):
            form.setFieldsValue({ [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex });

            if (!listBoAttributes) break;

            listAttributes = listBoAttributes;
            break;
        }

        // add dataType to
        if (listAttributes.length) {
          const attr = listAttributes.find(attr => {
            if (attr.value === fieldValue.value) {
              return attr;
            }

            return null;
          });

          form.setFieldsValue({
            [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: attr,
          });

          // set new value for options display format
          if (attr) {
            const newAttrDFOptions = DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === attr.dataType);

            if (!!newAttrDFOptions.length) {
              form.setFieldsValue({
                [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: newAttrDFOptions[0].value,
              });
              setAttrDFOptions(newAttrDFOptions);
            }

            // reset detail display format settings
            setAttrDFSettings(undefined);
            setShowDetailEditDF(false);
          }
        }
        break;

      case DYNAMIC_CONTENT_SETTING_KEY.INDEX:
        // setAttrDFSettings(undefined);
        break;
      // case DYNAMIC_CONTENT_SETTING_KEY.PERNAME:
      //   console.log(fieldValue)
      //   form.setFieldsValue({
      //     [DYNAMIC_CONTENT_SETTING_KEY.PERNAME]: fieldValue,
      //   });
      //   break;
      case DYNAMIC_CONTENT_SETTING_KEY.FUNCTION:
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: fieldValue,
        });
        break;
      case DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE:
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: fieldValue,
          [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: fieldValue === 'string' ? 'raw_string' : fieldValue,
        });
        // let formatedDFValue: string = '';
        let settings = attrDFSettings;
        // settings = settings ?? initNumberDFSettings;
        // formatedDFValue = formatNumberDF(
        //   1234.56789,
        //   selectedDFType as 'number' | 'currency' | 'percentage',
        //   settings as TNumberFormatSettings,
        // );
        // if (fieldValue === 'number') {
        //   settings = settings ?? initNumberDFSettings;
        //   formatedDFValue = formatNumberDF(
        //     1234.56789,
        //     selectedDFType as 'number' | 'currency' | 'percentage',
        //     settings as TNumberFormatSettings,
        //   );
        // }
        // Datatime Attribute DF
        if (fieldValue === 'datetime') {
          settings = { ...initDatetimeDFSettings, language: i18n.language };
          // formatedDFValue = formatDatetimeDF(dateExample, settings as TDatetimeFormatSetting);
        } else if (fieldValue === 'string') {
          settings = undefined;
        }
        setAttrDFOptions(DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === fieldValue));
        // setFormatedAttr(formatedDFValue);
        setAttrDFSettings(settings);
        break;
      case DYNAMIC_CONTENT_SETTING_KEY.EVENT:
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: undefined,
        });

        resetStateAndFormValueDF();
        break;

      case DYNAMIC_CONTENT_SETTING_KEY.SOURCE:
        resetStateAndFormValueDF();

        break;

      case DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE:
        switch (fieldValue) {
          case 'currency': {
            setAttrDFSettings({
              ...initNumberDFSettings,
              decimalPlaces: 1,
            });
            break;
          }
          case 'number':
          case 'percentage': {
            setAttrDFSettings(initNumberDFSettings);
            break;
          }
          case 'datetime': {
            setAttrDFSettings({
              ...initDatetimeDFSettings,
              language: i18n.language,
            });
            break;
          }
          case 'raw_string': {
            setAttrDFSettings(undefined);
            break;
          }
        }
    }
  };

  //* Render functions
  const renderBoAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.boAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        label={t(translations.dynamicContent.modal.label.attribute)}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingListBoAttr}
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listBoAttributes || [], selectedAttr)}
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderIndexFields = ({ disabled = false, loading = false }) => {
    if (!showIndex) return null;

    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return null;
    }

    return (
      <Form.Item
        className="ants-items-center"
        label={t(translations.dynamicContent.modal.label.index)}
        name={DYNAMIC_CONTENT_SETTING_KEY.INDEX}
        required
      >
        <Select
          disabled={disabled}
          loading={loading}
          className={classnames('ants-h-7')}
          options={Array.from({ length: 90 }, (_, i) => ({ value: i + 1, label: i + 1 }))}
          showSearch
        />
      </Form.Item>
    );
  };
  const renderCustomFunctionFields = () => {
    const dataType = form.getFieldValue([DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]);
    return (
      <Fragment>
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicContent.modal.label.personalizationName)}
          name={DYNAMIC_CONTENT_SETTING_KEY.PERNAME}
          required
        >
          <InputSuggestion
            onSelect={handleSelect}
            placeholder="Custom function name..."
            options={searchValue.length > 0 ? listCustomTemplatesSearch : listCustomTemplates}
            onChange={onChangeDataInput}
            value={templateName}
            // value={form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.PERNAME)}
          />
          {hasError[DYNAMIC_CONTENT_SETTING_KEY.PERNAME] && (
            <Text type="error">{t(translations.dynamicContent.modal.error.perName)}</Text>
          )}
        </Form.Item>
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicContent.modal.label.function)}
          name={DYNAMIC_CONTENT_SETTING_KEY.FUNCTION}
          required
        >
          <EditorScript
            // expandModalLabel={t(translations.customJavascript.title)}
            // label={t(translations.customJavascript.description)}
            // value={customJS.rawEditorOutput}
            mode="javascript"
            // onBlur={onBlurJSEditorScript}
            hideExpand
          />
        </Form.Item>
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicContent.modal.label.ouputDataType)}
          name={DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE}
          required
        >
          <Select
            className={classnames('ants-h-7')}
            options={optionDataType}
            // onChange={value => onChangeDragFieldSetting('datetimeType', value)}
          />
        </Form.Item>
        <Form.Item
          // initialValue={attrDFOptions[0].value}
          className="ants-items-start"
          required
          label={t(translations.dynamicContent.modal.label.ouputFormat)}
          name={DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE}
          extra={
            <div className="ants-flex ants-flex-wrap ants-items-center ants-mt-4 ants-gap-2">
              {dataType !== 'raw_string' ? (
                <Text
                  color="#555"
                  dangerouslySetInnerHTML={{
                    __html: `${t(
                      translations.dynamicContent.modal.label.displayFormat,
                    )}: <bdo dir='ltr'>${formatedAttr}</bdo>`,
                  }}
                />
              ) : (
                <></>
              )}

              {!showDetailEditDF && (
                <Button
                  disabled={
                    isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
                  }
                  type="text"
                  className="ants-inline-block"
                  onClick={() => setShowDetailEditDF(prevShow => !prevShow)}
                >
                  {t(translations.edit.title)}
                </Button>
              )}
            </div>
          }
        >
          <Select
            disabled={isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value}
            className={classnames('ants-h-7')}
            options={attrDFOptions}
          />
        </Form.Item>
      </Fragment>
    );
  };
  const renderCustomerAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.customerAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listCustomerAttributes[0]}
        label={t(translations.dynamicContent.modal.label.attribute)}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingDynamicContentAttr}
          className={classnames('ants-h-7')}
          labelInValue
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
          options={buildOptionAttrArchive(listCustomerAttributes, selectedAttr)}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderEventAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.eventAttr)}</Text>;
    }

    return (
      <Fragment>
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicContent.modal.label.selectEventAttr)}
          name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
          required
        >
          <TreeSelect
            loading={isFetchingEventAttr || !listEvents?.length}
            className={classnames('ants-h-7')}
            // filterTreeNode={(val, option) => {
            //   if (option?.children?.length) {
            //     return option?.children?.some(
            //       child => (child?.title?.toString() || '').toLowerCase().indexOf(val.toLowerCase().trim()) >= 0,
            //     );
            //   } else {
            //     return (option?.title?.toString() || '').toLowerCase().indexOf(val.toLowerCase().trim()) >= 0;
            //   }
            // }}
            labelInValue
            showSearch
            // treeExpandedKeys={expandedEventAttributes}
            // onSearch={handleTreeSearch}
            // onTreeExpand={handleTreeExpand}
          >
            {listEventAttributes &&
              listEventAttributes.map(attr => (
                <TreeNode
                  key={attr.value}
                  value={attr.value}
                  selectable={!attr?.children?.length}
                  title={
                    attr.children?.length ? (
                      <div onClick={() => handleOnClickParentNode(attr)}>{attr.label}</div>
                    ) : (
                      attr.label
                    )
                  }
                >
                  {buildOptionAttrArchive(attr?.children, selectedAttr)?.map(item => (
                    <TreeNode key={item.value} value={item.value} title={item.label} />
                  ))}
                </TreeNode>
              ))}
          </TreeSelect>
        </Form.Item>

        {form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE) &&
          form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)?.status !== 1 && (
            <Text type="warning">{t(translations.dynamicContent.modal.message.selectDisableEventAttr)}</Text>
          )}
      </Fragment>
    );
  };

  const renderEventFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.EVENT]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.eventBySource)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        label={t(translations.dynamicContent.modal.label.selectEvent)}
        name={DYNAMIC_CONTENT_SETTING_KEY.EVENT}
        required
        preserve={false}
      >
        <Select
          disabled={!!triggerEvent?.eventActionId && !!triggerEvent?.eventCategoryId}
          loading={isFetchingEventBySource}
          className={classnames('ants-h-7')}
          options={listEvents}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderSourceFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.SOURCE]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.source)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        label={t(translations.dynamicContent.modal.label.selectSource)}
        name={DYNAMIC_CONTENT_SETTING_KEY.SOURCE}
        required
      >
        <Select
          disabled={!isEmpty(triggerEvent?.insightPropertyIds)}
          mode="multiple"
          loading={isFetchingSourceBO}
          options={listSources}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderPromotionCodeAttr = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.promotionCodeAttr)}</Text>;
    }
    return (
      <Form.Item
        className="ants-items-center"
        label={t(translations.dynamicContent.modal.label.promotionCodeAttr)}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingPromotionCodeAttr}
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listPromotionCodeAttributes || [], selectedAttr)}
          showSearch
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };

  const renderPromotionPoolFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.promotionPools)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        // initialValue={listPromotionPools[0]?.value}
        label={t(translations.dynamicContent.modal.label.promotionPools)}
        name={DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL}
        required
      >
        <Select
          loading={isFetchingPromotionPool}
          className={classnames('ants-h-7')}
          options={listPromotionPools}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderVisitorAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicContent.modal.error.visitorAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listVisitorAttributes[0]}
        label={t(translations.dynamicContent.modal.label.attribute)}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingDynamicContentAttr}
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listVisitorAttributes, selectedAttr?.value)}
          showSearch
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };

  const renderDisplayFormat = () => {
    const selectedAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

    if (
      formSubmitDisabled ||
      !attrDFSettings ||
      isEmpty(attrDFSettings) ||
      !selectedAttr?.dataType ||
      !attrDFOptions.length
    )
      return <></>;

    if (selectedAttr.dataType === 'number' || selectedAttr.dataType === 'datetime')
      return (
        <>
          <Form.Item
            initialValue={attrDFOptions[0].value}
            className="ants-items-start"
            label={t(translations.dynamicContent.modal.label.displayFormat)}
            name={DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE}
            extra={
              <div className="ants-flex ants-flex-wrap ants-items-center ants-mt-4 ants-gap-2">
                <Text
                  color="#555"
                  dangerouslySetInnerHTML={{
                    __html: `${t(
                      translations.dynamicContent.modal.label.displayFormat,
                    )}: <bdo dir='ltr'>${formatedAttr}</bdo>`,
                  }}
                />
                {!showDetailEditDF && (
                  <Button
                    disabled={
                      isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
                    }
                    type="text"
                    className="ants-inline-block"
                    onClick={() => setShowDetailEditDF(prevShow => !prevShow)}
                  >
                    {t(translations.edit.title)}
                  </Button>
                )}
              </div>
            }
          >
            <Select
              disabled={isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value}
              className={classnames('ants-h-7')}
              options={attrDFOptions}
            />
          </Form.Item>
        </>
      );

    return null;
  };

  const renderDetailDFSettings = () => {
    if (showDetailEditDF && selectedDFType && attrDFSettings)
      return (
        <div className="ants-border-[1px] ants-border-[#9e9e9e] ants-rounded-sm ants-p-2 ants-mt-2  ants-flex ants-flex-col ants-gap-2">
          <DisplayFormat
            displayFormatType={selectedDFType}
            formatSettings={attrDFSettings}
            onSettingsChange={values => setAttrDFSettings(values)}
            dynamicContentType={form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE)}
          />
        </div>
      );
  };

  return (
    <ModalCustom
      closable={false}
      destroyOnClose={true}
      forceRender
      mask={true}
      okButtonProps={{ disabled: formSubmitDisabled || !isValidatePromotionSelected }}
      title={props.modalTitle || t(translations.dynamicContent.modal.title.addDynamicContent)}
      visible={visible}
      onCancel={handleOnCancelModal}
      onOk={handleOnOkModal}
      okText={t(translations.apply.title)}
    >
      <Form
        className={classnames('ants-w-full')}
        form={form}
        labelAlign="left"
        labelCol={{ span: 9 }}
        wrapperCol={{ span: 15 }}
        onValuesChange={handleFormValuesChanges}
        initialValues={{
          [DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE]: listDynamicContentTypes[0].value,
        }}
      >
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicContent.modal.label.contentSource)}
          name={DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE}
          required
        >
          <Select className={classnames('ants-h-7')} options={listDynamicContentTypes} />
        </Form.Item>

        {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value && (
          <Fragment>{renderVisitorAttrFields()}</Fragment>
        )}

        {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value && (
          <Fragment>{renderCustomerAttrFields()}</Fragment>
        )}
        {selectedDynamicContentType === CUSTOM_TYPE.value && <Fragment>{renderCustomFunctionFields()}</Fragment>}
        {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value && (
          <Fragment>
            {renderEventFields()}
            {renderSourceFields()}
            {renderEventAttrFields()}
            {isShowEventIndexField ? renderIndexFields({ disabled: false, loading: false }) : null}
          </Fragment>
        )}

        {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value && (
          <Fragment>
            {renderPromotionPoolFields()}
            {renderPromotionCodeAttr()}
          </Fragment>
        )}

        {selectedDynamicContentType && regexCSType.test(selectedDynamicContentType) && (
          <Fragment>
            {renderBoAttrFields()}
            {renderIndexFields({ disabled: isActionArchive, loading: isFetchingListBoAttr })}
          </Fragment>
        )}

        {showDisplayFormat && renderDisplayFormat()}
      </Form>

      {showDisplayFormat && renderDisplayFormat() !== null && renderDetailDFSettings()}
      {isShowCustomFunction && selectedDynamicContentType === CUSTOM_TYPE.value && (
        <div className="ants-flex ants-items-center ants-justify-start ants-mb-2" style={{ marginTop: '10px' }}>
          <Checkbox onChange={handleSaveAsTemplate} checked={saveAsTemplate}>
            <span style={{ fontSize: '12px' }}>{t(translations.dynamicContent.modal.label.saveTemplate)}</span>
          </Checkbox>
        </div>
      )}
    </ModalCustom>
  );
};

AddDynamicContent.defaultProps = {
  showDisplayFormat: true,
  showIndex: true,
};
