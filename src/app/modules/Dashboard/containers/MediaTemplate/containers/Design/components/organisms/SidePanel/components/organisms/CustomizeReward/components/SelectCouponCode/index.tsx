// Libraries
import React, { memo, useEffect, useMemo, useState } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { useTranslation } from 'react-i18next';
import classnames from 'classnames';

// Atoms
import { Text, Input, Typography, Tooltip } from 'app/components/atoms';

// Hooks
import { usePrevious } from 'app/hooks';

// Utils
import { isAttrValue, serializeAttrValue } from './utils';
import { handleError } from 'app/utils/handleError';
import { reorder } from 'app/utils/common';

// Styled
import { StyledOptionTitle, StyledTreeSelect } from './styled';

type TProps = {
  promotionPools: any[];
  promotionAttrs: any[];
  poolValue: string;
  attrValue: string;
  onChange?: (values: { pool: string; attr: string }) => void;
  usePool: boolean;
  required?: boolean;
  status?: 'warning' | 'error';
  helperMsg?: string;
  disabled?: boolean;
  forceShowHelperMsg?: boolean;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/CouponWheel/Content/components/SelectCouponCode/index.tsx';

const SelectCouponCode = (props: TProps) => {
  const { i18n } = useTranslation();

  const { promotionPools = [], promotionAttrs = [], poolValue, usePool } = props;
  // const [poolSearchTerm, setPoolSerchTerm] = useState<string>('');

  const previousStatus = usePrevious(props.status);
  const [opendFirstTime, setOpendFirstTime] = useState<boolean>(false);

  const [values, setValues] = useState<{ pool: string; attr: string }>({ pool: '', attr: '' });
  const [displayValues, setDisplayValues] = useState<{ poolDisplay: string; attrDisplay: string }>({
    poolDisplay: '',
    attrDisplay: '',
  });

  useEffect(() => {
    setValues({
      pool: props.poolValue,
      attr: props.attrValue,
    });
  }, [props.poolValue, props.attrValue]);

  useEffect(() => {
    setOpendFirstTime(false);
  }, [props.usePool]);

  useEffect(() => {
    if (previousStatus === 'error' && previousStatus !== props.status) {
      setOpendFirstTime(false);
    }
  }, [previousStatus, props.status]);

  useEffect(() => {
    let poolDisplay = '';
    let attrDisplay = '';

    if (values.pool && promotionPools.length) {
      poolDisplay = promotionPools.find(i => i.code === values.pool)?.name ?? '';
    }

    if (values.attr && promotionAttrs.length) {
      attrDisplay = promotionAttrs.find(i => i.itemPropertyName === values.attr);
      attrDisplay = attrDisplay ? get(attrDisplay, `propertyDisplayMultilang.${i18n.language.toUpperCase()}`) : '';
    }

    setDisplayValues({ attrDisplay, poolDisplay });
  }, [values, promotionAttrs, promotionPools, i18n]);

  const optionPromotionPools: {
    title: React.ReactNode;
    value: string;
    children: {
      title: React.ReactNode;
      value: string;
    }[];
  }[] = useMemo(() => {
    let draftOptionPromotionPools = promotionPools.map(p => ({
      title: (
        <StyledOptionTitle
          className={classnames('', {
            'ants-bg-button-primary-active': poolValue === p.code,
          })}
        >
          {p.name}
        </StyledOptionTitle>
      ),
      selectable: false,
      value: `pool@@${p.code}`,
      children: promotionAttrs.map(attr => {
        const displayMultilang = get(attr, `propertyDisplayMultilang.${i18n.language.toUpperCase()}`);

        return {
          title: displayMultilang || attr.itemPropertyName,
          value: `pool@@${p.code}::attr@@${attr.itemPropertyName}`,
        };
      }),
    }));

    if (poolValue) {
      const poolOptionIndex = draftOptionPromotionPools.findIndex(({ value }) => value === `pool@@${poolValue}`);

      if (poolOptionIndex !== -1) {
        draftOptionPromotionPools = reorder(draftOptionPromotionPools, poolOptionIndex, 0);
      }
    }

    return draftOptionPromotionPools;
  }, [promotionPools, promotionAttrs, poolValue, i18n.language]);

  const handleChangePoolValues = (value: string, _label: any, _extra: any) => {
    if (isAttrValue(value)) {
      const { attrValue, poolValue } = serializeAttrValue(value);
      const newValue = { pool: poolValue, attr: attrValue };

      setValues(newValue);
      props.onChange && props.onChange(newValue);
    }
  };

  const handleChangeFixedValue = (value: string) => {
    const newValue = { pool: value, attr: '' };

    setValues(newValue);
    props.onChange && props.onChange(newValue);
  };

  const renderCouponCode = () => {
    try {
      return (
        <div className="ants-relative w-full !ants-block">
          {props.usePool ? (
            <>
              {!isEmpty(displayValues.poolDisplay) ? (
                <Typography.Text
                  className="ants-absolute ants-bottom-full ants-text-[11px] ants-text-[#7f7f7f] ants-w-[70%]"
                  ellipsis={{
                    suffix: ' »',
                  }}
                >
                  {displayValues.poolDisplay}
                </Typography.Text>
              ) : null}

              <StyledTreeSelect
                treeData={optionPromotionPools}
                onChange={(value, label, extra) => handleChangePoolValues(value, label, extra)}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto', minWidth: 300 }}
                showSearch
                value={values.pool && values.attr ? `pool@@${values.pool}::attr@@${values.attr}` : null}
                disabled={props.disabled}
                dropdownRender={menu => {
                  return <div>{menu}</div>;
                }}
                onDropdownVisibleChange={open => {
                  if (open === false && opendFirstTime === false) {
                    setOpendFirstTime(true);
                  }
                }}
                status={props.status}
                // status={opendFirstTime ? props.status : undefined}
              />
            </>
          ) : (
            <Input
              value={values.pool}
              disabled={props.disabled}
              onBlur={() => setOpendFirstTime(true)}
              onAfterChange={value => handleChangeFixedValue(value)}
              debounce={600}
              status={opendFirstTime ? props.status : undefined}
            />
          )}

          {(opendFirstTime || props.forceShowHelperMsg) && props.helperMsg && (
            <Text
              className={classnames('ants-mt-1', {
                '!ants-text-cus-error': props.status === 'error',
                '!ants-text-cus-warning': props.status === 'warning',
              })}
            >
              {props.helperMsg}
            </Text>
          )}
        </div>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderCouponCode',
        args: {},
      });

      return <></>;
    }
  };

  return !!poolValue && usePool ? (
    <Tooltip
      overlay={`${displayValues.poolDisplay} >> ${displayValues.attrDisplay}`}
      placement="top"
      mouseEnterDelay={0.2}
      zIndex={1049}
    >
      {renderCouponCode()}
    </Tooltip>
  ) : (
    renderCouponCode()
  );
};

export default memo(SelectCouponCode);
