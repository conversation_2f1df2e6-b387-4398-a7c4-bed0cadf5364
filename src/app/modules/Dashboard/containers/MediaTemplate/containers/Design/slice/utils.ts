/* eslint-disable no-loop-func */
// Libraries
import {
  has,
  cloneDeep,
  get,
  mergeWith,
  isArray,
  isEmpty,
  uniq,
  set,
  unionWith,
  pick,
  omit,
  uniqBy,
  merge,
} from 'lodash';

// Query client
import { queryClient } from 'index';

// Constants
import {
  ATTRIBUTE_STATUS,
  BO_STATUS,
  COLLECTION_STATUS,
  OBJECT_TYPE,
  DYNAMIC_CONTENT_TYPE,
  PREFIX_EL_NAME,
  STANDARDS_BLOCKS,
  VIEW_PAGE,
  DESIGN_TEMPLATE_MODE,
  LAYOUT_TEMPLATE,
} from '../constants';
import { FALLBACK_SELECTION } from '../components/organisms/SidePanel/components/organisms/BlockEditing/Settings/Basic/constants';
import { QUERY_KEYS } from 'constants/queries';
import {
  OPERATORS_OPTION,
  SEGMENT_IDS,
  VALUE_TYPE,
} from '../components/organisms/SidePanel/components/organisms/FilterSetting/constants';
import { APP_CONFIG } from 'constants/appConfig';
import {
  FIELDS_SETTING,
  CUSTOM_FIELD,
} from '../components/organisms/SidePanel/components/organisms/FormFieldsSetting/constants';

// Config
import {
  DATA_MIGRATE,
  GET_TOP_RANKING_DEFAULT,
  JOURNEY_SETTINGS_DEFAULT,
  MIGRATE_BLOCK_SETTINGS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

// Types
import {
  MediaTemplateDesignState,
  TBuildRelationShip,
  TElement,
  TGetCloneBlocksPayload,
  TRemoveBlocks,
  TViewPage,
  TField,
} from './types';
import { TBlock, TElementAction, TWorkspace, TDataPayload } from './types';
import { TDynamic, TSettings, TTableSettings } from '../types';
import { TWheelItem } from '../components/organisms/SidePanel/components/organisms/BlockEditing/CouponWheel/Content';

// Utils
import { flattenTree, random } from 'app/utils/common';
import { getTranslateMessage } from 'utils/messages';
import { translations } from 'locales/translations';
import { handleError } from 'app/utils/handleError';
import { getTableMissingDataLabelDisplay, buildPromotionSectionsData } from '../components/organisms/Workspace/utils';

// Services
import {
  getListAttributes,
  getListAttributesBO,
  getListBusinessObject,
  getListCollectionsBO,
} from 'app/services/MediaTemplateDesign/BusinessObject';
import { initGroup } from '../components/organisms/SidePanel/components/organisms/ContentSources/utils';
import { getDynamicContentAttributes } from 'app/services/MediaTemplateDesign/DynamicContentAttribute';
import {
  regexCSType,
  serializeCSSelectedString,
} from '../components/organisms/SidePanel/components/organisms/AddDynamicContent/utils';
import { DISPLAY_CONDITION } from '../components/organisms/SidePanel/components/organisms/DisplayCondition/constants';

import { generateInternalCode } from '../components/organisms/SidePanel/components/organisms/BlockEditing/CouponWheel/utils';
import { checkPermissionSevices } from 'app/services/CheckPermission';

// Actions
import { mediaTemplateDesignActions as actions } from '.';
import { ACTIONS } from './sagaActions';
import { TRACKING_ITEM_TYPE_IDS } from 'constants/variables';

const {
  COLUMN,
  IMAGE,
  BUTTON,
  VIDEO,
  RATING,
  YES_NO,
  TEXT,
  OPTIN_FIELDS,
  TABLE,
  COUNT_DOWN,
  COUPON_WHEEL,
  SHAKE_AND_WIN,
  SURPRISE_TREASURE_HUNT,
} = STANDARDS_BLOCKS;

const PATH = 'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils.ts';

export const getCurrentPageIdx = (state: MediaTemplateDesignState) => {
  return state.workspace.viewPages.findIndex(viewPage => viewPage.id === state.toolbar.viewPageSelected);
};

// getPositionBlock current viewPages
export const getPositionBlock = (
  blockId: string | number,
  _blockType: string,
  state: MediaTemplateDesignState,
  viewPageIdx?: string,
) => {
  const { workspace } = state;

  let position = {
    rowIndex: -1,
    colIndex: -1,
    elIndex: -1,
    slideIndex: -1,
    slideElIndex: -1,
  };

  const currentPageIdx = typeof viewPageIdx !== 'undefined' ? +viewPageIdx : +getCurrentPageIdx(state);

  if (currentPageIdx !== -1) {
    for (const rowIdx in workspace.viewPages[currentPageIdx].rows) {
      const row = workspace.viewPages[currentPageIdx].rows[rowIdx];
      position.rowIndex = +rowIdx;
      if (row.id === blockId) return position;

      for (const colIdx in row.columns) {
        const col = row.columns[colIdx];
        position.colIndex = +colIdx;
        if (col.id === blockId) return position;

        for (const elIdx in col.elements) {
          const el = col.elements[elIdx];
          position.elIndex = +elIdx;
          if (el.id === blockId) return position;

          if (get(el, 'slides', []).length > 0) {
            for (const slideIdx in el.slides) {
              const slide = el.slides[slideIdx];
              position.slideIndex = +slideIdx;
              if (slide.id === blockId) return position;

              for (const slideElIdx in slide.elements) {
                const slideEl = slide.elements[slideElIdx];
                position.slideElIndex = +slideElIdx;
                if (slideEl.id === blockId) return position;
              }
              position.slideElIndex = -1;
            }
            position.slideIndex = -1;
          }
        }
        position.elIndex = -1;
      }
      position.colIndex = -1;
    }
    position.rowIndex = -1;
  }

  return {
    rowIndex: -1,
    colIndex: -1,
    elIndex: -1,
    slideIndex: -1,
    slideElIndex: -1,
  };
};

// getPositionBlock any viewPages
export const getPositionBlockViews = (blockId, blockType, state) => {
  const { workspace } = state;

  let position = {
    viewIndex: -1,
    rowIndex: -1,
    colIndex: -1,
    elIndex: -1,
    slideIndex: -1,
    slideElIndex: -1,
  };

  for (const viewIdx in workspace.viewPages) {
    const foundPositionBlock = getPositionBlock(blockId, blockType, state, viewIdx);

    if (foundPositionBlock.rowIndex !== -1) {
      position = {
        viewIndex: +viewIdx,
        ...getPositionBlock(blockId, blockType, state, viewIdx),
      } as any;

      break;
    }
  }

  return position;
};

export const getPositionBlockSelected = (state: MediaTemplateDesignState) => {
  const { sidePanel } = state;

  return getPositionBlock(sidePanel.blockSelectedId, sidePanel.type, state);
};

export const getBlockSelected = (state: MediaTemplateDesignState) => {
  const currentPageIdx = getCurrentPageIdx(state);

  const { colIndex, rowIndex, elIndex, slideIndex, slideElIndex } = getPositionBlockSelected(state);

  if (rowIndex !== -1) {
    if (colIndex !== -1 && elIndex !== -1) {
      if (slideIndex !== -1 && slideElIndex !== -1) {
        return get(
          state,
          `workspace.viewPages[${currentPageIdx}].rows[${rowIndex}].columns[${colIndex}].elements[${elIndex}].slides[${slideIndex}].elements[${slideElIndex}]`,
          {},
        ) as TElement;
      }

      return state.workspace.viewPages[currentPageIdx].rows[rowIndex].columns[colIndex].elements[elIndex];
    }

    return state.workspace.viewPages[currentPageIdx].rows[rowIndex];
  }

  return null;
};

export const getBlockById = (blockId: string, blockType: string, state: MediaTemplateDesignState) => {
  const currentPageIdx = getCurrentPageIdx(state);

  const { colIndex, rowIndex, elIndex, slideIndex, slideElIndex } = getPositionBlock(blockId, blockType, state);

  if (rowIndex !== -1) {
    if (colIndex !== -1 && elIndex !== -1) {
      if (slideIndex !== -1 && slideElIndex !== -1) {
        return get(
          state,
          `workspace.viewPages[${currentPageIdx}].rows[${rowIndex}].columns[${colIndex}].elements[${elIndex}].slides[${slideIndex}].elements[${slideElIndex}]`,
          {},
        );
      }

      return state.workspace.viewPages[currentPageIdx].rows[rowIndex].columns[colIndex].elements[elIndex];
    }

    return state.workspace.viewPages[currentPageIdx].rows[rowIndex];
  }

  return null;
};

export const getAllBlockFromView = (viewPage?: TViewPage) => {
  if (viewPage) {
    return Object.values(viewPage.blocks || {});
  }

  return [];
};

export const getAllBlocksFromAllViews = (viewPages: any) => {
  const blocks: TBlock[] = [];

  if (viewPages) {
    for (const viewPage of viewPages) {
      const viewBlocks = getAllBlockFromView(viewPage);
      blocks.push(...viewBlocks);
    }
  }

  return blocks;
};

export const getBlocksUseBO = ({
  listBlocksChecking,
  groupBoId,
}: {
  listBlocksChecking: TBlock[];
  groupBoId: string;
}) => {
  const blocks: TBlock[] = [];

  for (const block of listBlocksChecking) {
    let isUsedBo = false;

    switch (block.type) {
      case STANDARDS_BLOCKS.TEXT.name:
        const textDynamicData: any[] = Object.values(get(block, 'settings.dynamic.data', {}));
        const textDynamicLinkData: any[] = Object.values(get(block, 'settings.link.data', {}));

        const checkType = (type: any) => serializeCSSelectedString(type).groupId === groupBoId;

        if (
          textDynamicData.some(i => checkType(i.type)) ||
          textDynamicLinkData.some(i => i.linkType === 'dynamic' && checkType(i.type))
        ) {
          isUsedBo = true;
        }

        break;
      case STANDARDS_BLOCKS.YES_NO.name:
        const yesDynamics: any[] = Object.values(get(block, 'settings.yesDynamic', {}));
        const noDynamics: any[] = Object.values(get(block, 'settings.noDynamic', {}));

        // KHANG NOTE
        if (yesDynamics.some(({ isDynamic }) => isDynamic) || noDynamics.some(({ isDynamic }) => isDynamic)) {
          isUsedBo = true;
        }
        break;
      default:
        const dynamics: TDynamic[] = Object.values(get(block, 'settings.dynamic', {}));
        for (const { isDynamic, type } of dynamics) {
          if (isDynamic && regexCSType.test(type) && serializeCSSelectedString(type).groupId === groupBoId) {
            isUsedBo = true;
            break;
          }
        }
        break;
    }

    const pathDisplayCondition = 'settings.blockStylesSettings.displayCondition';

    if (has(block, pathDisplayCondition)) {
      const displayCondition = get(block, pathDisplayCondition);
      const { type, condition } = displayCondition;

      if (
        condition !== DISPLAY_CONDITION.NONE.value &&
        regexCSType.test(type) &&
        serializeCSSelectedString(type).groupId === groupBoId
      ) {
        blocks.push(block);
      }
    }

    if (isUsedBo) blocks.push(block);
  }

  return blocks;
};

const buildElements = (blocks: TBlock[], namespace: string, pageId: string, data: TDataPayload) => {
  if (!blocks || !blocks.length) {
    return [];
  }

  const { promotionPool = { rows: [] } } = data;

  let builtElements: TElementAction[] = [];

  try {
    blocks.forEach(block => {
      let element: TElementAction = {
        element_id: block.id,
        element_type: block.type,
        selector: '',
        type: '',
        pageId,
      };

      let isDone = false;

      switch (block.type) {
        case STANDARDS_BLOCKS.BUTTON.name:
          element = {
            ...element,
            ...block.settings?.actions?.ButtonElement,
            element_id: `${namespace}-${STANDARDS_BLOCKS.BUTTON.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.BUTTON.actionKey}--${block.id}`,
          };
          break;
        case STANDARDS_BLOCKS.OPTIN_FIELDS.name: {
          const draftVerifiedSubmit = cloneDeep(get(block.settings, 'verifiedSubmit', {}));
          const { conditions } = draftVerifiedSubmit;

          // Reset conditions to empty if conditions just have default condition
          if (
            conditions?.OR?.length === 1 &&
            conditions?.OR[0]?.AND?.length === 1 &&
            !conditions?.OR[0]?.AND[0]?.field
          ) {
            draftVerifiedSubmit.conditions = { OR: [{ AND: [] }] };
          }

          let actionSetting = block.settings?.actions?.FieldsElementButton;
          if (actionSetting?.providerInfo?.destinationSetting) {
            const newActionSetting = cloneDeep(actionSetting);
            delete newActionSetting.providerInfo;

            newActionSetting.providerInfo = {
              catalogId: actionSetting.providerInfo.catalogId,
              destinationId: actionSetting.providerInfo.destinationId,
              destinationSetting: {
                otpLength: actionSetting.providerInfo.destinationSetting.otpLength,
                expirationTime: actionSetting.providerInfo.destinationSetting.expirationTime,
              },
            };

            actionSetting = newActionSetting;
          }

          element = {
            ...element,
            ...actionSetting,
            syncData: block.settings?.syncData,
            limitedSubmit: block.settings?.limitedSubmit,
            verifiedSubmit: draftVerifiedSubmit,
            errorPosition: block.settings?.errorStylesSettings?.errorPosition,
            element_id: `${namespace}-${STANDARDS_BLOCKS.OPTIN_FIELDS.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.OPTIN_FIELDS.actionKey}--${block.id}`,
          };
          break;
        }
        case STANDARDS_BLOCKS.OTP_VERIFICATION.name: {
          element = {
            ...element,
            ...block.settings?.actions?.OTPElementButton,
            eventTrigger: block.settings?.eventTrigger,
            element_id: `${namespace}-${STANDARDS_BLOCKS.OTP_VERIFICATION.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.OTP_VERIFICATION.actionKey}--${block.id}`,
          };
          break;
        }
        case STANDARDS_BLOCKS.COUPON_WHEEL.name:
          element = {
            ...element,
            ...block.settings?.actions?.CouponWheelElement,
            element_id: `${namespace}-${STANDARDS_BLOCKS.COUPON_WHEEL.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.COUPON_WHEEL.actionKey}--${block.id}`,
            sections: buildPromotionSectionsData(block.settings?.sections, promotionPool, true),
          };
          break;
        case STANDARDS_BLOCKS.SHAKE_AND_WIN.name: {
          const { triggerSettings, timeToShake, translationTime, shakeType, imageToShake, uploadedImage } =
            block.settings;
          element = {
            ...element,
            ...block.settings?.actions?.ShakeAndWinElement,
            config: {
              timeToShake,
              translationTime,
              shakeType,
              imageToShake: imageToShake.type === 'other-image' && imageToShake.otherUrl,
              imageBefore: uploadedImage.previewUrlBefore,
              imageAfter: uploadedImage.previewUrlAfter,
              triggerSettings,
            },
            element_id: `${namespace}-${STANDARDS_BLOCKS.SHAKE_AND_WIN.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.SHAKE_AND_WIN.actionKey}--${block.id}`,
            sections: buildPromotionSectionsData(block.settings?.sections, promotionPool, true),
          };
          break;
        }
        case STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.name:
          const { translationTime, animationClicked, animationHover, dimensions, metrics, cellImages } = block.settings;
          element = {
            ...element,
            ...block.settings?.actions?.SurpriseTreasureHuntElement,
            config: {
              translationTime,
              animationClicked,
              animationHover,
              dimensions,
              metrics,
              cellImages: {
                imageAllUrlAfter: (cellImages.applyImageAllAfter && cellImages.imageAllUrlAfter) || '',
                imageAllUrlBefore: (cellImages.applyImageAllBefore && cellImages.imageAllUrlBefore) || '',
                images:
                  !cellImages.applyImageAllAfter || !cellImages.applyImageAllBefore ? cellImages.imageDynamic : null,
              },
            },
            element_id: `${namespace}-${STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.actionKey}--${block.id}`,
            sections: buildPromotionSectionsData(block.settings?.sections, promotionPool, true),
          };
          break;
        case STANDARDS_BLOCKS.COUNT_DOWN.name:
          element = {
            ...element,
            ...block.settings?.actions?.CountdownElement,
            element_id: `${namespace}-${STANDARDS_BLOCKS.COUNT_DOWN.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.COUNT_DOWN.actionKey}--${block.id}`,
          };
          break;
        case STANDARDS_BLOCKS.YES_NO.name:
          builtElements.push({
            ...element,
            ...block.settings?.actions?.NoButtonElement,
            element_id: `${namespace}-${STANDARDS_BLOCKS.YES_NO.actionKey.no}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.YES_NO.actionKey.no}--${block.id}`,
          });

          element = {
            ...element,
            ...block.settings?.actions?.YesButtonElement,
            element_id: `${namespace}-${STANDARDS_BLOCKS.YES_NO.actionKey.yes}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.YES_NO.actionKey.yes}--${block.id}`,
          };
          break;
        case STANDARDS_BLOCKS.SLIDE_SHOW.name:
          const {
            editor,
            autoSlide,
            columnGap,
            component,
            skipItems,
            slideLoop,
            slideDelay,
            totalItems,
            displayItems,
            displayStyle,
            slideDuration,
            slideDirection,
            slideTransition,
          } = block.settings;

          element = {
            ...element,
            ...block.settings?.actions?.SlideShowElement,
            config: {
              editor,
              autoSlide,
              columnGap,
              component,
              skipItems,
              slideLoop,
              slideDelay,
              totalItems,
              displayItems,
              displayStyle,
              slideDuration,
              slideDirection,
              slideTransition,
            },
            element_id: `${namespace}-${STANDARDS_BLOCKS.SLIDE_SHOW.actionKey}--${block.id}`,
            selector: `#${namespace}-${STANDARDS_BLOCKS.SLIDE_SHOW.actionKey}--${block.id}`,
          };

          break;
        case STANDARDS_BLOCKS.TEXT.name:
          element = {
            ...element,
            element_id: `${namespace}-${block.settings.component}--wrapper--${block.id}`,
            selector: `#${namespace}-${block.settings.component}--wrapper--${block.id}`,
            dynamic: {
              textData: block.settings?.dynamic?.data,
              linkData: block.settings?.link?.data,
            },
          };

          if (!block.settings?.dynamic?.data && !block.settings?.link?.data) {
            isDone = true;
          }
          break;
      }

      !isDone && element.selector && builtElements.push(element);
    });
  } catch (error) {}

  return builtElements;
};

const buildBOTableSettings = (viewPages: TViewPage[]) => {
  if (!viewPages || !viewPages.length) return {};

  const result = {};

  viewPages.forEach(page => {
    const { blocks } = page;

    Object.entries({ ...blocks }).forEach(([_blockId, block]) => {
      if (block.type === STANDARDS_BLOCKS.TABLE.name) {
        const tableBlockSettings = block.settings as TTableSettings;

        const { table, tableBody, sortBy, columns } = tableBlockSettings;

        result[_blockId] = {
          showMissingDataType: getTableMissingDataLabelDisplay(tableBody.settings.showMissingDataType),
          itemTypeId: tableBlockSettings.boId,
          filters: tableBlockSettings.filters,
          sort: sortBy.columnId,
          direction: sortBy.order,
          showTop: get(table, 'settings.showTop', 10),
          rowStyles: [
            `style='background-color: ${table.settings.oddRowColor}'`,
            `style='background-color: ${table.settings.evenRowColor}'`,
          ],
          columns: columns
            .filter(({ colType }) => colType !== 'index')
            .map(col => pick(col, ['dataType', 'colType', 'value', 'displayFormat'])),
        };
      }
    });
  });

  return result;
};

const buildActions = (viewPages: TViewPage[], namespace: string, data: TDataPayload) => {
  if (!viewPages || !viewPages.length) {
    return [];
  }
  let elementActions: TElementAction[] = [];

  viewPages.forEach(page => {
    elementActions.push(...buildElements(Object.values(page.blocks || {}), namespace, page.id, data));
  });

  return elementActions;
};

const calculateMaxIndex = viewPages => {
  if (viewPages && viewPages.length) {
    let dynamicIndexItems: number[] = [];

    viewPages.forEach(view => {
      if (view.html) {
        const matchIters = view.html.matchAll(
          new RegExp(`${APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_DYNAMIC_INDEX}=\\"([\\d,]+)\\"`, 'g'),
        );
        while (1) {
          const indexValue = matchIters.next();
          if (!indexValue.value || indexValue.done) {
            break;
          }

          dynamicIndexItems = dynamicIndexItems.concat(indexValue.value[1].split(','));
        }
      }
    });

    return Math.max(0, ...dynamicIndexItems);
  }

  return Math.max(
    ...Array.from(document.querySelectorAll(`[${APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_DYNAMIC_INDEX}]`))
      .map(item => (item as HTMLElement).dataset.dynmicIndex)
      .join(',')
      .split(',')
      .map(item => +item),
  );
};

const getMaxIndexContentSource = ({ viewPages, groupId }) => {
  let arrayDynamicIndex: number[] = [0];

  if (viewPages && viewPages.length) {
    for (const key in viewPages) {
      const page = viewPages[key];

      const { blocks } = page || {};

      for (let blockKey in blocks) {
        const { type, settings } = blocks[blockKey] || {};

        switch (type) {
          case IMAGE.name:
          case BUTTON.name:
          case VIDEO.name:
          case OPTIN_FIELDS.name:
          case RATING.name: {
            const { dynamic } = settings || {};

            for (const key in dynamic) {
              const dynamicItem = dynamic[key];

              const { attribute = {}, type, index, isDynamic } = dynamicItem || {};
              const { groupId: matchGroupId } = serializeCSSelectedString(type);

              switch (true) {
                case regexCSType.test(type): {
                  if (groupId === matchGroupId && isDynamic && !!attribute.value) {
                    arrayDynamicIndex.push(index);
                  }
                  break;
                }
                default:
                  break;
              }
            }

            break;
          }
          case YES_NO.name: {
            const { yesDynamic, noDynamic } = settings || {};

            const dynamics = [{ ...yesDynamic }, { ...noDynamic }];

            for (const dynamic of dynamics) {
              for (const key in dynamic) {
                const dynamicItem = dynamic[key];
                const { attribute = {}, type, isDynamic, index } = dynamicItem || {};
                const { groupId: matchGroupId } = serializeCSSelectedString(type);

                switch (true) {
                  case regexCSType.test(type): {
                    if (!!attribute.value && isDynamic && groupId === matchGroupId) {
                      arrayDynamicIndex.push(index);
                    }
                    break;
                  }
                  default:
                    break;
                }
              }
            }

            break;
          }
          case TEXT.name: {
            const dynamicData = get(settings, 'dynamic.data', {});
            const linkDynamicData = get(settings, 'link.data', {});

            const dynamics = [{ ...dynamicData }, { ...linkDynamicData }];

            for (const dynamicData of dynamics) {
              for (const key in dynamicData) {
                const dynamicInfo = dynamicData[key];
                const { attribute = {}, type, index } = dynamicInfo || {};
                const { groupId: matchGroupId } = serializeCSSelectedString(type);

                switch (true) {
                  case regexCSType.test(type):
                    if (!!attribute.value && groupId === matchGroupId) {
                      arrayDynamicIndex.push(index);
                    }
                    break;
                  default:
                    break;
                }
              }
            }

            break;
          }
          default:
            break;
        }
      }
    }
  }

  return Math.max(...arrayDynamicIndex);
};

export const getFonts = (viewPages: TViewPage[]) => {
  if (!viewPages || !viewPages.length) {
    return [];
  }

  const customizeMerge = (objValue: any, srcValue: any) => {
    if (isArray(objValue)) {
      return uniq(objValue.concat(srcValue));
    }
  };

  let fonts: Record<string, Array<string | number>> = {};

  try {
    viewPages.forEach(page => {
      Object.keys(page.blocks || {}).forEach(blockKey => {
        const block = (page.blocks || {})[blockKey];

        if (block) {
          const { type, settings } = block;

          switch (type) {
            case STANDARDS_BLOCKS.TEXT.name:
              const textEl = document.createElement('div');
              textEl.innerHTML = block.settings.rawHTML;

              textEl.querySelectorAll('[style*="font-family"]')?.forEach(el => {
                const font = {};
                const fontFamily = (el as any).style.fontFamily?.replace(/\"/g, '');

                font[fontFamily] = [400];

                mergeWith(fonts, font, customizeMerge);

                if (!!(el as any).style.fontWeight) {
                  font[fontFamily] = [+(el as any).style.fontWeight];

                  mergeWith(fonts, font, customizeMerge);
                } else {
                  const fontWeightChildEls = el.querySelectorAll('[style*="font-weight"]');

                  fontWeightChildEls.forEach(childEl => {
                    font[fontFamily] = [+(childEl as any).style.fontWeight];

                    mergeWith(fonts, font, customizeMerge);
                  });
                }
              });

              break;
            case STANDARDS_BLOCKS.BUTTON.name:
              const buttonFonts = {
                [get(settings, 'buttonStyles.fontFamily')]: [get(settings, 'buttonStyles.fontWeight')],
              };
              mergeWith(fonts, buttonFonts, customizeMerge);
              break;
            case STANDARDS_BLOCKS.OPTIN_FIELDS.name:
              const fieldFonts = {
                [get(settings, 'styles.fontFamily')]: [+get(settings, 'styles.fontWeight')],
              };
              const submitBtnFonts = {
                [get(settings, 'buttonStyles.fontFamily')]: [+get(settings, 'buttonStyles.fontWeight')],
              };

              const privacyText = get(settings, 'fields.privacyText', {});

              // If privacy text is set, add it to fonts
              if (privacyText.order !== -1) {
                const privacyTextEl = document.createElement('div');
                privacyTextEl.innerHTML = privacyText.rawText;

                privacyTextEl.querySelectorAll('[style*="font-family"]')?.forEach(el => {
                  const font = {};
                  const fontFamily = (el as any).style.fontFamily?.split(',')[0].replace(/\"/g, '');

                  font[fontFamily] = [400];

                  mergeWith(fonts, font, customizeMerge);

                  if (!!(el as any).style.fontWeight) {
                    font[fontFamily] = [+(el as any).style.fontWeight];

                    mergeWith(fonts, font, customizeMerge);
                  } else {
                    const fontWeightChildEls = el.querySelectorAll('[style*="font-weight"]');

                    fontWeightChildEls.forEach(childEl => {
                      font[fontFamily] = [+(childEl as any).style.fontWeight];

                      mergeWith(fonts, font, customizeMerge);
                    });
                  }
                });
              }

              mergeWith(fonts, fieldFonts, customizeMerge);
              mergeWith(fonts, submitBtnFonts, customizeMerge);

              break;
            case STANDARDS_BLOCKS.YES_NO.name:
              const yesBtnFonts = {
                [get(settings, 'yesButtonStyles.fontFamily')]: [+get(settings, 'yesButtonStyles.fontWeight')],
              };
              const noBtnFonts = {
                [get(settings, 'noButtonStyles.fontFamily')]: [+get(settings, 'noButtonStyles.fontWeight')],
              };

              mergeWith(fonts, yesBtnFonts, customizeMerge);
              mergeWith(fonts, noBtnFonts, customizeMerge);
              break;
            case STANDARDS_BLOCKS.COUNT_DOWN.name:
              const numberFonts = {
                [get(settings, 'numberStyles.fontFamily')]: [+get(settings, 'numberStyles.fontWeight')],
              };
              const unitFonts = {
                [get(settings, 'unitStyles.fontFamily')]: [+get(settings, 'unitStyles.fontWeight')],
              };

              mergeWith(fonts, numberFonts, customizeMerge);
              mergeWith(fonts, unitFonts, customizeMerge);
              break;
            case STANDARDS_BLOCKS.COUPON_WHEEL.name:
              const labelFonts = {
                [get(settings, 'labelStyles.fontFamily')]: [+get(settings, 'labelStyles.fontWeight')],
              };

              mergeWith(fonts, labelFonts, customizeMerge);
              break;
            default:
              break;
          }
        }
      });
    });
  } catch (error) {}

  return fonts;
};

const buildObjectRelationships = (workspace: TWorkspace) => {
  try {
    const objectRelationships: Record<string, any>[] = [];
    const viewPages = workspace?.viewPages || [];
    const { itemTypeId } = workspace?.boSettings || {};

    const buildObjectRelationShip = ({
      itemTypeId,
      objectName = null,
      objectType,
      objectPropertyName,
    }: TBuildRelationShip) => ({
      object_id: itemTypeId,
      object_type: objectType || 1,
      object_name: objectName,
      object_property_name: objectPropertyName,
    });

    viewPages.forEach(viewPage => {
      const { blocks } = viewPage || {};

      Object.values({ ...blocks }).forEach(block => {
        const { type, settings } = block || {};

        // Get info from display condition
        const displayCondition = get(settings, 'blockStylesSettings.displayCondition');
        const { condition, field, event_metadata, operator, value } = displayCondition || {};

        if (!!condition) {
          if (!!field) {
            objectRelationships.push(
              buildObjectRelationShip({
                itemTypeId,
                objectType: OBJECT_TYPE.ITEM_PROPERTY,
                objectPropertyName: field,
              }),
            );
          }

          if (
            [OPERATORS_OPTION.MATCHES.value, OPERATORS_OPTION.NOT_MATCHES.value].includes(operator || '') &&
            field === SEGMENT_IDS
          ) {
            if (Array.isArray(value)) {
              value.forEach(val => {
                objectRelationships.push(
                  buildObjectRelationShip({
                    itemTypeId: val,
                    objectType: OBJECT_TYPE.SEGMENT,
                    objectPropertyName: null,
                  }),
                );
              });
            }
          }

          if (!!event_metadata) {
            const { useBo, field_code_bo, item_type_id, item_property_name } = event_metadata;

            if (!!useBo && !!field_code_bo) {
              objectRelationships.push(
                buildObjectRelationShip({
                  itemTypeId,
                  objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  objectPropertyName: field_code_bo,
                }),
              );
            }

            if (!useBo && !!item_property_name) {
              objectRelationships.push(
                buildObjectRelationShip({
                  itemTypeId: item_type_id,
                  objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  objectPropertyName: item_property_name,
                }),
              );
            }
          }
        }

        switch (type) {
          case IMAGE.name:
          case BUTTON.name:
          case VIDEO.name:
          case RATING.name:
            const { dynamic } = settings || {};

            Object.values({ ...dynamic }).forEach((dynamicItem: any) => {
              const { isDynamic, field } = dynamicItem || {};

              if (!!isDynamic && !!field) {
                objectRelationships.push(
                  buildObjectRelationShip({
                    itemTypeId,
                    objectType: OBJECT_TYPE.ITEM_PROPERTY,
                    objectPropertyName: field,
                  }),
                );
              }
            });

            break;
          case YES_NO.name:
            const { yesDynamic, noDynamic } = settings || {};

            [{ ...yesDynamic }, { ...noDynamic }].forEach(dynamic => {
              Object.values({ ...dynamic }).forEach((dynamicItem: any) => {
                const { isDynamic, field } = dynamicItem || {};

                if (!!isDynamic && !!field) {
                  objectRelationships.push(
                    buildObjectRelationShip({
                      itemTypeId,
                      objectType: OBJECT_TYPE.ITEM_PROPERTY,
                      objectPropertyName: field,
                    }),
                  );
                }
              });
            });

            break;
          case TEXT.name:
            const dynamicData = get(settings, 'dynamic.data', {});
            const linkDynamicData = get(settings, 'link.data', {});

            [{ ...dynamicData }, { ...linkDynamicData }].forEach(dynamicData => {
              Object.values({ ...dynamicData }).forEach((dynamicInfo: any) => {
                const { attribute = {}, type } = dynamicInfo || {};

                switch (true) {
                  case type === DYNAMIC_CONTENT_TYPE.BO_SETTINGS.value:
                    if (!!attribute.value) {
                      objectRelationships.push(
                        buildObjectRelationShip({
                          itemTypeId,
                          objectType: OBJECT_TYPE.ITEM_PROPERTY,
                          objectPropertyName: attribute.value,
                        }),
                      );
                    }
                    break;
                  case type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
                    if (!!attribute.value) {
                      objectRelationships.push(
                        buildObjectRelationShip({
                          itemTypeId: -100,
                          objectType: OBJECT_TYPE.ITEM_PROPERTY,
                          objectPropertyName: attribute.value,
                        }),
                      );
                    }
                    break;
                  case type === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
                    if (!!attribute.value) {
                      objectRelationships.push(
                        buildObjectRelationShip({
                          itemTypeId: -1007,
                          objectType: OBJECT_TYPE.ITEM_PROPERTY,
                          objectPropertyName: attribute.value,
                        }),
                      );
                    }
                    break;
                  case type === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
                    if (!!attribute.value) {
                      objectRelationships.push(
                        buildObjectRelationShip({
                          itemTypeId: -1003,
                          objectType: OBJECT_TYPE.ITEM_PROPERTY,
                          objectPropertyName: attribute.value,
                        }),
                      );
                    }
                    break;
                  case type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
                    if (!!attribute.propertyName) {
                      objectRelationships.push(
                        buildObjectRelationShip({
                          itemTypeId: attribute.itemTypeId,
                          objectType: OBJECT_TYPE.ITEM_PROPERTY,
                          objectPropertyName: attribute.propertyName,
                        }),
                      );
                    }
                    break;
                  default:
                    break;
                }
              });
            });

            break;
          default:
            break;
        }
      });
    }); //}}}

    const csGroups = workspace?.contentSources.groups || [];

    csGroups.map(async g => {
      const { filters, ranking, itemTypeId } = g;
      const rankingValues = get(ranking, 'algorithms.value', []);

      // Add Ranking if chosen
      rankingValues.forEach((rankingValue: any = {}) => {
        const { value, by } = rankingValue || {};

        switch (value) {
          case 'viral_products':
            if (!!by) {
              objectRelationships.push(
                buildObjectRelationShip({
                  itemTypeId: by,
                  objectType: OBJECT_TYPE.SEGMENT,
                  objectPropertyName: null,
                }),
              );
            }
            break;
          case 'distance':
            if (!!by) {
              objectRelationships.push(
                buildObjectRelationShip({
                  itemTypeId,
                  objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  objectPropertyName: by,
                }),
              );
            }

            if (!!rankingValue?.with) {
              objectRelationships.push(
                buildObjectRelationShip({
                  itemTypeId: get(rankingValue, 'with.item_type_id'),
                  objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  objectPropertyName: get(rankingValue, 'with.item_property_name'),
                }),
              );
            }

            break;
          case 'retarget_search':
            if (Array.isArray(by) && by.length) {
              by.forEach(propertyName => {
                objectRelationships.push(
                  buildObjectRelationShip({
                    itemTypeId,
                    objectType: OBJECT_TYPE.ITEM_PROPERTY,
                    objectPropertyName: propertyName,
                  }),
                );
              });
            }

            break;

          default:
            break;
        }
      });

      // Add filter if chosen
      for (const orIndex in filters.OR) {
        const orFilter = filters.OR[orIndex];

        for (const andIndex in orFilter.AND) {
          const andFilter = orFilter.AND[andIndex];

          const { column, value_type, operator, event_metadata, visitor_metadata, customer_metadata, value } =
            andFilter || {};

          if (!!column) {
            objectRelationships.push(
              buildObjectRelationShip({
                itemTypeId,
                objectType: OBJECT_TYPE.ITEM_PROPERTY,
                objectPropertyName: column,
              }),
            );
          }

          if (
            [OPERATORS_OPTION.MATCHES.value, OPERATORS_OPTION.NOT_MATCHES.value].includes(operator || '') &&
            column === SEGMENT_IDS
          ) {
            if (Array.isArray(value)) {
              value.forEach(val => {
                objectRelationships.push(
                  buildObjectRelationShip({
                    itemTypeId: val,
                    objectType: OBJECT_TYPE.SEGMENT,
                    objectPropertyName: null,
                  }),
                );
              });
            }
          }

          if (value_type === VALUE_TYPE.CUSTOMER.value && customer_metadata?.item_property_name) {
            objectRelationships.push(
              buildObjectRelationShip({
                itemTypeId: VALUE_TYPE.CUSTOMER.id,
                objectType: OBJECT_TYPE.ITEM_PROPERTY,
                objectPropertyName: customer_metadata.item_property_name,
              }),
            );
          }

          if (value_type === VALUE_TYPE.VISITOR.value && visitor_metadata?.item_property_name) {
            objectRelationships.push(
              buildObjectRelationShip({
                itemTypeId: VALUE_TYPE.VISITOR.id,
                objectType: OBJECT_TYPE.ITEM_PROPERTY,
                objectPropertyName: visitor_metadata.item_property_name,
              }),
            );
          }

          if (value_type === VALUE_TYPE.EVENT.value && event_metadata?.item_property_name) {
            objectRelationships.push(
              buildObjectRelationShip({
                itemTypeId: event_metadata.item_type_id,
                objectType: OBJECT_TYPE.ITEM_PROPERTY,
                objectPropertyName: event_metadata.item_property_name,
              }),
            );
          }
        }
      }

      // Add BO if chosen
      if (!!itemTypeId) {
        objectRelationships.push(
          buildObjectRelationShip({
            itemTypeId,
            objectType: OBJECT_TYPE.ITEM_PROPERTY,
            objectPropertyName: 'id',
          }),
        );
      }
    });

    return unionWith(
      objectRelationships,
      (a, b) => a.object_id === b.object_id && a.object_property_name === b.object_property_name,
    );
  } catch (error) {
    handleError(error, {
      path: PATH,
      name: 'buildObjectRelationships',
      args: { workspace },
    });
  }
};

export const getTemplateSetting = (workspace: TWorkspace, data: TDataPayload) => {
  const yesNoViewPage = workspace.viewPages.find(viewPage => VIEW_PAGE.YES_NO.name === viewPage.id);
  const isCodeMode = workspace.mode === DESIGN_TEMPLATE_MODE.CODE;
  let position, positionSettings;

  if (isCodeMode) {
    const type = workspace.template?.type;

    const { settings, viewPages } = workspace;

    if (type === LAYOUT_TEMPLATE.FLOATING_BAR.name) {
      const { floatingBarPosition, positionSettings: posSettings = {} } = settings || {};

      position = floatingBarPosition;
      positionSettings = posSettings;
    } else if (type === LAYOUT_TEMPLATE.SLIDE_IN.name) {
      const { position: pos, positionSettings: posSettings } = viewPages[0]?.settings?.slideClosedContainer || {};

      position = pos;
      positionSettings = posSettings;
    }
  }

  const result = {
    id: workspace.id,
    parent_id: null,
    name: workspace.name,
    type: workspace.template?.type,
    status: 'active',
    prefixEl: PREFIX_EL_NAME,
    namespace: workspace.namespace,
    fonts: getFonts(workspace.viewPages),
    enableFonts: true,
    updated_at: new Date().toISOString(),
    options: {
      ...workspace.settings,
      slidePosition: workspace.viewPages[0]?.settings?.slideClosedContainer?.position,
      slideDirection: workspace.viewPages[0]?.settings?.slideClosedContainer?.slideDirection,
      slideDisplayStyle: workspace.viewPages[0]?.settings?.slideClosedContainer?.displayStyle,
    },
    folders: [],
    actions: buildActions(workspace.viewPages, workspace.namespace, data),
    rulesets: [
      {
        name: 'Default Ruleset',
        actions: [
          {
            type: 'show-campaign',
            value: yesNoViewPage?.settings.isActive ? VIEW_PAGE.YES_NO.name : VIEW_PAGE.OPTIN.name,
          },
        ],
      },
    ],
    customJS: workspace.viewPages[0]?.settings?.customJS,
    // boSettings: {
    //   ...workspace.boSettings,
    //   maxIndex: calculateMaxIndex(workspace.viewPages),
    // },
    contentSources: workspace.contentSources.groups.map(g =>
      omit(
        { ...g, maxIndex: getMaxIndexContentSource({ viewPages: workspace.viewPages, groupId: g.groupId }) },
        'data',
      ),
    ),
    // utmTracking: workspace.utmSettings
    //   ? Object.entries(workspace.utmSettings).reduce(
    //       (result, cur) => ({
    //         ...result,
    //         [cur[0]]: cur[1].map(item => item.value).join(''),
    //       }),
    //       {},
    //     )
    //   : {},
    isExcludeDuplicate: workspace.contentSources.isExcludeDuplicate || false,
    objectRelationships: buildObjectRelationships(workspace),
    utmTracking: workspace.trackingModule?.data
      ? Object.entries(workspace.trackingModule.data).reduce(
          (result, cur) => ({
            ...result,
            [cur[0]]: cur[1].map(item => item.value).join(''),
          }),
          {},
        )
      : {},
    trackingModule: {
      mode: get(workspace, 'trackingModule.mode', 'utm'),
      data: workspace.trackingModule?.data
        ? Object.entries(workspace.trackingModule.data).reduce(
            (result, cur) => ({
              ...result,
              [cur[0]]: cur[1].map(item => item.value).join(''),
            }),
            {},
          )
        : {},
      isTrackingItem: workspace.trackingModule?.isTrackingItem || false,
    },
    boTableSettings: buildBOTableSettings(workspace.viewPages),
    codeModeSettings: isCodeMode
      ? {
          ...(workspace.viewPages[0]?.codeModeSettings || {}),
          position,
          positionSettings,
        }
      : undefined,
    isCodeMode,
  };

  return result;
};

// New version
/**
 * Extracts dynamic items from a collection of blocks.
 *
 * This function iterates through the provided blocks and retrieves dynamic data
 * based on the block type. It supports different block types such as `TEXT`,
 * `YES_NO`, and others, and collects their respective dynamic items.
 *
 * @param blocks - A record of blocks where the key is a string and the value is of type `TBlock`.
 * @returns An array of dynamic items extracted from the blocks.
 *
 * The function processes blocks as follows:
 * - For `TEXT` blocks, it extracts dynamic data from `settings.dynamic.data` and `settings.link.data`.
 * - For `YES_NO` blocks, it extracts dynamic data from `settings.yesDynamic` and `settings.noDynamic`.
 * - For other block types, it extracts dynamic data from `settings.dynamic` and filters items where `isDynamic` is true.
 */
export const getDynamicItemsFromBlocks = ({ blocks }: { blocks: TBlock[] }) => {
  let dynamicItems: Record<string, any>[] = [];

  Object.values(blocks).forEach(block => {
    const { type } = block;

    switch (type) {
      case STANDARDS_BLOCKS.TEXT.name: {
        const textDynamicData: any[] = Object.values(get(block, 'settings.dynamic.data', {}));
        const textDynamicLinkData: any[] = Object.values(get(block, 'settings.link.data', {}));

        dynamicItems = dynamicItems.concat(textDynamicData, textDynamicLinkData);

        break;
      }
      case STANDARDS_BLOCKS.YES_NO.name: {
        const yesDynamics: any[] = Object.values(get(block, 'settings.yesDynamic', {}));
        const noDynamics: any[] = Object.values(get(block, 'settings.noDynamic', {}));

        dynamicItems = dynamicItems.concat(yesDynamics, noDynamics);

        break;
      }

      default: {
        const dynamics = Object.values(get(block, 'settings.dynamic', {})) as any[];

        dynamicItems = dynamicItems.concat(dynamics.filter(({ isDynamic }) => isDynamic));

        break;
      }
    }
  });

  return dynamicItems;
};

export const getBlockSelectedV2 = (state: MediaTemplateDesignState) => {
  const { sidePanel } = state;
  return getBlockByIdV2(sidePanel.blockSelectedId.toString(), state);
};

export const getBlockByIdV2 = (blockId: string, state: MediaTemplateDesignState) => {
  const currentPageIdx = getCurrentPageIdx(state);
  const { blocks = {} } = state.workspace.viewPages[currentPageIdx];

  return blocks[blockId];
};

export const getCloneBlocks = ({
  blocks = {},
  tree = {},
  blockId = '',
  parentId = '',
  objectShakeReferral = {},
}: TGetCloneBlocksPayload) => {
  const children = tree[blockId] || [];
  let childrenBlocks: Record<string, TBlock> = {};
  const childrenTree: Record<string, string[]> = {};
  const cloneBlock = cloneDeep(blocks[blockId]);
  const cloneBlockId = random(20);
  objectShakeReferral[blockId] = cloneBlockId;
  cloneBlock.id = cloneBlockId;
  childrenBlocks[cloneBlock.id] = cloneBlock;

  if (parentId) {
    childrenTree[parentId] = [cloneBlock.id];
  }
  // Case Block is Column -> Create empty array
  if (['col', 'slide', 'group'].includes(blocks[blockId].type)) {
    childrenTree[cloneBlock.id] = [];
  }

  children.forEach(child => {
    const cloneBlocks = getCloneBlocks({ blocks, tree, blockId: child, parentId: cloneBlock.id, objectShakeReferral });

    Object.assign(childrenBlocks, cloneBlocks.childrenBlocks);

    mergeWith(childrenTree, cloneBlocks.childrenTree, (objValue, srcValue) => {
      if (Array.isArray(objValue)) {
        return objValue.concat(srcValue);
      }
    });
  });
  return { childrenBlocks, childrenTree, objectShakeReferral };
};

export const removeBlocks = ({ blocks = {}, tree = {}, blockId = '', parentId = '' }: TRemoveBlocks) => {
  const children = tree[blockId] || [];

  if (!blocks[blockId]) {
    return;
  }

  delete blocks[blockId];
  delete tree[blockId];

  // Remove block from parent
  if (parentId) {
    const childrenTree = tree[parentId] || [];
    const blockIdx = childrenTree?.indexOf(blockId);

    if (blockIdx !== -1) {
      childrenTree.splice(blockIdx, 1);
    }
  }

  // Remove block from children
  children.forEach(child => {
    removeBlocks({
      blocks,
      tree,
      blockId: child,
      parentId: parentId,
    });
  });
};

export const getAllChildrenBlockId = ({ tree = {}, blockId = '', isChild = false }) => {
  const children = tree[blockId] || [];
  const blockIds: string[] = [];

  if (isChild) {
    blockIds.push(blockId);
  }

  children.forEach((childId: string) => {
    const cloneBlocks = getAllChildrenBlockId({ tree, blockId: childId, isChild: true });

    blockIds.push(...cloneBlocks);
  });

  return blockIds;
};

export const buildMappingFields = (dynamicItem: any, isFormatNumber = false, key = '') => {
  if (!dynamicItem || !dynamicItem.type) {
    return '';
  }
  const { type, attribute, index, pool } = dynamicItem;

  if (attribute?.value) {
    const keySurfix = isFormatNumber ? `.format("${key}")` : '';
    switch (true) {
      case type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
        if (index != null) {
          return `event.${attribute.itemTypeName}[${index}].${attribute.propertyName}${keySurfix}`;
        }

        return `event.${attribute.value}${keySurfix}`;
      case type === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
        return `visitor.${attribute.value}${keySurfix}`;
      case type === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
        return `customer.${attribute.value}${keySurfix}`;
      case type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
        return pool ? `promotion_code.${pool}.${attribute.value}${keySurfix}` : '';
      case regexCSType.test(type): {
        const { groupId } = serializeCSSelectedString(type);
        return index || index === 0 ? `groups.${groupId}[${index}].${attribute.value}${keySurfix}` : '';
      }
      case type === DYNAMIC_CONTENT_TYPE.BO_SETTINGS.value:
        return index || index === 0 ? `items[${index}].${attribute.value}${keySurfix}` : '';
      case type === 'custom':
        return `#{custom.${attribute.value}}`;

      default:
        return '';
    }
  }
  return '';
};

export const buildMergeTag = (dynamicItem: any, isFormat = false, key = '') => {
  if (!dynamicItem || !dynamicItem.type) {
    return '';
  }
  const { type, attribute, index, pool } = dynamicItem;

  let dynamicValue = '';

  if (attribute?.value) {
    const keySurfix = isFormat ? `.format("${key}")` : '';

    switch (true) {
      case type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
        dynamicValue =
          index != null
            ? `#{event.${attribute.itemTypeName}[${index}].${attribute.propertyName}${keySurfix}}`
            : `#{event.${attribute.value}${keySurfix}}`;
        break;
      case type === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
        dynamicValue = `#{visitor.${attribute.value}${keySurfix}}`;
        break;
      case type === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
        dynamicValue = `#{customer.${attribute.value}${keySurfix}}`;
        break;
      case type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
        dynamicValue = pool ? `#{promotion_code.${pool}.${attribute.value}${keySurfix}}` : '';
        break;
      case type === 'custom':
        dynamicValue = `#{custom.${attribute.value}}`;
        break;
      case regexCSType.test(type): {
        const { groupId } = serializeCSSelectedString(type);
        dynamicValue = index || index === 0 ? `#{groups.${groupId}[${index}].${attribute.value}${keySurfix}}` : '';
        break;
      }
    }
  }

  return dynamicValue;
};
export const buildMergeTagV2 = (dynamicItem: any, isFormat = false, key = '') => {
  if (!dynamicItem || !dynamicItem.type) {
    return '';
  }
  const { type, attribute, index, pool } = dynamicItem;

  let dynamicValue = '';

  if (attribute?.value) {
    const keySurfix = isFormat ? `.format("${key}")` : '';

    switch (true) {
      case type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
        dynamicValue =
          index != null
            ? `@{event.${attribute.itemTypeName}[${index}].${attribute.propertyName}${keySurfix}}`
            : `@{event.${attribute.value}${keySurfix}}`;
        break;
      case type === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
        dynamicValue = `@{visitor.${attribute.value}${keySurfix}}`;
        break;
      case type === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
        dynamicValue = `@{customer.${attribute.value}${keySurfix}}`;
        break;
      case type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
        dynamicValue = pool ? `@{promotion_code.${pool}.${attribute.value}${keySurfix}}` : '';
        break;
      case type === 'custom':
        dynamicValue = `@{custom.${attribute.value}}`;
        break;
      case regexCSType.test(type): {
        const { groupId } = serializeCSSelectedString(type);
        dynamicValue = index || index === 0 ? `@{groups.${groupId}[${index}].${attribute.value}${keySurfix}}` : '';
        break;
      }
    }
  }

  return dynamicValue;
};

export const getDataTrackingItem = ({ blocks }: { blocks: TBlock[] }) => {
  // Filter dynamic items have content source match tracking item ids
  let dynamicItems = getDynamicItemsFromBlocks({ blocks }).filter(({ type }) => {
    const { itemTypeId } = serializeCSSelectedString(type);

    return TRACKING_ITEM_TYPE_IDS.includes(itemTypeId);
  });

  // If there are dynamic items and the first item has an index
  // Then continue to process
  if (!!dynamicItems.length && !!dynamicItems[0]?.index) {
    const { index, type, attribute } = dynamicItems[0];
    const { itemTypeId } = serializeCSSelectedString(dynamicItems[0].type);

    return {
      itemTypeId,
      itemTypeName: attribute?.itemTypeName || '',
      value: buildMergeTag({ type, index, attribute: { value: 'id' } }),
    };
  }

  return undefined;
};

const getAttributeError = ({
  listAttributeBO = [],
  attributeSelectedId = '',
}: {
  listAttributeBO: any[];
  attributeSelectedId: string;
}) => {
  const attributeSelected: any = listAttributeBO.find(
    ({ itemPropertyName }) => itemPropertyName === attributeSelectedId,
  );

  if (attributeSelected) {
    switch (+attributeSelected.status) {
      // Status 4: attribute archive
      case ATTRIBUTE_STATUS['4'].value:
        return getTranslateMessage(translations.messageError.attributeArchive.message);

      default:
        return null;
    }
  }

  // If attribute not exists
  return getTranslateMessage(translations.messageError.attributeDelete.message);
};

const getBoError = ({ listBO = [], boSelectedId = 0 }) => {
  const boSelected: any = listBO.find(({ id }) => +id === +boSelectedId);

  if (boSelected) {
    switch (+boSelected.status) {
      // Status 4: attribute archive
      case BO_STATUS['4'].value:
        return getTranslateMessage(translations.messageError.BOArchive.message);

      default:
        return null;
    }
  }

  // If attribute not exists
  return getTranslateMessage(translations.messageError.BODelete.message);
};

const getCollectionError = ({ listCollection = [], collectionId = 0, checkDisable = false }) => {
  const collectionSelected: any = listCollection.find(({ value }) => value === collectionId);

  if (collectionSelected) {
    switch (true) {
      // Status 4: attribute archive
      case +collectionSelected.status === COLLECTION_STATUS['4'].value:
        return getTranslateMessage(translations.messageError.collectionArchive.message);

      case +collectionSelected.status === COLLECTION_STATUS[2].value && checkDisable:
        return getTranslateMessage(translations.messageError.collectionDisable.message);

      default:
        return null;
    }
  }

  // If attribute not exists
  return getTranslateMessage(translations.messageError.collectionDelete.message);
};

const getListEventAttr = async ({ insightPropertyId, eventActionId, eventCategoryId }) => {
  try {
    const listEventAttr = await queryClient.fetchQuery({
      queryKey: [QUERY_KEYS.GET_LIST_EVENT_ATTR, insightPropertyId, `${eventActionId}:${eventCategoryId}`],
      queryFn: () =>
        getListAttributes({
          eventActionId: eventActionId,
          eventCategoryId: eventCategoryId,
          sourceId: Array.isArray(insightPropertyId) ? insightPropertyId.join(',') : insightPropertyId,
          getAll: true,
        }),
    });

    const flatEventAttr = flattenTree(listEventAttr, 'items').map(
      ({ itemPropertyName, eventPropertyName, ...item }) => ({
        ...item,
        itemPropertyName: eventPropertyName || itemPropertyName,
      }),
    );

    return flatEventAttr;
  } catch (error) {
    return [];
  }
};

const getListCollectionQuery = async ({ itemTypeId }) => {
  try {
    const { rows: listCollection } = await queryClient.fetchQuery({
      queryKey: [QUERY_KEYS.GET_LIST_COLLECTION, itemTypeId],
      queryFn: () => getListCollectionsBO({ itemTypeId }),
    });

    return listCollection;
  } catch (error) {
    return [];
  }
};

export const getEventAttributesBOQuery = async ({ itemTypeIds }: { itemTypeIds: (number | null)[] }) => {
  try {
    if (itemTypeIds.filter(itemTypeId => !!itemTypeId).length) {
      const { rows } = (await queryClient.fetchQuery({
        queryKey: [QUERY_KEYS.GET_EVENT_ATTRIBUTE_BO, itemTypeIds],
        queryFn: () => getListAttributesBO({ itemTypeIds: itemTypeIds }),
      })) as any;

      return get(rows, `[0].properties`, []);
    }

    return [];
  } catch (error) {
    return [];
  }
};

const getBusinessObjectsQuery = async () => {
  try {
    const businessObjects = await queryClient.fetchQuery({
      queryKey: [QUERY_KEYS.GET_LIST_BO],
      queryFn: async () => {
        const { rows: businessObjects } = await getListBusinessObject();

        return businessObjects;
      },
    });

    return businessObjects;
  } catch (error) {
    return [];
  }
};

const getDynamicContentAttributesQuery = async () => {
  try {
    const personalizeAttributes = await queryClient.fetchQuery({
      queryKey: [QUERY_KEYS.GET_LIST_DYNAMIC_CONTENT_ATTR],
      queryFn: async () => {
        const data = await getDynamicContentAttributes();

        return {
          visitor: data.visitor,
          customer: data.customer,
        };
      },
    });

    return personalizeAttributes;
  } catch (error) {
    return {
      visitor: [],
      customer: [],
    };
  }
};

const checkPermissionPoolError = async ({ poolCodes, isLatest }: { poolCodes: string[]; isLatest?: boolean }) => {
  const objects = poolCodes.map(poolCode => ({
    objectCode: poolCode,
    objectType: OBJECT_TYPE.PROMOTION_POOL,
  }));
  const permissionInfo = await queryClient.fetchQuery({
    queryKey: [QUERY_KEYS.CHECK_PERMISSION, OBJECT_TYPE.PROMOTION_POOL, poolCodes],
    queryFn: () =>
      checkPermissionSevices.getInfo({
        data: {
          objects,
        },
      }),
    staleTime: isLatest ? 0 : Infinity,
  });
  const poolError: Record<string, string> = {};
  const poolWarning: Record<string, string> = {};
  let poolErrorMsg = '';
  let poolWarningMsg = '';

  if (!permissionInfo || permissionInfo.code < 200 || permissionInfo.code > 300 || !permissionInfo?.data?.entries) {
    poolCodes.forEach(poolCode => {
      poolError[poolCode] = 'This pool is removed';
    });
  } else if (permissionInfo?.data?.entries) {
    permissionInfo?.data?.entries?.forEach(item => {
      const { isExist, isView } = item || {};
      if (!isExist) {
        poolError[item.objectCode] = 'This pool is removed';
        poolErrorMsg = 'This pool is removed';
      } else if (!isView) {
        poolWarning[item.objectCode] = 'You do not have permission on this pool anymore';
        poolWarningMsg = 'You do not have permission on this pool anymore';
      }
    });
  }
  return { poolError, poolWarning, poolErrorMsg, poolWarningMsg };
};

export const validateError = async ({ workspace, action }: { workspace: TWorkspace; action?: string }) => {
  try {
    const draftWorkspace = cloneDeep(workspace);

    const viewPages = draftWorkspace?.viewPages || [];
    const csSettingsErrors: string[] = [];
    let promotionPoolErrors: Record<string, string> = {};
    let promotionPoolWarnings: Record<string, string> = {};
    const viewPageErrors = {};
    const viewPageWarnings = {};
    const boErrorMsg = getTranslateMessage(translations.messageError.boError.message);
    const boCompleteMsg = getTranslateMessage(translations.messageError.completeSetupInContentSources.message);

    // Queries
    let promiseQueries: any = [];

    promiseQueries = [getBusinessObjectsQuery(), getDynamicContentAttributesQuery()];

    const [listBO, dynamicContentAttributes] = await Promise.all(promiseQueries);

    for (const viewPage of viewPages) {
      const { id: viewPageId, blocks } = viewPage || {};
      const errors = {};
      const warnings = {};

      for (let blockKey in blocks) {
        const { id, type, settings } = blocks[blockKey] || {};
        const blockErrors: string[] = [];
        const blockWarnings: string[] = [];

        // Get info from display condition
        const displayCondition = get(settings, 'blockStylesSettings.displayCondition');
        const {
          type: typeDCAttr,
          condition,
          attribute,
          event_metadata,
          value_type,
          operator,
          value,
        } = displayCondition || {};

        if (!!condition && regexCSType.test(typeDCAttr)) {
          const { itemTypeId } = serializeCSSelectedString(typeDCAttr);

          const listAttributeBO = await getEventAttributesBOQuery({ itemTypeIds: [itemTypeId] });

          if (!!attribute.value) {
            // Check attribute is archived or deleted
            const attributeError = getAttributeError({
              listAttributeBO,
              attributeSelectedId: attribute.value,
            });

            !!attributeError && blockErrors.push(attributeError);
          } else {
            // Check field is required
            blockErrors.push(getTranslateMessage(translations.messageError.fieldIsRequired.message));
          }

          if (!!event_metadata) {
            const {
              useBo,
              field_code_bo,
              item_property_name,
              event_category_id,
              event_action_id,
              insight_property_id,
            } = event_metadata;

            if (!!useBo && !!field_code_bo) {
              const attributeError = getAttributeError({
                listAttributeBO,
                attributeSelectedId: field_code_bo,
              });

              !!attributeError && blockErrors.push(attributeError);
            }

            if (!useBo && !!item_property_name) {
              const listEventAttr = await getListEventAttr({
                eventActionId: event_action_id,
                eventCategoryId: event_category_id,
                insightPropertyId: insight_property_id,
              });

              const attributeError = getAttributeError({
                listAttributeBO: listEventAttr,
                attributeSelectedId: item_property_name,
              });

              !!attributeError && blockErrors.push(attributeError);
            }
          }

          // Check type of value is normal
          if (['', 'normal'].includes(value_type)) {
            if (![OPERATORS_OPTION.EXISTS.value, OPERATORS_OPTION.NOT_EXISTS.value].includes(operator || '')) {
              const isEmptyValue =
                value == null ||
                !`${value}` ||
                (Array.isArray(value) && !value.filter(Boolean).length) ||
                `${value}`.match('null');

              isEmptyValue && blockErrors.push(getTranslateMessage(translations.messageError.fieldIsRequired.message));
            }
          } else {
            // Type is event
            const isEmptyValue = !event_metadata?.item_property_name && !event_metadata?.field_code_bo;

            isEmptyValue && blockErrors.push(getTranslateMessage(translations.messageError.fieldIsRequired.message));
          }
        }

        switch (type) {
          case IMAGE.name:
          case BUTTON.name:
          case VIDEO.name:
          case RATING.name: {
            const { dynamic } = settings || {};

            for (const key in dynamic) {
              const dynamicItem = dynamic[key];

              const { attribute = {}, type, isDynamic } = dynamicItem || {};

              switch (true) {
                case regexCSType.test(type): {
                  const { itemTypeId } = serializeCSSelectedString(type);
                  const listAttributeBO = await getEventAttributesBOQuery({ itemTypeIds: [itemTypeId] });

                  if (!!attribute.value && isDynamic) {
                    const attributeError = getAttributeError({
                      listAttributeBO,
                      attributeSelectedId: attribute.value,
                    });

                    !!attributeError && blockErrors.push(attributeError);
                  }
                  break;
                }

                case dynamicItem.type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value: {
                  const { pool } = dynamicItem || {};
                  if (pool) {
                    const { poolError, poolWarning, poolErrorMsg, poolWarningMsg } = await checkPermissionPoolError({
                      poolCodes: [pool],
                      isLatest:
                        action === actions.setMediaTemplateDetail.type || action === ACTIONS.SAVE_MEDIA_TEMPLATE,
                    });
                    if (poolErrorMsg) {
                      blockErrors.push(poolErrorMsg);
                      promotionPoolErrors[pool] = poolError[pool];
                    }
                    if (poolWarningMsg) {
                      blockWarnings.push(poolWarningMsg);
                      promotionPoolWarnings[pool] = poolWarning[pool];
                    }
                  }
                  break;
                }

                // case DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
                //   if (!!attribute.value) {
                //     objectRelationships.push(
                //       buildObjectRelationShip({
                //         itemTypeId: -100,
                //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                //         objectPropertyName: attribute.value,
                //       }),
                //     );
                //   }
                //   break;
                // case DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
                //   if (!!attribute.value) {
                //     objectRelationships.push(
                //       buildObjectRelationShip({
                //         itemTypeId: -1007,
                //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                //         objectPropertyName: attribute.value,
                //       }),
                //     );
                //   }
                //   break;
                // case DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
                //   if (!!attribute.value) {
                //     objectRelationships.push(
                //       buildObjectRelationShip({
                //         itemTypeId: -1003,
                //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                //         objectPropertyName: attribute.value,
                //       }),
                //     );
                //   }
                //   break;
                // case DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
                //   if (!!attribute.propertyName) {
                //     objectRelationships.push(
                //       buildObjectRelationShip({
                //         itemTypeId: attribute.itemTypeId,
                //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                //         objectPropertyName: attribute.propertyName,
                //       }),
                //     );
                //   }
                //   break;
                default:
                  break;
              }
            }

            break;
          }
          case OPTIN_FIELDS.name: {
            const { columnMapping = [], event = {}, source = {} } = get(settings, 'syncData', {});
            const { errorMessage } = get(settings, 'verifiedSubmit', {});

            if (event.eventActionId && event.eventCategoryId && source.insightPropertyId) {
              const listEventAttr = await getListEventAttr({
                eventActionId: event.eventActionId,
                eventCategoryId: event.eventCategoryId,
                insightPropertyId: source.insightPropertyId,
              });
              const attributeErrors = columnMapping
                .map(col => {
                  const value = get(col, 'attribute.attributeOpin.value', '') || get(col, 'attribute.value', '');

                  return getAttributeError({
                    listAttributeBO: listEventAttr.map(eventAttr => ({
                      itemPropertyName: `${eventAttr.itemTypeId || ''}${eventAttr.itemTypeId ? '.' : ''}${
                        eventAttr.itemPropertyName
                      }`,
                      status: eventAttr.status,
                    })),
                    attributeSelectedId: value,
                  });
                })
                .filter(error => error);

              !!attributeErrors?.[0] && blockErrors.push(attributeErrors?.[0]);
            }

            if (!errorMessage) {
              blockErrors.push(getTranslateMessage(translations.messageError.fieldIsRequired.message));
            }

            break;
          }
          case YES_NO.name: {
            const { yesDynamic, noDynamic } = settings || {};

            const dynamics = [{ ...yesDynamic }, { ...noDynamic }];

            for (const dynamic of dynamics) {
              for (const key in dynamic) {
                const dynamicItem = dynamic[key];
                const { attribute = {}, type, isDynamic } = dynamicItem || {};

                switch (true) {
                  case regexCSType.test(type): {
                    if (!!attribute.value && isDynamic) {
                      const { itemTypeId } = serializeCSSelectedString(type);
                      const listAttributeBO = await getEventAttributesBOQuery({ itemTypeIds: [itemTypeId] });

                      const attributeError = getAttributeError({
                        listAttributeBO,
                        attributeSelectedId: attribute.value,
                      });

                      !!attributeError && blockErrors.push(attributeError);
                    }
                    break;
                  }

                  case dynamicItem.type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value: {
                    const { pool } = dynamicItem || {};
                    if (pool) {
                      const { poolError, poolWarning, poolErrorMsg, poolWarningMsg } = await checkPermissionPoolError({
                        poolCodes: [pool],
                        isLatest:
                          action === actions.setMediaTemplateDetail.type || action === ACTIONS.SAVE_MEDIA_TEMPLATE,
                      });
                      if (poolErrorMsg) {
                        blockErrors.push(poolErrorMsg);
                        promotionPoolErrors[pool] = poolError[pool];
                      }
                      if (poolWarningMsg) {
                        blockWarnings.push(poolWarningMsg);
                        promotionPoolWarnings[pool] = poolWarning[pool];
                      }
                    }
                    break;
                  }

                  // case DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
                  //   if (!!attribute.value) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: -100,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.value,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  // case DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
                  //   if (!!attribute.value) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: -1007,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.value,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  // case DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
                  //   if (!!attribute.value) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: -1003,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.value,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  // case DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
                  //   if (!!attribute.propertyName) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: attribute.itemTypeId,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.propertyName,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  default:
                    break;
                }
              }
            }

            break;
          }
          case TEXT.name: {
            const dynamicData = get(settings, 'dynamic.data', {});
            const linkDynamicData = get(settings, 'link.data', {});

            const dynamics = [{ ...dynamicData }, { ...linkDynamicData }];

            for (const dynamicData of dynamics) {
              for (const key in dynamicData) {
                const dynamicInfo = dynamicData[key];
                const { attribute = {}, type } = dynamicInfo || {};

                switch (true) {
                  case regexCSType.test(type):
                    if (!!attribute.value) {
                      const { itemTypeId } = serializeCSSelectedString(type);
                      const listAttributeBO = await getEventAttributesBOQuery({ itemTypeIds: [itemTypeId] });

                      const attributeError = getAttributeError({
                        listAttributeBO,
                        attributeSelectedId: attribute.value,
                      });

                      !!attributeError && blockErrors.push(attributeError);
                    }
                    break;

                  case dynamicInfo.type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value: {
                    const { pool } = dynamicInfo || {};
                    if (pool) {
                      const { poolError, poolWarning, poolErrorMsg, poolWarningMsg } = await checkPermissionPoolError({
                        poolCodes: [pool],
                        isLatest:
                          action === actions.setMediaTemplateDetail.type || action === ACTIONS.SAVE_MEDIA_TEMPLATE,
                      });
                      if (poolErrorMsg) {
                        blockErrors.push(poolErrorMsg);
                        promotionPoolErrors[pool] = poolError[pool];
                      }

                      if (poolWarningMsg) {
                        blockWarnings.push(poolWarningMsg);
                        promotionPoolWarnings[pool] = poolWarning[pool];
                      }
                    }
                    break;
                  }

                  // case DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
                  //   if (!!attribute.value) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: -100,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.value,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  // case DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
                  //   if (!!attribute.value) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: -1007,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.value,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  // case DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
                  //   if (!!attribute.value) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: -1003,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.value,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  // case DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
                  //   if (!!attribute.propertyName) {
                  //     objectRelationships.push(
                  //       buildObjectRelationShip({
                  //         itemTypeId: attribute.itemTypeId,
                  //         objectType: OBJECT_TYPE.ITEM_PROPERTY,
                  //         objectPropertyName: attribute.propertyName,
                  //       }),
                  //     );
                  //   }
                  //   break;
                  default:
                    break;
                }
              }
            }

            break;
          }
          case TABLE.name:
            const boId = settings.boId;

            if (boId) {
              const boError = getBoError({
                listBO,
                boSelectedId: boId,
              });

              !!boError && blockErrors.push(boError);
            }

            break;
          case COUPON_WHEEL.name:
          case SHAKE_AND_WIN.name:
          case SURPRISE_TREASURE_HUNT.name:
            let hasErrorSection = false;
            let poolUsed: string[] = [];

            settings.sections.forEach(async (s: any) => {
              if (hasErrorSection) return;

              const errorBySection = s.validateInfo.filter((v: any) => v.status === 'error');

              if (errorBySection.length) {
                // prettier-ignore
                blockErrors.push(`${getTranslateMessage(translations.couponWheelSectionError.title)}`);

                hasErrorSection = true;
              }
              if (s.pool && s.couponCode) {
                poolUsed.push(s.couponCode);
              }
            });

            if (poolUsed.length > 0) {
              const { poolError, poolWarning, poolErrorMsg, poolWarningMsg } = await checkPermissionPoolError({
                poolCodes: poolUsed,
                isLatest: action === actions.setMediaTemplateDetail.type || action === ACTIONS.SAVE_MEDIA_TEMPLATE,
              });
              if (poolErrorMsg) {
                blockErrors.push(poolErrorMsg);
                promotionPoolErrors = { ...promotionPoolErrors, ...poolError };
              }

              if (poolWarningMsg) {
                blockWarnings.push(poolWarningMsg);
                promotionPoolWarnings = { ...promotionPoolWarnings, ...poolWarning };
              }
            }

            break;
          default:
            break;
        }

        // If block has errors then push to view page errors
        if (blockErrors.length) {
          errors[id] = blockErrors;
        }

        // If block has warnings then push to view page warnings
        if (blockWarnings.length) {
          warnings[id] = blockWarnings;
        }
      }

      if (Object.keys({ ...errors }).length) {
        viewPageErrors[viewPageId] = errors;
      }

      if (Object.keys({ ...warnings }).length) {
        viewPageWarnings[viewPageId] = warnings;
      }
    }

    const csGroups = workspace?.contentSources.groups || [];

    // Validate content source group name have similar
    if (uniqBy(csGroups, 'groupName').length < csGroups.length) {
      csSettingsErrors.push(getTranslateMessage(translations.contentSources.errorMessage.groupSameName));
    }

    await Promise.all(
      csGroups.map(async g => {
        const rankingValues = get(g, 'ranking.algorithms.value', []);
        const filters = g.filters;
        const itemTypeId = g.itemTypeId;

        const listAttributeBO = await getEventAttributesBOQuery({ itemTypeIds: [itemTypeId] });
        const listCollection = await getListCollectionQuery({ itemTypeId });

        // Validate Group name
        if (isEmpty(g.groupName)) {
          csSettingsErrors.push(getTranslateMessage(translations.contentSources.errorMessage.groupNameEmpty));
        }

        // Validate Ranking
        for (let index = 0; index < rankingValues.length; index++) {
          const rankingValue = rankingValues[index];

          const { value, by } = rankingValue || {};

          switch (value) {
            case 'viral_products': {
              if (!by) {
                csSettingsErrors.push(boCompleteMsg);
              } else {
                const collectionError = getCollectionError({
                  listCollection: listCollection as any,
                  collectionId: by,
                  checkDisable: false,
                });

                !!collectionError && csSettingsErrors.push(boErrorMsg);
              }
              break;
            }
            case 'distance': {
              if (!!by) {
                const attributeError = getAttributeError({
                  listAttributeBO,
                  attributeSelectedId: by,
                });

                !!attributeError && csSettingsErrors.push(boErrorMsg);
              } else {
                csSettingsErrors.push(boCompleteMsg);
              }

              if (!!rankingValue?.with) {
                const {
                  item_property_name,
                  insight_property_id = [],
                  event_action_id,
                  event_category_id,
                } = rankingValue.with || {};

                if (!item_property_name) {
                  csSettingsErrors.push(boCompleteMsg);
                } else {
                  const listEventAttr = await getListEventAttr({
                    eventActionId: event_action_id,
                    eventCategoryId: event_category_id,
                    insightPropertyId: insight_property_id,
                  });

                  const attributeError = getAttributeError({
                    listAttributeBO: listEventAttr,
                    attributeSelectedId: item_property_name,
                  });

                  !!attributeError && csSettingsErrors.push(boErrorMsg);
                }
              }

              break;
            }
            case 'retarget_search':
              if (Array.isArray(by) && by.length) {
                by.forEach(propertyName => {
                  const attributeError = getAttributeError({
                    listAttributeBO,
                    attributeSelectedId: propertyName,
                  });

                  !!attributeError && csSettingsErrors.push(boErrorMsg);
                });
              } else {
                csSettingsErrors.push(boCompleteMsg);
              }

              break;
            case 'similar_content':
            case 'same_category':
              if (isEmpty(by)) {
                csSettingsErrors.push(boCompleteMsg);
              }
              break;
            case 'get_top': {
              const { sort_by } = rankingValue || {};

              if (!!sort_by) {
                const attributeError = getAttributeError({
                  listAttributeBO,
                  attributeSelectedId: sort_by,
                });

                !!attributeError && csSettingsErrors.push(boErrorMsg);
              } else {
                csSettingsErrors.push(boCompleteMsg);
              }
              break;
            }
            case 'notify': {
              const { interest } = rankingValue || {};

              if (!interest) {
                csSettingsErrors.push(boCompleteMsg);
              }

              break;
            }
            default:
              break;
          }
        }

        // Validate Filter
        for (const orIndex in filters.OR) {
          const orFilter = filters.OR[orIndex];

          for (const andIndex in orFilter.AND) {
            const andFilter = orFilter.AND[andIndex];

            const { column, value_type, operator, event_metadata, visitor_metadata, customer_metadata, value } =
              andFilter || {};

            const metadata =
              value_type === VALUE_TYPE.EVENT.value
                ? event_metadata
                : value_type === VALUE_TYPE.CUSTOMER.value
                ? customer_metadata
                : visitor_metadata;

            if (!!column) {
              const attributeError = getAttributeError({
                listAttributeBO,
                attributeSelectedId: column,
              });

              !!attributeError && csSettingsErrors.push(boErrorMsg);
            }

            if (
              [OPERATORS_OPTION.MATCHES.value, OPERATORS_OPTION.NOT_MATCHES.value].includes(operator || '') &&
              column === SEGMENT_IDS
            ) {
              if (Array.isArray(value)) {
                if (
                  value.some(val => !!getCollectionError({ listCollection, collectionId: val, checkDisable: false }))
                ) {
                  csSettingsErrors.push(boErrorMsg);
                }
              }
            }

            // Validate filter if value type is one of visitor, customer, event
            if (metadata?.item_property_name) {
              if ([VALUE_TYPE.CUSTOMER.value, VALUE_TYPE.VISITOR.value].includes(value_type)) {
                const attributeError = getAttributeError({
                  listAttributeBO:
                    dynamicContentAttributes[value_type]?.map(item => ({ ...item, itemPropertyName: item.value })) ||
                    [],
                  attributeSelectedId: metadata?.item_property_name || '',
                });

                !!attributeError && csSettingsErrors.push(boErrorMsg);
              }

              if (value_type === VALUE_TYPE.EVENT.value) {
                const { event_action_id, event_category_id, insight_property_ids, item_property_name } =
                  metadata as any;

                const listEventAttr = await getListEventAttr({
                  eventActionId: event_action_id,
                  eventCategoryId: event_category_id,
                  insightPropertyId: insight_property_ids,
                });

                const attributeError = getAttributeError({
                  listAttributeBO: listEventAttr,
                  attributeSelectedId: item_property_name,
                });

                !!attributeError && csSettingsErrors.push(boErrorMsg);
              }
            }
          }
        }

        // Validate BO
        if (itemTypeId !== null) {
          const boError = getBoError({
            listBO,
            boSelectedId: itemTypeId,
          });

          !!boError && csSettingsErrors.push(boErrorMsg);
        }
      }),
    );

    const workspaceErrors: TWorkspace['errors'] = {};
    const workspaceWarnings: TWorkspace['warnings'] = {};

    if (csSettingsErrors.length) {
      workspaceErrors.boSettings = uniq(csSettingsErrors);
    }

    // PromotionPool Errors
    if (Object.keys({ ...promotionPoolErrors }).length) {
      workspaceErrors.promotionPool = promotionPoolErrors;
    }
    // PromotionPool Warnings
    if (Object.keys({ ...promotionPoolWarnings }).length) {
      workspaceWarnings.promotionPool = promotionPoolWarnings;
    }
    // ReFetch List Promotion Pool
    if (Object.keys({ ...promotionPoolErrors }).length || Object.keys({ ...promotionPoolWarnings }).length) {
      queryClient.refetchQueries([QUERY_KEYS.GET_LIST_PROMOTION_POOL]);
    }

    if (Object.keys({ ...viewPageErrors }).length) {
      workspaceErrors.viewPages = viewPageErrors;
    }
    if (Object.keys({ ...viewPageWarnings }).length) {
      workspaceWarnings.viewPages = viewPageWarnings;
    }

    return { workspaceErrors, workspaceWarnings };
  } catch (error) {
    return {};
  }
};

export const getAllBlockErrors = ({ workspaceErrors }: { workspaceErrors: Record<string, any> }) => {
  return Object.values(get(workspaceErrors, 'viewPages', {})).reduce(
    (previousValue: Record<string, any>, currentValue: any) => {
      return Object.assign(previousValue, currentValue);
    },
    {},
  );
};

export const migrateMediaTemplateDetail = (mediaTemplateDetail: TWorkspace) => {
  const cloneMediaTemplateDetail = cloneDeep(omit(mediaTemplateDetail, ['warnings']));
  const {
    viewPages = [],
    boSettings,
    utmSettings,
    trackingModule,
    settings,
    journeySettings,
    contentSources,
    isInitial,
  } = cloneMediaTemplateDetail;

  const randomCSGroupId = `csg${random(5)}`;

  viewPages.forEach(viewPage => {
    const { rows = [], settings } = viewPage;
    const blocks = {};
    const tree: Record<string, any> = {
      root: [],
    };

    // Check if Blocks isn't exist
    if (!Object.values(viewPage.blocks || {}).length) {
      rows.length &&
        rows.forEach(row => {
          const draftRow = { ...row };
          const columnIds: string[] = [];
          (draftRow as any).type = COLUMN.name;

          row.columns.forEach(column => {
            const draftColumn = { ...column };
            const elementIds: string[] = [];

            (draftColumn as any).type = 'col';

            draftColumn.elements.forEach(element => {
              if (Array.isArray(element.slides)) {
                const slideIds: string[] = [];

                element.slides.forEach(slide => {
                  const draftSlide = { ...slide };
                  const slideElementIds: string[] = [];

                  slide.elements.forEach(slideElement => {
                    blocks[slideElement.id] = slideElement;
                    slideElementIds.push(slideElement.id.toString());
                  });

                  draftSlide.elements = [];
                  blocks[draftSlide.id] = draftSlide;
                  tree[draftSlide.id] = slideElementIds;
                  slideIds.push(draftSlide.id.toString());
                });

                tree[element.id] = slideIds;
              }

              blocks[element.id] = element;
              elementIds.push(element.id.toString());
            });

            draftColumn.elements = [];
            blocks[draftColumn.id] = draftColumn;
            tree[draftColumn.id] = elementIds;
            columnIds.push(draftColumn.id);
          });

          draftRow.columns = [];
          blocks[draftRow.id] = draftRow;
          tree.root.push(draftRow.id);
          tree[draftRow.id] = columnIds;
        });

      viewPage.rows = [];
      viewPage.blocks = blocks;
      viewPage.tree = tree;
    }

    // Migrate per block
    Object.keys(viewPage.blocks || {}).forEach(blockId => {
      const block = (viewPage.blocks || {})[blockId];

      /*=====  Start of Migrate gradients  ======*/
      if (!get(block.settings, 'blockStylesSettings.gradients', null)) {
        set(block.settings, 'blockStylesSettings.gradients', migrateGradients(block.settings.blockStylesSettings));

        if (get(block.settings, 'blockHoverStylesSettings')) {
          set(
            block.settings,
            'blockHoverStylesSettings.gradients',
            migrateGradients(block.settings.blockHoverStylesSettings),
          );
        }
      }

      switch (block.type) {
        case BUTTON.name:
          if (!get(block.settings, 'buttonSettings.gradients', null)) {
            set(block.settings, 'buttonSettings.gradients', migrateGradients(block.settings.buttonSettings));
            set(block.settings, 'buttonHoverSettings.gradients', migrateGradients(block.settings.buttonHoverSettings));
          }

          break;
        case YES_NO.name:
          if (!get(block.settings, 'yesButtonSettings.gradients', null)) {
            set(block.settings, 'yesButtonSettings.gradients', migrateGradients(block.settings.yesButtonSettings));
            set(
              block.settings,
              'yesButtonHoverSettings.gradients',
              migrateGradients(block.settings.yesButtonHoverSettings),
            );
            set(block.settings, 'noButtonSettings.gradients', migrateGradients(block.settings.noButtonSettings));
            set(
              block.settings,
              'noButtonHoverSettings.gradients',
              migrateGradients(block.settings.noButtonHoverSettings),
            );
          }

          break;
        case COUNT_DOWN.name:
          if (!get(block.settings, 'stylesSettings.gradients', null)) {
            set(block.settings, 'stylesSettings.gradients', migrateGradients(block.settings.stylesSettings));
          }
          break;
        case OPTIN_FIELDS.name:
          if (!get(block.settings, 'stylesSettings.gradients', null)) {
            set(block.settings, 'stylesSettings.gradients', migrateGradients(block.settings.stylesSettings));
            set(block.settings, 'buttonSettings.gradients', migrateGradients(block.settings.buttonSettings));
          }
          if (isInitial) {
            set(block.settings, 'syncData', {
              columnMapping: [],
              isAction: false,
              event: {},
              source: {},
            });
          }
          break;

        default:
          break;
      }
      /*=====  End of Migrate gradients  ======*/

      // Migrate dynamic content
      switch (block.type) {
        case IMAGE.name:
        case BUTTON.name:
        case VIDEO.name:
        case RATING.name:
        case OPTIN_FIELDS.name: {
          const dynamic = get(block, 'settings.dynamic', null);

          if (!!dynamic) {
            Object.keys({ ...dynamic }).forEach(key => {
              const dynamicItem = dynamic[key] || {};
              const { field, isDynamic, index, attribute } = dynamicItem;

              if (isDynamic && !!field && !attribute) {
                dynamic[key] = {
                  isDynamic,
                  type: DYNAMIC_CONTENT_TYPE.BO_SETTINGS.value,
                  index,
                  mappingFields: `groups.${randomCSGroupId}[${index}].${field}`,
                  attribute: {
                    itemTypeId: 1,
                    itemPropertyName: field,
                    itemPropertyDisplay: field,
                    value: field,
                    label: field,
                    disabled: false,
                  },
                };
              }
            });

            block.settings.dynamic = dynamic;
          }
          break;
        }
        default:
          break;
      }

      // Migrate block by type
      switch (block.type) {
        case STANDARDS_BLOCKS.OPTIN_FIELDS.name:
          if (!get(block, 'settings.fields.firstNameInput', null)) {
            const { nameInput, ...restOfFields } = block.settings.fields;

            block.settings.fields = {
              nameInput,
              ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].fields,
              ...restOfFields,
            };
          }

          if (!get(block, 'settings.stylesSettings.gapX', null)) {
            block.settings.stylesSettings = {
              ...block.settings.stylesSettings,
              ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].stylesSettings,
            };
          }

          if (!get(block, 'settings.limitedSubmit', null)) {
            block.settings.limitedSubmit = DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].limitedSubmit;
          }

          if (!get(block, 'settings.verifiedSubmit', null)) {
            block.settings.verifiedSubmit = DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].verifiedSubmit;
          }

          const fields = get(block, 'settings.fields', {});

          if (
            !block.settings.labelStyles ||
            (Object.values(block.settings.fields) as TField[]).some(
              field => field.id !== 'submitButton' && !field.nameField,
            )
          ) {
            block.settings = {
              ...block.settings,
              ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].settings,
            };
            for (let key in fields) {
              const curField = block.settings.fields[key];

              block.settings.fields[key] =
                curField.id !== 'submitButton'
                  ? {
                      ...curField,
                      ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].defaultFieldsProperties,
                      fieldWidth: curField.isCustom ? curField.fieldWidth : curField.inputWidth,
                      inputWidth: curField.isCustom ? curField.inputWidth : '100%',
                      nameField:
                        curField.nameField || FIELDS_SETTING[curField.isCustom ? curField.type : curField.id]?.name,
                      label: curField.label || FIELDS_SETTING[curField.isCustom ? curField.type : curField.id]?.name,
                      errorText:
                        curField.errorText ||
                        `The ${curField?.name?.toLowerCase()} ${
                          curField.name.includes('field') ? '' : 'field'
                        } is required`,
                    }
                  : curField;
            }
          }

          break;

        case STANDARDS_BLOCKS.COUNT_DOWN.name:
          if (!get(block, 'settings.separatorStyles', null)) {
            const { settings } = block.settings;

            block.settings.separatorStyles = DATA_MIGRATE[STANDARDS_BLOCKS.COUNT_DOWN.name].separatorStyles;
            block.settings.settings = {
              ...settings,
              ...DATA_MIGRATE[STANDARDS_BLOCKS.COUNT_DOWN.name].settings,
            };
          }

          break;
        case STANDARDS_BLOCKS.COUPON_WHEEL.name:
          const sections: TWheelItem[] = get(block, 'settings.sections');

          const updatedSections = sections.map((section, _index, currentSections) => {
            const { pool } = section;

            if (!has(section, 'internalCode')) {
              section.internalCode = generateInternalCode(
                section.label,
                currentSections.map(s => s.internalCode),
              );
            }

            if (!has(section, 'couponCodeAttr')) {
              section.couponCodeAttr = section.pool ? 'id' : '';
            }

            if (!has(section, 'limitSpinning')) {
              section.limitSpinning = pool ? { type: 'out_of_code' } : { type: 'unlimited' };
            }

            if (!has(section, 'saved')) {
              section.saved = false;
            }

            if (!has(section, 'cappingLevel')) {
              section.cappingLevel = null;
            }

            if (!has(section, 'frequency')) {
              section.frequency = null;
            }

            if (!has(section, 'sectionId')) {
              section.sectionId = random(5);
            }

            return section;
          });

          set(block, 'settings.sections', updatedSections);

          break;
        default:
          break;
      }

      // Migrate boSettings to contentSources
      {
        const { dynamic, path, dynamicLink, pathLink } = getDynamicInfoByBlock(block);

        if (dynamic !== null) {
          if (dynamic.hasOwnProperty('isDynamic') && !dynamic.isDynamic) return;

          Object.entries(dynamic).forEach(([key, value]) => {
            if (!dynamic[key].mappingKey && !dynamic[key].mappingFields) {
              dynamic[key].mappingKey = `${blockId}-${key}`;
              dynamic[key].mappingFields = buildMappingFields(value);
            }

            if (dynamic[key].type === 'bo-settings' && boSettings?.itemTypeId) {
              dynamic[key].type = `content-source::${randomCSGroupId}::${boSettings.itemTypeId}`;
            }
          });

          set(block, path, dynamic);
        }

        if (dynamicLink !== null) {
          Object.keys(dynamicLink).forEach(key => {
            if (dynamicLink[key].linkType === 'dynamic' && dynamicLink[key].type === 'bo-settings') {
              dynamicLink[key].type = `content-source::${randomCSGroupId}::${boSettings?.itemTypeId}`;
            }
          });

          set(block, pathLink, dynamicLink);
        }
      }

      // Migrate display condition of block
      {
        const displayCondition: any = get(block, 'settings.blockStylesSettings.displayCondition', {});
        const { field, condition, attribute, dataType } = displayCondition;

        if (condition !== '' && field && attribute === undefined) {
          set(block, 'settings.blockStylesSettings.displayCondition', {
            ...displayCondition,
            attribute: {
              dataType,
              value: field,
            },
            type: `content-source::${randomCSGroupId}::${boSettings?.itemTypeId}`,
          });
          // * Ignore path settings.blockHoverStylesSettings.displayCondition
        }
      }

      // Migrate block settings
      Object.keys(MIGRATE_BLOCK_SETTINGS).forEach(key => {
        if (!get(block, `settings.${key}`, null)) {
          set(block, `settings.${key}`, MIGRATE_BLOCK_SETTINGS[key]);
        }
      });
    });

    // Migrate Gradient
    if (!get(settings, 'container.gradients', null)) {
      set(viewPage.settings, 'container.gradients', migrateGradients(settings.container));
      set(viewPage.settings, 'slideClosedContainer.gradients', migrateGradients(settings.slideClosedContainer));
      set(viewPage.settings, 'fullscreenContainer.gradients', migrateGradients(settings.fullscreenContainer));
    }
  });

  // Migrates boSettings
  if (!!boSettings) {
    const { filters } = boSettings;

    for (const orIndex in filters.OR) {
      const orFilter = filters.OR[orIndex];

      for (const andIndex in orFilter.AND) {
        const andFilter = orFilter.AND[andIndex];
        const { event_metadata } = andFilter;

        if (andFilter.value_type === 'event' && !event_metadata?.insight_property_ids) {
          const draftInsightPropertyIds = Array.isArray(event_metadata?.insight_property_id)
            ? event_metadata?.insight_property_id
            : [event_metadata?.insight_property_id];

          set(andFilter, 'event_metadata.insight_property_ids', draftInsightPropertyIds);
          set(andFilter, 'event_metadata.insight_property_id', null);
        }
      }
    }

    // Migrate Fallback
    if (!boSettings?.fallback) {
      boSettings.fallback = FALLBACK_SELECTION.MOST_SEEN.value;
    }

    if (isEmpty(contentSources?.groups) && boSettings.itemTypeId) {
      cloneMediaTemplateDetail.contentSources = {
        groups: [
          {
            ...initGroup('Group 1'),
            ...boSettings,
            groupId: randomCSGroupId,
            ranking: boSettings.ranking === null ? GET_TOP_RANKING_DEFAULT : boSettings.ranking,
          },
        ],
        expanded: [],
        isLoadingDataBO: false,
      };
    }
  }

  // Migrates trackingModule
  if (!trackingModule && utmSettings) {
    cloneMediaTemplateDetail.trackingModule = {
      mode: 'utm',
      data: utmSettings,
    };
  }

  // Migrates thumbnail capture
  if (settings && settings.thumbnailCapture == null) {
    cloneMediaTemplateDetail.settings.thumbnailCapture = true;
  }

  // Migrates Delay Showing
  if (settings && settings.delayShowing == null) {
    cloneMediaTemplateDetail.settings.delayShowing = DATA_MIGRATE.settings.delayShowing;
  }

  // Migrate Auto close template
  if (settings && settings.autoCloseTemplate == null) {
    cloneMediaTemplateDetail.settings.autoCloseTemplate = DATA_MIGRATE.settings.autoCloseTemplate;
  }

  if (!journeySettings) {
    // Migrate journey settings
    cloneMediaTemplateDetail.journeySettings = JOURNEY_SETTINGS_DEFAULT;
  }

  if (viewPages.some(viewPage => viewPage.rows.length)) {
    cloneMediaTemplateDetail.isInitial = true;
  }

  // Migrate current thumbnail to viewPages -> thumbnail (if there is no thumbnail in viewPages)
  if (viewPages.every(viewPage => !viewPage.thumbnail) && cloneMediaTemplateDetail.thumbnail) {
    const isActiveViewPageIndex = viewPages.findIndex(viewPage => viewPage.settings?.isActive);
    cloneMediaTemplateDetail.viewPages[isActiveViewPageIndex].thumbnail = cloneMediaTemplateDetail.thumbnail;
  }

  return cloneMediaTemplateDetail;
};

export const migrateGradients = (gradientInfo: Record<string, any>) => {
  if (!gradientInfo) return [];
  const { secondGradientColor, secondGradientColorLocation, firstGradientColorLocation, firstGradientColor } =
    gradientInfo || {};

  return [
    {
      gradientColor: firstGradientColor,
      gradientColorLocation: firstGradientColorLocation,
    },
    {
      gradientColor: secondGradientColor,
      gradientColorLocation: secondGradientColorLocation,
    },
  ];
};

export const getDynamicInfoByBlock = (block: TBlock) => {
  let dynamic: Record<string, any> | null = null;
  let dynamicLink: Record<string, any> | null = null;
  let path: string | null = null;
  let pathLink: string | null = null;

  switch (block.type) {
    case STANDARDS_BLOCKS.TEXT.name:
      path = 'settings.dynamic.data';
      pathLink = 'settings.link.data';
      break;
    case IMAGE.name:
    case BUTTON.name:
    case VIDEO.name:
    case RATING.name:
    case OPTIN_FIELDS.name: {
      path = 'settings.dynamic';
      break;
    }
    default:
      break;
  }

  if (path) {
    dynamic = get(block, path, {});
    if (isEmpty(dynamic)) dynamic = null;
  }

  if (pathLink) {
    dynamicLink = get(block, pathLink, {});
    if (isEmpty(dynamicLink)) dynamicLink = null;
  }

  return {
    ...(path !== null && dynamic !== null
      ? {
          path,
          dynamic,
        }
      : { path: null, dynamic: null }),

    ...(pathLink !== null && dynamicLink !== null
      ? {
          pathLink,
          dynamicLink,
        }
      : { pathLink: null, dynamicLink: null }),
  };
};

/**
 * Get block responsive settings: Merge default settings with settings of each responsive breakpoint
 * @param {Object} options - The options object.
 * @param {string} options.breakpoint - The breakpoint at which to retrieve settings.
 * @param {TSettings} options.settings - The base settings object.
 * @returns {Object} - The responsive settings for the specified breakpoint.
 */
export const getBlockResponsiveSettings = ({
  breakpoint,
  settings,
}: {
  breakpoint: string;
  settings: Partial<TSettings>;
}) => {
  return omit(merge(cloneDeep(settings), cloneDeep(settings.breakpoints?.[breakpoint]) || {}), 'breakpoints');
};
