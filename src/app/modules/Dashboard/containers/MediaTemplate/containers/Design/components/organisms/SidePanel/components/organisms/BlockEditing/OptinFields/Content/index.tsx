// Libraries
import React, { memo, useRef, useCallback, useEffect, useLayoutEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';

// Molecules
import { Collapse } from 'app/components/molecules';
import { EditorScript } from 'app/components/molecules/EditorScript';
import { Panel } from 'app/components/molecules/Collapse';

// Organisms
import { FormFieldsSetting } from '../../../FormFieldsSetting';

// Selectors
import {
  selectBlockSelected,
  selectSidePanel,
  selectTemplate,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Styled
import { ContentWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';
import { ButtonGroupSetting } from '../../../ButtonGroupSetting';

// Translation
import { translations } from 'locales/translations';

// Constants
import {
  SIDE_PANEL_COLLAPSE,
  LAYOUT_TEMPLATE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { DATA_MIGRATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

// Organisms
import { SyncData } from '../../../SyncData';
import { syncDataConfig } from '../../../SyncData/constants';
import { LimitedSubmitSetting } from '../../../LimitedSubmitButton';
import { SwitchLabel } from '../../../../molecules';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/OptinFields/Content/index.tsx';

interface ContentProps {}

const { FORM_FIELDS, SUBMIT_BUTTON, SUCCESS_SCRIPTS, SYNC_DATA, LIMIT_SUBMIT } = SIDE_PANEL_COLLAPSE;

export const Content: React.FC<ContentProps> = memo(props => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateBlockSetting, setSidePanel, setSidePanelSettings } = mediaTemplateDesignActions;

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const template = useSelector(selectTemplate);
  const { activePanel, settings: sidePanelSettings } = useSelector(selectSidePanel);
  const { optinFields } = sidePanelSettings;
  const blockSettings = blockSelected?.settings;
  const renderLimitRef = useRef(false);
  // const styles = blockSettings?.styles;
  const {
    fields,
    stylesSettings,
    styles,
    buttonSettings,
    actions: {
      FieldsElementButton: { scripts },
    },
    dynamic = DATA_MIGRATE['optin_fields'].dynamic,
    limitedSubmit,
  } = blockSettings;

  // Init default active panel
  useEffect(() => {
    return () => {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            editingFieldId: '',
          },
        }),
      );
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (optinFields.editingFieldId) {
      if (optinFields.editingFieldId === 'submitButton') {
        onChangeActiveKey(SUBMIT_BUTTON);
      } else {
        onChangeActiveKey(FORM_FIELDS);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [optinFields.editingFieldId]);

  useLayoutEffect(() => {
    if (!renderLimitRef.current && activePanel === LIMIT_SUBMIT) {
      renderLimitRef.current = true;
    }
  }, [activePanel]);

  // Handlers
  const onChangeFormFieldsSetting = useCallback(
    (fieldsSettings = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.fields = {
                  ...draft.fields,
                  ...fieldsSettings,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeFormFieldsSetting',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeLimitSubmit = useCallback(
    (limitedSubmit = {}, ignoreUndoAction = false) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting({
              ...produce(blockSelected.settings, draft => {
                draft.limitedSubmit = limitedSubmit;
              }),
              ignoreUndoAction,
            }),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeLimitSubmit',
          args: { limitedSubmit },
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeEditorScript = useCallback(
    (scriptsSettings = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                // debounce
                draft.actions.FieldsElementButton.scripts = scriptsSettings;
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: onChangeEditorScript.name,
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeButtonStyling = useCallback(
    (
      buttonSettings = {},
      buttonStyles = {},
      elementOptions = {},
      blockStyles = {},
      element = {},
      dynamicSetting = {},
      ignoreUndoAction = false,
    ) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting({
              ...produce(blockSelected.settings, draft => {
                draft.buttonSettings = {
                  ...draft.buttonSettings,
                  ...buttonSettings,
                };
                draft.buttonStyles = {
                  ...draft.buttonStyles,
                  ...buttonStyles,
                };
                draft.blockStyles = {
                  ...draft.blockStyles,
                  ...blockStyles,
                };
                draft.actions.FieldsElementButton = {
                  ...draft.actions.FieldsElementButton,
                  ...element,
                };
                draft.actions.FieldsElementButton.options = {
                  ...draft.actions.FieldsElementButton.options,
                  ...elementOptions,
                };
                draft.dynamic = {
                  ...draft.dynamic,
                  ...dynamicSetting,
                };
              }),
              ignoreUndoAction,
            }),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeSyncDataColumn = useCallback(
    (dataSource = [], isAction) => {
      try {
        if (blockSelected) {
          if (blockSelected.settings.syncData) {
            dispatch(
              updateBlockSetting(
                produce(blockSelected.settings, draft => {
                  draft.syncData.columnMapping = dataSource;
                  if (isAction) {
                    draft.syncData.isAction = isAction;
                  }
                }),
              ),
            );
          } else {
            dispatch(
              updateBlockSetting(
                produce(blockSelected.settings, draft => {
                  draft.syncData = {
                    columnMapping: dataSource,
                    isAction: isAction ? isAction : false,
                    event: {},
                    source: {},
                  };
                }),
              ),
            );
          }
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );
  const onChangeSyncDataBO = useCallback(
    (BO = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.syncData = {
                  ...draft.syncData,
                  ...BO,
                };
                draft.syncData.isAction = true;
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );
  const onChangeSyncData = useCallback(
    (data = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.syncData = {
                  ...draft.syncData,
                  ...data,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeDefault',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );
  const onChangeActiveKey = (key: string | string[]) => {
    dispatch(
      setSidePanel({
        activePanel: key as string,
      }),
    );
  };

  return (
    <ContentWrapper>
      <Collapse accordion defaultActiveKey={FORM_FIELDS} activeKey={activePanel} onChange={onChangeActiveKey}>
        <Panel header={t(translations.formFields.title)} key={FORM_FIELDS}>
          <div className="ants-mb-4">
            <SwitchLabel
              label={t(translations.displayLabels.title)}
              checked={blockSettings?.stylesSettings?.displayLabels}
              onChange={checked => {
                dispatch(
                  updateBlockSetting(
                    produce(blockSelected.settings, draft => {
                      draft.stylesSettings = {
                        ...draft.stylesSettings,
                        displayLabels: checked,
                      };
                      draft.fields = Object.entries(draft.fields).reduce(
                        (res, cur) => ({
                          ...res,
                          [cur[0]]: {
                            ...(cur[1] as Object),
                            displayLabel: checked,
                          },
                        }),
                        {},
                      );
                    }),
                  ),
                );
              }}
            />
          </div>
          <FormFieldsSetting
            stylesSettings={stylesSettings}
            styles={styles}
            onChange={onChangeFormFieldsSetting}
            fields={fields}
            buttonSettings={buttonSettings}
            onChangeActiveKey={onChangeActiveKey}
          />
        </Panel>
        <Panel header={t(translations.submitButton.title)} key={SUBMIT_BUTTON}>
          <ButtonGroupSetting
            dynamicSetting={dynamic}
            buttonSettings={blockSettings.buttonSettings}
            buttonStyles={blockSettings.buttonStyles}
            blockStyles={blockSettings.blockStyles}
            ButtonElement={blockSettings.actions.FieldsElementButton}
            onChange={onChangeButtonStyling}
            excludeActions={template?.type === LAYOUT_TEMPLATE.INLINE.name ? ['close'] : []}
          />
        </Panel>
        <Panel header={t('Sync data to Event & Business Object')} key={SYNC_DATA}>
          <SyncData
            fields={fields}
            syncData={blockSettings.syncData ? blockSettings.syncData : syncDataConfig}
            onChangeColumnMapping={onChangeSyncDataColumn}
            onChangeBO={onChangeSyncDataBO}
            onChangeSyncData={onChangeSyncData}
          />
        </Panel>
        <Panel header={t(translations.successScripts.title)} key={SUCCESS_SCRIPTS}>
          <EditorScript
            expandModalLabel={t(translations.successScripts.title)}
            label={t(translations.successScript.description)}
            value={scripts}
            onChange={onChangeEditorScript}
          />
        </Panel>
        <Panel header={t(translations.titleLimitedSubmit.title)} key={LIMIT_SUBMIT}>
          <LimitedSubmitSetting data={limitedSubmit} onChange={onChangeLimitSubmit} fields={fields} />
        </Panel>
        {!renderLimitRef.current && (
          <LimitedSubmitSetting data={limitedSubmit} onChange={onChangeLimitSubmit} fields={fields} isTempMode />
        )}
      </Collapse>
    </ContentWrapper>
  );
});
