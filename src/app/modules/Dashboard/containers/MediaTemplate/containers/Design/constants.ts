// Utils
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';
import { TDataType } from './types';

// Images
// Assets
import shakePhone from 'assets/images/icons/shake-phone.png';
import OTPIcon from 'assets/images/icons/OTP.png';
import giftIcon from 'assets/images/icons/gift.png';

export const DEFAULT_THUMBNAIL =
  'https://st-media-template.antsomi.com/upload/2022/11/15/0c299bb2-ea9d-46e2-ac8c-1d5cb9c0407f.png';
export const CAMPAIGN_BLOCKS_DROPPABLE_ID = 'root';
export const BLOCK_ITEM = 'block_item';
export const DROPPABLE_SAVED_BLOCK_ID = 'saved_blocks';
export const DROPPABLE_STANDARDS_BLOCK_ID = 'standards';
export const MESSAGE_PREVIEW_CLOSED = 'preview-antsomi-cdp-campaign-closed';
export const MAX_DYNAMIC_INDEX = 90;

export const BO_STATUS = {
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: getTranslateMessage(translations.messageError.BOArchive.message),
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: getTranslateMessage(translations.messageError.BODelete.message),
  },
};
export const ATTRIBUTE_STATUS = {
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: getTranslateMessage(translations.messageError.attributeArchive.message),
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: getTranslateMessage(translations.messageError.attributeDelete.message),
  },
};

export const PROMOTION_POOL_STATUS = {
  default: {
    value: 'default',
    label: 'Default',
    errorMessage: getTranslateMessage(translations.messageError.promotionPoolDeactivated.message),
  },
};

export const COLLECTION_STATUS = {
  2: {
    value: 2,
    label: 'Disable',
    errorMessage: getTranslateMessage(translations.messageError.collectionDisable.message),
  },
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: getTranslateMessage(translations.messageError.collectionArchive.message),
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: getTranslateMessage(translations.messageError.collectionDelete.message),
  },
};

export const DESIGN_TEMPLATE_MODE = {
  GENERAL: 'GENERAL',
  CODE: 'CODE',
};

export const PREFIX_EL_NAME = 'mt';

export const MOBILE_SETTING = {
  width: 391,
  height: 660,
};

export const OBJECT_TYPE = {
  ITEM_PROPERTY: 1,
  SEGMENT: 2,
  JOURNEY: 3,
  AM: 4,
  DATAFLOW: 5,
  DATASOURCE: 6,
  MEDIA_TEMPLATE: 7,
  PROMOTION_POOL: 14,
};

export const STANDARDS_BLOCKS = {
  COLUMN: {
    name: 'column',
    label: getTranslateMessage(translations.column.title, 'Column'),
    icon: 'icon-ants-column-2',
  },
  TEXT: {
    name: 'text',
    label: getTranslateMessage(translations.text.title, 'Text'),
    icon: 'icon-ants-text-2',
  },
  IMAGE: {
    name: 'image',
    label: getTranslateMessage(translations.image.title, 'Image'),
    icon: 'icon-ants-image-3',
  },
  BUTTON: {
    name: 'button',
    label: getTranslateMessage(translations.button.title, 'Button'),
    icon: 'icon-ants-button',
    actionKey: 'ButtonElement',
  },
  OPTIN_FIELDS: {
    name: 'optin_fields',
    label: getTranslateMessage(translations.optinFields.title, 'Optin fields'),
    icon: 'icon-ants-optin-fields',
    actionKey: 'FieldsElementButton',
  },
  YES_NO: {
    name: 'yes/no',
    label: getTranslateMessage(translations.yesNo.title, 'Yes/No'),
    icon: 'icon-ants-yesno-button',
    actionKey: {
      yes: 'YesButtonElement',
      no: 'NoButtonElement',
    },
  },
  COUNT_DOWN: {
    name: 'count_down',
    label: getTranslateMessage(translations.countDown.title, 'Count down'),
    icon: 'icon-ants-clock',
    actionKey: 'CountdownElement',
  },
  VIDEO: {
    name: 'video',
    label: getTranslateMessage(translations.video.title, 'Video'),
    icon: 'icon-ants-video',
  },
  COUPON_WHEEL: {
    name: 'coupon_wheel',
    label: getTranslateMessage(translations.couponWheel.title, 'Coupon wheel'),
    icon: 'icon-ants-wheel',
    actionKey: 'CouponWheelElement',
  },
  SPACER: {
    name: 'spacer',
    label: getTranslateMessage(translations.spacer.title, 'Spacer'),
    icon: 'icon-ants-spacer',
  },
  DIVIDER: {
    name: 'divider',
    label: getTranslateMessage(translations.divider.title, 'Divider'),
    icon: 'icon-ants-divider',
  },
  ICON: {
    name: 'icon',
    label: getTranslateMessage(translations.icon.title, 'Icon'),
    icon: 'icon-ants-emoji',
  },
  HTML: {
    name: 'html',
    label: getTranslateMessage(translations.html.title, 'HTML'),
    icon: 'icon-ants-html',
  },
  SLIDE_SHOW: {
    name: 'slide-show',
    label: getTranslateMessage(translations.slideShow.title, 'Slide show'),
    icon: 'icon-ants-slide-show',
    actionKey: 'SlideShowElement',
  },
  GROUP: {
    name: 'group',
    label: getTranslateMessage(translations.group.title, 'Group'),
    icon: 'icon-ants-box-outline',
    actionKey: 'GroupElement',
  },
  RATING: {
    name: 'rating',
    label: getTranslateMessage(translations.rating.title, 'Rating'),
    icon: 'icon-ants-star',
  },
  TABLE: {
    name: 'table',
    label: getTranslateMessage(translations.tableBlock.title, 'Table'),
    icon: 'icon-ants-table', // To do: replace icon
    enable: true,
  },
  SHAKE_AND_WIN: {
    name: 'shake-and-win',
    label: getTranslateMessage(translations.shakeAndWin.title, 'Shake and win'),
    icon: '',
    actionKey: 'ShakeAndWinElement',
    image: shakePhone,
  },
  SURPRISE_TREASURE_HUNT: {
    name: 'surprise-treasure-hunt',
    label: getTranslateMessage(translations.luckyGiftBox.title, 'Lucky Gift Box'),
    icon: '',
    isWorkingBreak: true,
    actionKey: 'SurpriseTreasureHuntElement',
    image: giftIcon,
  },
  OTP_VERIFICATION: {
    name: 'otp-verification',
    label: getTranslateMessage('', 'OTP Verification'),
    icon: '',
    actionKey: 'OTPVerificationElement',
    image: OTPIcon,
  },
  HTML_CODE_MODE: {
    name: 'html-code-mode',
    label: getTranslateMessage(translations.html.title, 'HTML'),
    icon: 'icon-ants-html',
  },
};

export const SIDE_PANEL_TYPE = {
  SETTINGS: {
    name: 'settings',
    label: getTranslateMessage(translations.settings.title),
    icon: '',
  },
  BLOCKS: {
    name: 'blocks',
    label: getTranslateMessage(translations.blocks.title),
    icon: '',
  },
  ...STANDARDS_BLOCKS,
};

export const VIEW_PAGE = {
  YES_NO: {
    name: 'yes/no',
    label: 'Yes/No',
  },
  OPTIN: {
    name: 'optin',
    label: 'Optin',
  },
  SUCCESS: {
    name: 'success',
    label: 'Success',
  },
};

export const LAYOUT_TEMPLATE = {
  POP_UP: {
    id: 1,
    name: 'pop_up',
    label: getTranslateMessage(translations.popUp.title),
  },
  FLOATING_BAR: {
    id: 2,
    name: 'floating_bar',
    label: getTranslateMessage(translations.floatingBar.title),
  },
  FULL_SCREEN: {
    id: 3,
    name: 'full_screen',
    label: getTranslateMessage(translations.fullscreen.title),
  },
  INLINE: {
    id: 4,
    name: 'inline',
    label: getTranslateMessage(translations.inline.title),
  },
  SLIDE_IN: {
    id: 5,
    name: 'slide_in',
    label: getTranslateMessage(translations.slideIn.title),
  },
  GAMIFIED: {
    id: 6,
    name: 'gamified',
    label: getTranslateMessage(translations.gamified.title),
  },
};

export const ALIGN_TYPE = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right',
} as const;

export const ITEMS_ALIGN_TYPE = {
  STRETCH: 'stretch',
  TOP: 'flex-start',
  CENTER: 'center',
  BOTTOM: 'flex-end',
  BASELINE: 'baseline',
};

export const TABLE_MISSING_DATA_DISPLAY = {
  NULL: 'null',
  ZERO: 'zero',
  NO_DATA: 'no-data',
  BLANK: 'blank',
};

export const TABLE_CELL_VERTICAL_ALIGN_TYPE = {
  MIDDLE: 'middle',
  TOP: 'top',
  BOTTOM: 'bottom',
};

export const TABLE_DE_MAX_COL = 10;

export const TABLE_ME_MAX_COL = 10;

// not include col with type index
export const TABLE_MAX_COL = TABLE_DE_MAX_COL + TABLE_ME_MAX_COL + 2;

export const POSITION = {
  LEFT_TOP: {
    value: 'left top',
    label: getTranslateMessage(translations.leftTop.title),
  },
  LEFT_CENTER: {
    value: 'left center',
    label: getTranslateMessage(translations.leftCenter.title),
  },
  LEFT_BOTTOM: {
    value: 'left bottom',
    label: getTranslateMessage(translations.leftBottom.title),
  },
  RIGHT_TOP: {
    value: 'right top',
    label: getTranslateMessage(translations.rightTop.title),
  },
  RIGHT_CENTER: {
    value: 'right center',
    label: getTranslateMessage(translations.rightCenter.title),
  },
  RIGHT_BOTTOM: {
    value: 'right bottom',
    label: getTranslateMessage(translations.rightBottom.title),
  },
  CENTER_TOP: {
    value: 'center top',
    label: getTranslateMessage(translations.centerTop.title),
  },
  CENTER_CENTER: {
    value: 'center center',
    label: getTranslateMessage(translations.centerCenter.title),
  },
  CENTER_BOTTOM: {
    value: 'center bottom',
    label: getTranslateMessage(translations.centerBottom.title),
  },
};

export const IMAGE_REPEAT = {
  NO_REPEAT: {
    value: 'no-repeat',
    label: getTranslateMessage(translations.noRepeat.title),
  },
  REPEAT: {
    value: 'repeat',
    label: getTranslateMessage(translations.repeat.title),
  },
  REPEAT_HORIZONTAL: {
    value: 'repeat-x',
    label: getTranslateMessage(translations.repeatHorizontal.title),
  },
  REPEAT_VERTICAL: {
    value: 'repeat-y',
    label: getTranslateMessage(translations.repeatVertical.title),
  },
};

export const IMAGE_SIZE = {
  COVER: {
    value: 'cover',
    label: getTranslateMessage(translations.cover.title),
  },
  CONTAIN: {
    value: 'contain',
    label: getTranslateMessage(translations.contain.title),
  },
  AUTO: {
    value: 'auto',
    label: getTranslateMessage(translations.auto.title),
  },
};

export const SIDE_PANEL_SECTION = {
  CLOSE_BUTTON_STYLING: 'close-button-styling-section',
};

export const SIDE_PANEL_COLLAPSE = {
  CONTENT_SOURCES: 'CONTENT_SOURCES',
  CUSTOM_COLORS: 'CUSTOM_COLORS',
  CUSTOM_CSS: 'CUSTOM_CSS',
  CUSTOM_JAVASCRIPT: 'CUSTOM_JAVASCRIPT',
  SUBMIT_BUTTON: 'SUBMIT_BUTTON',
  YES_BUTTON: 'YES_BUTTON',
  NO_BUTTON: 'NO_BUTTON',
  FORM_FIELDS: 'FORM_FIELDS',
  LABEL_STYLING: 'LABEL_STYLING',
  SUCCESS_SCRIPTS: 'SUCCESS_SCRIPTS',
  LIMIT_SUBMIT: 'LIMIT_SUBMIT',
  CONTAINER_STYLING: 'CONTAINER_STYLING',
  HTML: 'HTML',
  CSS: 'CSS',
  TEMPLATE_DETAILS: 'TEMPLATE_DETAILS',
  DISPLAY_SETTINGS: 'DISPLAY_SETTINGS',
  TRACKING_MODULE: 'TRACKING_MODULE',
  VIEW_STYLING: 'VIEW_STYLING',
  COUPON_WHEEL_SETTING: 'COUPON_WHEEL_SETTING',
  COUPON_SETTING: 'COUPON_SETTING',
  SYNC_DATA: 'SYNC_DATA',
  BUSINESS_OBJECT: 'BUSINESS_OBJECT',
  STYLE_STYLING: 'STYLE_STYLING',
  DYNAMIC_CONTENT: 'DYNAMIC_CONTENT',
  GROUP_STYLING: 'GROUP_STYLING',
  DISPLAY_CONDITION: 'DISPLAY_CONDITION',
  TEXT_STYLING: 'TEXT_STYLING',
  RESPONSIVE_SETTING: 'RESPONSIVE_SETTING',
  RADIO_CHECKBOX_STYLING: 'RADIO_CHECKBOX_STYLING',
  SURPRISE_TREASURE_HUNT: 'surprise-treasure-hunt',
  SURPRISE_TREASURE_HUNT_CELLS: 'surprise-treasure-hunt-cells',
  SHAKE_AND_WIN: 'shake-and-win',
  SHAKE_TRIGGER_SETTING: 'shake-trigger-setting',
  RESEND_BUTTON: 'resend-button',
};

export const LEFT_PANEL_TABS = {
  LAYERS: {
    value: 'layers',
    label: getTranslateMessage(translations.layers.title),
    icon: 'icon-ants-layers',
  },
  // COMPONENTS: {
  //   value: 'components',
  //   label: getTranslateMessage(translations.components.title),
  //   icon: 'icon-ants-library-add',
  // },
};

export const DYNAMIC_LINK_SETTING_KEY = {
  EVENT: 'event',
  INDEX: 'index',
  LINK_TYPE: 'linkType',
  OPEN_NEW_TAB: 'openNewTab',
  ATTRIBUTE: 'attribute',
  DYNAMIC_CONTENT_TYPE: 'type',
  PROMOTION_POOL: 'pool',
  SOURCE: 'source',
  TEXT: 'text',
  TITLE: 'title',
  URL: 'url',
};
export const DYNAMIC_LINK_TYPE = {
  DYNAMIC: 'dynamic',
  STATIC: 'static',
};

export const DYNAMIC_CONTENT_SETTING_KEY = {
  EVENT: 'event',
  INDEX: 'index',
  ATTRIBUTE: 'attribute',
  DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS: 'numberFormatSettings',
  DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS: 'datetimeFormatSettings',
  DISPLAY_FORMAT_TYPE: 'dfType',
  DYNAMIC_CONTENT_TYPE: 'type',
  PROMOTION_POOL: 'pool',
  SOURCE: 'source',
  FUNCTION: 'customFunction',
  PERNAME: 'templateName',
  SAVE_AS_TEMPLATE: 'saveAsTemplate',
  DATA_TYPE: 'dataType',
};
export const ATTRIBUTE_NUMBERIC_FORMAT = {
  DISPLAY_TYPE: 'attribute-numberic-type',
  CURRENCY: 'attribute-numberic-currency',
  DECIMAL_PLACES: 'attribute-numberic-decimal-places',
  GROUPING_SEPARATOR: 'attribute-numberic-grouping-separator',
  DECIMAL_SEPARATOR: 'attribute-numberic-decimal-separator',
};
export const DYNAMIC_CONTENT_TYPE: {
  [key: string]: {
    index: number;
    label: string;
    value: string;
  };
} = {
  BO_SETTINGS: {
    index: 4,
    label: '',
    value: 'bo-settings',
  },
  CUSTOMER_ATTRIBUTE: {
    index: 1,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.customerAttr),
    value: 'customer-attribute',
  },
  EVENT_ATTRIBUTE: {
    index: 2,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.eventAttr),
    value: 'event-attribute',
  },
  PROMOTION_CODE: {
    index: 3,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.promotionCode),
    value: 'promotion-code',
  },
  VISITOR_ATTRIBUTE: {
    index: 0,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.visitorAttr),
    value: 'visitor-attribute',
  },
};
export const DATA_TYPE: {
  [key: string]: {
    index: number;
    label: string;
    value: string;
  };
} = {
  NUMBER: {
    index: 0,
    label: 'Number',
    value: 'number',
  },
  DATE_TIME: {
    index: 1,
    label: 'Datetime',
    value: 'datetime',
  },
  STRING: {
    index: 2,
    label: 'String',
    value: 'string',
  },
};
export const DYNAMIC_CONTENT_ATTR_DF_TYPE: { label: string; value: string; dataType: TDataType }[] = [
  {
    label: getTranslateMessage(translations.dynamicContent.modal.attrDisplayFormat.number),
    value: 'number',
    dataType: 'number',
  },
  {
    label: getTranslateMessage(translations.dynamicContent.modal.attrDisplayFormat.percentage),
    value: 'percentage',
    dataType: 'number',
  },
  {
    label: getTranslateMessage(translations.dynamicContent.modal.attrDisplayFormat.currency),
    value: 'currency',
    dataType: 'number',
  },
  {
    label: getTranslateMessage(translations.dynamicContent.modal.attrDisplayFormat.datetime),
    value: 'datetime',
    dataType: 'datetime',
  },
  {
    label: getTranslateMessage(translations.dynamicContent.modal.attrDisplayFormat.rawString),
    value: 'raw_string',
    dataType: 'string',
  },
];
export const ATTRIBUTE_NUMBERIC_FORMAT_OPTION = {
  DECIMAL_SEPARATOR: {
    COMMA: {
      index: 0,
      label: ',',
      value: ',',
    },
    DOT: {
      index: 1,
      label: '.',
      value: '.',
    },
  },
  GROUPING_SEPARATOR: {
    COMMA: {
      index: 0,
      label: ',',
      value: ',',
    },
    DOT: {
      index: 1,
      label: '.',
      value: '.',
    },
    NONE: {
      index: 2,
      label: 'none',
      value: 'none',
    },
  },
} as const;

export const DISPLAY_CONDITION_OPTIONS = {
  NONE: {
    label: getTranslateMessage(translations.none.title),
    value: '',
  },
  SHOW_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.showWhen),
    value: 'show_when',
  },
  HIDDEN_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.hiddenWhen),
    value: 'hidden_when',
  },
};

export const COLUMN_DISPLAY_CONDITION_OPTIONS = {
  NONE: {
    label: getTranslateMessage(translations.none.title),
    value: '',
  },
  SHOW_ALL_COLUMNS_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.showAllColumnsWhen),
    value: 'show_when',
  },
  SHOW_COLUMN_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.showColumnWhen),
    value: 'show_column_when',
  },
  HIDDEN_ALL_COLUMNS_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.hideAllColumnsWhen),
    value: 'hidden_when',
  },
  HIDDEN_COLUMN_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.hiddenColumnWhen),
    value: 'hidden_column_when',
  },
};

export const SLIDE_DISPLAY_CONDITION_OPTIONS = {
  NONE: {
    label: getTranslateMessage(translations.none.title),
    value: '',
  },
  SHOW_SLIDE_SHOW_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.showSlideshowWhen),
    value: 'show_when',
  },
  SHOW_SLIDE_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.showSlideWhen),
    value: 'show_slide_when',
  },
  HIDDEN_SLIDE_SHOW_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.hiddenSlideshowWhen),
    value: 'hidden_when',
  },
  HIDDEN_SLIDE_WHEN: {
    label: getTranslateMessage(translations.displayCondition.options.hiddenSlideWhen),
    value: 'hidden_slide_when',
  },
};
export const BO_TYPE = {
  DATE_TIME: 'datetime',
  STRING: 'string',
  TEXT: 'text',
  BOOLEAN: 'boolean',
  NUMBER: 'number',
};

export const TYPE_SETTING = {
  OPTIN_FIELDS: 'optinFields',
};

export const BO_DIMENSION_TYPE = [BO_TYPE.DATE_TIME, BO_TYPE.STRING, BO_TYPE.TEXT, BO_TYPE.BOOLEAN];

export const BO_METRICS_TYPE = [BO_TYPE.NUMBER];
