// Libraries
import React, { CSSProperties } from 'react';
import { TContentSourceGroup } from './components/organisms/SidePanel/components/organisms/ContentSources/types';
import { TWheelItem } from './components/organisms/SidePanel/components/organisms/BlockEditing/CouponWheel/Content';
import { TPositionSettings } from './components/organisms/SidePanel/components/organisms/PositionStyleSetting/types';

// Types
import { TSlide } from './slice/types';

export type TPage = {
  name: string;
  label: string;
};

export type TUtmItem = {
  type: string;
  value: string | number;
};

export type TGradient = {
  gradientColor: string;
  gradientColorLocation: number;
};

export type TAnimation = {
  animationType: string;
  animationDelay: number;
  animationDuration: number;
  animationIterationStyle: string;
  animationIterationCount: number;
};

export type TStylesSettings = {
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedBorderRadiusInput: boolean;
  linkedBorderWidthInput: boolean;
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  linkedPositionInput?: boolean;
  positionSuffix?: string;
  linkedMarginInput: boolean;
  marginSuffix: string;
  boxShadowStyle: string;
  boxShadowColor: string;
  boxShadowBlur: string;
  boxShadowSpread: string;
  boxShadowHorizontal: string;
  boxShadowVertical: string;
  boxShadowInset: boolean;
  backgroundColor: string;
  backgroundColorStyle: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  backgroundSize?: string;
  backgroundImageObj?: TBackgroundImageObj;
  gradientType: string;
  radialPosition: string;
  linearAngle: number;
  customId: string;
  customClass: string;
  marginAlign?: TAlign;
  columnGap?: number;
  inputSize?: string;
  hidden?: boolean;
  gapX?: string;
  gapY?: string;
  gapSuffix?: string;
  linkedGapInput?: boolean;
  gradients: Array<{
    gradientColor: string;
    gradientColorLocation: number;
  }>;
  heightSuffix?: string;
  displayCondition: {
    condition: string;
    operator: string;
    value: any;
    type: string;
    attribute: Record<string, any>;
    [key: string]: any;
  };
};

export type THoverStylesSettings =
  | TStylesSettings
  | {
      editHover: boolean;
    };

export interface TStyleBlockSetting extends TStylesSettings {
  editHover: boolean;
  iconColor: string;
}

export interface TErrorStylesSetting extends TStylesSettings {
  defaultErrorStyles: boolean;
  errorPosition: 'top' | 'bottom' | 'field';
}

export type TRowSettings = {
  type: string;
  name: string;
  widths: number[];
};

export type TColumnSettings = {
  width: number;
};

export type THTMlSettings = {
  showModal: boolean;
  htmlAreaValue: {
    value: string;
  };
};

export type TTextSettings = {
  textStyling: {};
};

export type TSlideDirection = 'horizontal' | 'vertical';

export type TSlideShowSettings = {
  totalItems: number;
  displayItems: number;
  displayStyle: string;
  columnGap: number;
  skipItems: number;
  autoSlide: string;
  slideDelay: number;
  slideDuration: number;
  slideLoop: string;
  slideDirection: TSlideDirection;
  slideTransition: string;
  navigationSettings: Record<string, any>;
  navigationStyles: React.CSSProperties;
};

export type TCouponWheelSettings = {
  sections: TWheelItem[];
  sectionColors: string[];
  actions: {
    CouponWheelElement: Record<string, any>;
  };
};

export type TDynamic = {
  isDynamic: boolean;
  field: string;
  index: number;
  type: string;
};

export type TImageSettings = {
  dynamic: {
    previewUrl: TDynamic;
    altText: TDynamic;
    linkedUrl: TDynamic;
  };
};

export type TColumnFormat = 'number' | 'heatmap' | 'bar';

export type TColumnTableBlock = {
  id: string;
  value: string;
  label: string;
  dataType: string;
  colType: 'dimension' | 'metric' | 'index';
  placement: 'left' | 'right' | 'center';
  displayFormat?: {
    type: TColumnFormat;
  };
  width: string;
};

export type TShowCellMissingData = 'null' | 'zero' | 'no-data' | 'blank';

export type TCellMissingDataDisplayOptions = {
  [MissingType in TShowCellMissingData]: string;
};

export type TTableSettings = {
  boId: number;
  columns: TColumnTableBlock[];
  dimensions: TColumnTableBlock[];
  metrics: TColumnTableBlock[];
  component: string;
  table: {
    settings: {
      cellAlignItems: string;
      headerBackground: string;
      cellBorderColor: string;
      oddRowColor: string;
      evenRowColor: string;
      showTop: number;
      cellPadding: number;
    };
    styles: CSSProperties;
  };
  sortBy: {
    columnId: string;
    order: 'desc' | 'asc';
  };
  tableHeader: {
    settings: {
      showHeader: boolean;
      wrapText: boolean;
    };
    styles: CSSProperties;
  };
  tableBody: {
    settings: {
      showMissingDataType: TShowCellMissingData;
      showRowNumbers: boolean;
      wrapText: boolean;
    };
    styles: CSSProperties;
  };
  tableFooter: {
    settings: {};
    styles: CSSProperties;
  };
  filters: TFilters;
  blockStyles: CSSProperties;
  blockStylesSettings: TStylesSettings;
  blockHoverStyles: CSSProperties;
  blockHoverStylesSettings: THoverStylesSettings;
};

export type TDynamicTextSettings = {
  dynamic: {
    data: Record<string, Record<string, any>>;
    highlight: boolean;
    selectedId: string;
  };
  link: {
    data: Record<string, Record<string, any>>;
    selectedId: string;
  };
  defaultDynamicIndex?: number;
};

export type TTextStyling = {
  textStyling: {
    selectLineDisplay: Record<string, Record<string, any>>;
    isEllipsisText: boolean;
    color?: string;
    textAlign?: string;
  };
};

export type TResponsiveSettings = {
  notStackOnMobile?: boolean;
  hideOnDesktop?: boolean;
  hideOnMobile?: boolean;
};

export type TBreakpoint = {
  // Globals
  blockStyles?: React.CSSProperties;
  blockStylesSettings?: Partial<TStylesSettings>;
  blockHoverStyles?: React.CSSProperties;
  blockHoverStylesSettings?: any;
  styles?: React.CSSProperties;
  stylesSettings?: Record<string, any>;
  outerContainerStyles?: React.CSSProperties;

  // Button
  buttonStyles?: Record<string, any>;
  buttonSettings?: Record<string, any>;
  buttonHoverStyles?: Record<string, any>;
  buttonHoverSettings?: Record<string, any>;

  // Text
  textStyling?: TTextStyling['textStyling'];
};

export type TSettings<T = any> = {
  [key: string]: any;
  component?: string;
  name?: string;
  fromIndex?: number;
  defaultIndex?: number;
  blockStyles: React.CSSProperties;
  blockStylesSettings: TStylesSettings;
  blockHoverStyles?: React.CSSProperties;
  blockHoverStylesSettings?: any;
  responsiveSettings?: TResponsiveSettings;
  breakpoints?: Record<string, TBreakpoint>;
} & {
  [P in keyof T]?: T[P];
};

export type TBackgroundImageObj = {
  name: string;
  previewUrl: string;
};

export type TAutomaticallyClose = {
  enable: boolean;
  time: any;
};

export type TDelayShowingCondition = {
  type: string;
  value: number;
};

export type TDelayShowing = {
  enable?: boolean;
  conditions: TDelayShowingCondition[];
};

export type TAutoCloseTemplate = {
  enable: boolean;
  at: string;
  after: number;
};

export type TGlobalSettings = {
  mobile: number;
  thumbnailCapture: boolean;
  interactionCookieType: string;
  successCookieType: string;
  seenCookieType: string;
  interactionCookieDuration: number;
  successCookieDuration: number;
  seenCookieDuration: number;
  crossSubdomainCookies: number;
  showAffiliateLink: number;
  floatingBarPosition: string;
  slideOpen: boolean;
  autoToggle: number;
  pageSlide: boolean;
  enableLock: boolean;
  lockMethod: string;
  countdown: number;
  gamified: number;
  smartSuccess: boolean;
  chatbot: unknown[];
  analyticsAccountId: number;
  gaId: string;
  uaId: string;
  attentionActivation: boolean;
  customColors: string[];
  slideToggleState: boolean;
  bgClose: boolean;
  mcTitle: string;
  mcMetaDescription: string;
  mcImageUrl: string;
  enableWebFonts: number;
  lazyLoadImage?: boolean;
  delayShowing: TDelayShowing;
  autoCloseTemplate: TAutoCloseTemplate;
  positionSettings?: TPositionSettings;
};

export type TBusinessObjectSettings = {
  itemTypeId: number | null;
  itemTypeName: string | null;
  itemTypeDisplay: string | null;
  filters: TFilters;
  ranking: TRanking | null;
  fallback: string;
};

export type TContentSourceSettings = {
  groups: TContentSourceGroup[];
  expanded: string[];
  isLoadingDataBO: boolean;
  isExcludeDuplicate?: boolean;
};

export type TUtmTrackingSettings = {
  source: TUtmItem[];
  medium: TUtmItem[];
  campaign: TUtmItem[];
  term: TUtmItem[];
  content: TUtmItem[];
};

export type TTrackingModuleData = {
  source: TUtmItem[];
  medium: TUtmItem[];
  campaign: TUtmItem[];
  term: TUtmItem[];
  content: TUtmItem[];
};

export type TOperatorValue =
  | 'greater_than'
  | 'greater_than_equal'
  | 'less_than'
  | 'less_than_equal'
  | 'not_equals'
  | 'equals'
  | 'between'
  | 'exists'
  | 'not_exists'
  | 'contains'
  | 'doesnt_contain'
  | 'start_with'
  | 'not_start_with'
  | 'end_with'
  | 'not_end_with'
  | 'before_date'
  | 'after_date'
  | 'equal_time_ago'
  | 'not_equal_time_ago'
  | 'before_time_ago'
  | 'after_time_ago'
  | 'between_time_ago'
  | 'matches'
  | 'not_matches'
  | 'includes'
  | 'doesnt_include'
  | null;

export type TDataType =
  | 'string'
  | 'number'
  | 'datetime'
  | 'array'
  | 'boolean'
  | 'object'
  | 'text'
  | 'array_string'
  | 'array_number'
  | 'array_datetime'
  | 'array_text'
  | 'suggestion'
  | 'date'
  | 'object_id'
  | 'unique'
  | null;

export function isDataType(dataString: string | null): dataString is TDataType {
  const dataTypes = [
    'string',
    'number',
    'datetime',
    'array',
    'boolean',
    'object',
    'text',
    'array_string',
    'array_number',
    'array_datetime',
    'array_text',
    'suggestion',
    'date',
    'object_id',
    'unique',
    null,
  ];

  if (!dataTypes.includes(dataString)) {
    throw new Error('Datatype not in list Type');
  }

  return true;
}

export type VisitorMetadata = {
  item_type_id: string | number;
  item_type_name: string;
  item_property_name: string;
};

export type CustomerMetadata = VisitorMetadata;

export type EventMetadata = {
  insight_property_id: any | any[] | null;
  insight_property_ids?: string[] | null;
  event_action_id: string | null;
  event_category_id: string | null;
  event_tracking_name: string | null;
  item_type_id: string | null;
  item_type_name: string | null;
  item_property_name: string | null;
  item_property_label: string | null;
  event_property_syntax: string | null;
};

export type SelectOption = {
  value?: string;
  label?: string;
};

export type EventBoFieldMetadata = EventMetadata & {
  field_code_bo?: string;
  field_label_bo?: string;
  useBo?: boolean;
};

export type TFilter = {
  value: any;
  operator: TOperatorValue;
  data_type: TDataType;
  column: string | null;
  condition_type: 'comp_attr' | null;
  value_type: 'event' | 'normal' | 'visitor' | 'customer';
  time_unit?: 'DAY' | 'HOUR';
  event_metadata?: EventMetadata;
  visitor_metadata?: VisitorMetadata;
  customer_metadata?: CustomerMetadata;
  extend?: any;
};

export type TFilters = {
  OR: {
    AND: TFilter[];
  }[];
};

export type TRanking = {
  type: 'algorithms' | 'custom';
  algorithms: {
    sort: 'mix' | 'order';
    value: {
      value: string;
      quantity: number;
      by?: string;
      last?: number;
      to?: string;
      sort_type?: 'desc' | 'asc';
      [key: string]: any;
    }[];
    filters: string[];
  };
  custom: string;
};

export type TContainer = {
  linkedMarginInput: boolean;
  marginSuffix: string;
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedBorderRadiusInput: boolean;
  gradientType: string;
  radialPosition: string;
  gradients: TGradient[];
  linearAngle: number;
  boxShadowStyle: string;
  boxShadowColor: string;
  boxShadowBlur: string;
  boxShadowSpread: string;
  boxShadowHorizontal: string;
  boxShadowVertical: string;
  boxShadowInset: boolean;
  overflowHidden: boolean;
  overlayColor: string;
  containerWidth: number;
  containerWidthSuffix: string;
  imageLayerFirst: boolean;
  linkedBorderWidthInput: boolean;
  backgroundColor: string;
  backgroundColorStyle: string;
  backgroundPosition: string;
  backgroundRepeat: string;
  backgroundSize: string;
  styles: {
    borderTopWidth: string;
    borderRightWidth: string;
    borderBottomWidth: string;
    borderLeftWidth: string;
    borderTopLeftRadius: string;
    borderTopRightRadius: string;
    borderBottomRightRadius: string;
    borderBottomLeftRadius: string;
    paddingTop: string;
    paddingRight: string;
    paddingLeft: string;
    paddingBottom: string;
    background: string;
    borderColor: string;
    borderStyle: string;
    boxShadow: string;
  };
  backgroundImageObj: TBackgroundImageObj;
  containerSize: string;
};

export type TCloseButton = {
  paddingTop: string;
  paddingRight: string;
  paddingLeft: string;
  paddingBottom: string;
  marginTop: string;
  marginRight: string;
  marginLeft: string;
  marginBottom: string;
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedBorderRadiusInput: boolean;
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  linkedMarginInput: boolean;
  marginSuffix: string;
  boxShadowStyle: string;
  boxShadowColor: string;
  boxShadowBlur: string;
  boxShadowSpread: string;
  boxShadowHorizontal: string;
  boxShadowVertical: string;
  boxShadowInset: boolean;
  closeButtonActive: boolean;
  buttonTitle: string;
  buttonStyle: string;
  buttonSize: string;
  buttonWeight: string;
  buttonPosition: string;
  iconColor: string;
  backgroundColor: string;
  hoverIconColor: string;
  hoverBackgroundColor: string;
  boxShadow: string;
};

export type TCustomCSS = {
  applyCssPrefix: boolean;
  rawEditorOutput: string;
};

export type TCustomJS = {
  rawEditorOutput: string;
};

export type TFullScreenContainer = {
  gradientType: string;
  gradients: TGradient[];
  radialPosition: string;
  linearAngle: number;
  imageLayerFirst: boolean;
  linkedBorderWidthInput: boolean;
  backgroundColor: string;
  backgroundColorStyle: string;
  backgroundPosition: string;
  backgroundRepeat: string;
  backgroundSize: string;
  styles: {
    borderTopWidth?: string;
    borderRightWidth?: string;
    borderBottomWidth?: string;
    borderLeftWidth?: string;
    background?: string;
    borderColor?: string;
    borderStyle?: string;
  };
  backgroundImageObj: TBackgroundImageObj;
};

export type TSlideClosedContainer = {
  displayStyle: string;
  position: string;
  slideDirection: string;
  linkedPaddingInput: boolean;
  linkedMarginInput: boolean;
  paddingSuffix: string;
  marginSuffix: string;
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedBorderRadiusInput: boolean;
  gradientType: string;
  gradients: TGradient[];
  radialPosition: string;
  linearAngle: number;
  boxShadowStyle: string;
  boxShadowColor: string;
  boxShadowBlur: string;
  boxShadowSpread: string;
  boxShadowHorizontal: string;
  boxShadowVertical: string;
  boxShadowInset: boolean;
  toggleClosedText: string;
  linkedBorderWidthInput: boolean;
  backgroundColor: string;
  backgroundColorStyle: string;
  icon: string;
  iconColor: string;
  iconSize: number;
  iconSpacing: number;
  positionSettings: TPositionSettings;
  contentAnimation: TAnimation;
  toggleAnimation: TAnimation;
  styles: {
    borderTopWidth: string;
    borderRightWidth: string;
    borderBottomWidth: string;
    borderLeftWidth: string;
    borderTopLeftRadius: string;
    borderTopRightRadius: string;
    borderBottomRightRadius: string;
    borderBottomLeftRadius: string;
    paddingTop: string;
    paddingRight: string;
    paddingLeft: string;
    paddingBottom: string;
    marginTop: string;
    marginRight: string;
    marginLeft: string;
    marginBottom: string;
    background: string;
    borderColor: string;
    borderStyle: string;
    cursor: string;
    userSelect: any;
    fontSize: string;
    fontStyle: string;
    fontWeight: number;
    fontFamily: string;
    lineHeight: string;
    letterSpacing: string;
    textTransform: any;
    textDecoration: string;
    color: string;
  };
  viewMinimized: boolean;
};

export type TViewSettings<T = any> = {
  [key: string]: any;
  isActive: boolean;
  global: boolean;
  container: TContainer;
  closeButton: TCloseButton;
  openButton: Record<string, any>;
  customCSS: TCustomCSS;
  customJS: TCustomJS;
  slideClosedContainer: TSlideClosedContainer;
  fullscreenContainer: TFullScreenContainer;
  sidebarRootActive: boolean;
} & {
  [P in keyof T]?: T[P];
};
export interface BlockProps {
  parentId?: string;
  id: string;
  type: string;
  idx: number;
  settings: TSettings<THTMlSettings | TSlideShowSettings | TTextStyling>;
  isPreviewMode?: boolean;
  namespace?: string;
  slides?: TSlide[];
  rowId?: string;
  columnId?: string;
}

export type TAlign = 'left' | 'center' | 'right';

export type TSuffix = 'px' | '%';

export type TVerifiedSubmitConditions = {
  OR: Array<{
    AND: Array<TVerifiedSubmitConditionsAnd>;
  }>;
};

export type TVerifiedSubmitConditionsAnd = {
  field: string | null;
  hash: string;
  operator: string;
  valueType: string;
  value: any;
  filters: {
    OR: Array<{
      AND: Array<any>;
    }>;
  };
};
