// Libraries
import React, { useRef, useEffect, useState } from 'react';
import { BlockProps } from '../../../../../../types';
import { IconName, IconPrefix } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useDispatch, useSelector } from 'react-redux';

//Action
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Icons
import { WarningIcon } from 'app/components/icons';

// Selectors
import { selectSidePanel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Styled
import { CouponWheelBlockWrapper } from './styled';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Utils
import { random } from 'app/utils/common';
import { useGetListPromotionPool } from 'app/queries/PromotionPool';
import { buildPromotionSectionsData, buildPromotionTagData } from '../../../utils';

interface CouponWheelBlockProps extends BlockProps {}

const STANDARD_WIDTH = 690;
const ERROR_POSITION = {
  TOP: 'top',
  CENTER: 'center',
  BOTTOM: 'bottom',
};
// const MAX_PULL = 50;
const PULL_UPPER = 8;
const PULL_SIZE_STEP = 0.05;

export const CouponWheelBlock: React.FC<CouponWheelBlockProps> = props => {
  const dispatch = useDispatch();

  // Actions
  const { setData } = mediaTemplateDesignActions;

  const { id, settings, namespace } = props;

  const {
    sections,
    wheelHeight,
    pullDirection,
    labelStyles,
    outerWheelColor,
    innerWheelColor,
    flipperColor,
    innerWheelImage,
    innerWheelIcon,
    errorMessage,
  } = settings;

  // Selectors
  const sidePanel = useSelector(selectSidePanel);

  const { data: promotionPools = { rows: [], total: 0 } } = useGetListPromotionPool();

  // State
  const wheelRef = useRef<HTMLDivElement>(null);
  const wheelWrapperRef = useRef<HTMLDivElement>(null);
  const wheelErrorRef = useRef<HTMLDivElement>(null);
  const wheelErrorPlaceholderRef = useRef<HTMLDivElement>(null);
  const isOverSize = useRef(false);
  const isPullScale = useRef(false);
  const wrapperClientRect = wheelWrapperRef.current ? wheelWrapperRef.current.getBoundingClientRect() : null;
  const wheelRect = wheelRef.current ? wheelRef.current.getBoundingClientRect() : null;
  const [wrapperSize, setWrapperSize] = useState({
    width: wrapperClientRect?.width || STANDARD_WIDTH,
    height: wrapperClientRect?.height || STANDARD_WIDTH,
  });

  //using for re-fresh coupon wheel
  const [refreshPage, setRefreshPage] = useState(0);

  useEffect(() => {
    //after change wheelHeight, pullDirection , this page will reload
    setTimeout(() => {
      setRefreshPage(prev => prev + 1);
    }, 50);
  }, [wheelHeight, pullDirection]);

  isOverSize.current =
    wrapperClientRect && wheelRect
      ? pullDirection && (isOverSize.current = Math.round(wrapperClientRect.width) <= Math.round(wheelRect.width))
      : false;
  isPullScale.current = wrapperClientRect ? wrapperClientRect.height > wrapperClientRect.width : false;

  const [deltaScale, setDeltaScale] = useState(wrapperSize.height / STANDARD_WIDTH);

  useEffect(() => {
    const wrapperObserver = new ResizeObserver((entrys: ResizeObserverEntry[]) => {
      if (!wheelWrapperRef?.current) {
        return;
      }
      for (let entry of entrys) {
        const cr = entry.contentRect;
        setWrapperSize(cr);
        setDeltaScale(Math.min(cr.width, cr.height) / STANDARD_WIDTH);
      }
    });

    wheelWrapperRef?.current && wrapperObserver.observe(wheelWrapperRef?.current);

    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      wheelWrapperRef?.current && wrapperObserver.unobserve(wheelWrapperRef?.current);
    };
  }, []);

  useEffect(() => {
    if (promotionPools?.total) {
      dispatch(
        setData({
          promotionPool: promotionPools,
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotionPools]);

  // const wheelSize = wheelHeight + (deltaScale < 1 ? 3.5 * Math.min(Math.abs(pullDirection), MAX_PULL) : 0);
  let realDetaScale =
    pullDirection && isOverSize.current ? deltaScale + PULL_SIZE_STEP * Math.abs(pullDirection) : deltaScale;
  let realPull = isOverSize.current ? 0 : pullDirection;

  if (pullDirection && wheelRect && wrapperClientRect && STANDARD_WIDTH * realDetaScale > wrapperClientRect?.height) {
    realDetaScale = wrapperClientRect?.height / STANDARD_WIDTH;
    const pullScale = (realDetaScale - deltaScale) / PULL_SIZE_STEP;
    realPull = pullDirection < 0 ? pullDirection + pullScale : pullDirection - pullScale;
  }

  useEffect(() => {
    if (
      wheelErrorRef.current &&
      sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
      sidePanel.blockSelectedId === id
    ) {
      wheelErrorRef.current.style.width = STANDARD_WIDTH * realDetaScale + 'px';
    }

    if (
      wheelErrorPlaceholderRef.current &&
      sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
      sidePanel.blockSelectedId === id
    ) {
      wheelErrorPlaceholderRef.current.style.width = STANDARD_WIDTH * realDetaScale + 'px';
    }
  }, [errorMessage, sidePanel.activePanel, sidePanel.blockSelectedId, id, realDetaScale, refreshPage]);

  const getTanFromDegrees = degrees => {
    return Math.tan((degrees * Math.PI) / 180);
  };

  const renderMessageWithPlaceholder = position => {
    return (
      <>
        {renderMessage(position)} {renderMessage(position, false)}
      </>
    );
  };

  const renderMessage = (position, isMainMessage = true) => {
    return position === errorMessage?.position ? (
      <div
        ref={isMainMessage ? wheelErrorRef : wheelErrorPlaceholderRef}
        className={`${
          isMainMessage ? namespace : 'ants-invisible placeholder'
        }-coupon-wheel-error ants-flex ants-justify-center ants-items-center ants-z-1
        ${
          position === ERROR_POSITION.CENTER
            ? 'ants-absolute ants-inset-0'
            : position === ERROR_POSITION.TOP
            ? isMainMessage
              ? 'ants-absolute ants-top-2'
              : 'ants-relative ants-top-2'
            : isMainMessage
            ? 'ants-absolute ants-bottom-2'
            : 'ants-relative ants-bottom-2'
        }`}
        style={{
          fontSize: 13,
          display:
            sidePanel.blockSelectedId === id &&
            sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
            !props.isPreviewMode
              ? 'flex'
              : 'none',
          ...(isOverSize.current
            ? {
                left: pullDirection > 0 ? realPull * PULL_UPPER : 'unset',
                right: pullDirection < 0 ? -realPull * PULL_UPPER : 'unset',
              }
            : {
                left: '50%',
                right: 'unset',
                transform: `translateX(calc(-50% + ${realPull * PULL_UPPER}px))`,
              }),
        }}
      >
        <div
          className="ants-px-15px ants-py-10px ants-bg-gray-4 
            ants-text-cus-primary ants-w-fit ants-h-fit
            ants-flex ants-items-center ants-gap-10px
            ants-rounded ants-relative ants-text-center"
        >
          <WarningIcon
            style={{
              width: 21,
              height: 21,
            }}
          />
          {errorMessage.message}
        </div>
      </div>
    ) : null;
  };

  const renderItems = () => {
    // const percent = ((1 - (2 * (172.5 * getTanFromDegrees(360 / (sections.length * 2)))) / 345) / 2) * 100 - 0.5;
    const sectionLength = sections.length;
    const percent = ((1 - getTanFromDegrees(360 / (sectionLength * 2))) / 2) * 100 - 0.1;

    return sections.map((section, idx) => (
      <div
        key={idx}
        className="wheel-item"
        style={{
          transform: `rotate(${(360 / sectionLength) * idx}deg)`,
          clipPath: `polygon(${percent}% 0, ${100 - percent}% 0, 50% 50.2%, ${percent}% 0)`,
          background: section.backgroundColor,
        }}
      >
        <span
          className="wheel-item-label"
          style={{
            ...labelStyles,
          }}
        >
          {section.label}
        </span>
      </div>
    ));
  };

  const sectionLength = sections.length;
  const wheelItemDeg = 360 / sectionLength;

  return (
    <>
      {renderMessageWithPlaceholder(ERROR_POSITION.TOP)}
      {renderMessageWithPlaceholder(ERROR_POSITION.CENTER)}
      <CouponWheelBlockWrapper
        id={`${namespace}-${settings.component}--${id}`}
        // style={{ height: (wrapperWidth * wheelSize) / 100 + 'px' }}
        style={{
          height: wheelHeight + 'vh',
        }}
        ref={wheelWrapperRef}
        data-wheel={buildPromotionSectionsData(sections, promotionPools)}
        data-tag={buildPromotionTagData(sections, promotionPools)}
      >
        <div
          id={`wheel-content-wrapper-scale-${random(8)}`}
          className={`wheel-content-wrapper-scale`}
          style={{
            position: pullDirection && isOverSize.current ? 'absolute' : 'relative',
            height: wheelHeight + 'vh',
            width: STANDARD_WIDTH * realDetaScale + 'px',
            // width: `${wheelSize}%`,
            // overflow: 'hidden',
            // left: `${pullDirection * 8.2}px`,
            // left: isOverSize.current
            //   ? pullDirection > 0
            //     ? pullDirection > MAX_PULL
            //       ? PULL_UPPER * (pullDirection - MAX_PULL)
            //       : 0
            //     : 'unset'
            //   : PULL_UPPER * pullDirection,
            // right: isOverSize.current
            //   ? pullDirection < 0
            //     ? pullDirection < -MAX_PULL
            //       ? -PULL_UPPER * (pullDirection + MAX_PULL)
            //       : 0
            //     : 'unset'
            //   : -PULL_UPPER * pullDirection,
            left: pullDirection > 0 ? realPull * PULL_UPPER : 'unset',
            right: pullDirection < 0 ? -realPull * PULL_UPPER : 'unset',
          }}
        >
          <div
            ref={wheelRef}
            className="wheel-content-wrapper"
            style={{
              width: STANDARD_WIDTH,
              height: STANDARD_WIDTH,
              // transform: `scale(${(wheelSize * deltaScale) / 100}, ${(wheelSize * deltaScale) / 100})`,
              // WebkitTransform: `scale(${(wheelSize * deltaScale) / 100}, ${(wheelSize * deltaScale) / 100})`,
              transform: `scale(${realDetaScale}, ${realDetaScale})`,
              WebkitTransform: `scale(${realDetaScale}, ${realDetaScale})`,
              // transformOrigin: 'left top',
              overflow: 'hidden',
            }}
          >
            <div
              className="wheel-flipper"
              style={{
                background: flipperColor,
              }}
            />

            <div
              className="wheel-content"
              style={{
                borderRadius: '50%',
                borderColor: outerWheelColor,
                background: outerWheelColor,
                transform: `rotate(${90 - wheelItemDeg / 2}deg)`,
                left: -12,
              }}
            >
              {sections.map((i, idx) => (
                <div
                  key={idx}
                  className="wheel-item-decor"
                  style={{
                    transform: `rotate(${wheelItemDeg * (idx + 0.5) - (idx !== sections.length - 1 ? 0.1 : -0.1)}deg)`,
                  }}
                >
                  <span
                    className="wheel-item-decor-inner"
                    style={{
                      background: outerWheelColor,
                    }}
                  ></span>
                </div>
              ))}
              <div className="wheel-content-inner">
                <div
                  className="wheel-center"
                  style={{
                    background: innerWheelColor,
                    transform: `translate(-50%, -50%) rotate(${wheelItemDeg / 2 - 90}deg)`,
                  }}
                >
                  {innerWheelIcon?.icon || innerWheelImage.previewUrl ? (
                    <>
                      {innerWheelIcon?.icon ? (
                        <FontAwesomeIcon
                          icon={(innerWheelIcon?.icon || '').split(' ') as [IconPrefix, IconName]}
                          style={{
                            fontSize: 90,
                            width: 90,
                            height: 90,
                            color: innerWheelIcon.iconColor,
                          }}
                        />
                      ) : (
                        <img
                          src={innerWheelImage?.previewUrl || ''}
                          style={{
                            maxWidth: 100,
                            maxHeight: 75,
                          }}
                          alt=""
                        />
                      )}
                    </>
                  ) : (
                    <div className="spin2-win">
                      <div className="spin2-win-inner" />
                    </div>
                  )}
                </div>

                {renderItems()}
              </div>
            </div>
          </div>
        </div>
      </CouponWheelBlockWrapper>
      {renderMessageWithPlaceholder(ERROR_POSITION.BOTTOM)}
    </>
  );
};
