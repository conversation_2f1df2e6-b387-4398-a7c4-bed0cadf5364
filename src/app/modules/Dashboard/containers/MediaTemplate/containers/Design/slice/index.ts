/* eslint-disable no-loop-func */
// Libraries
import { PayloadAction } from '@reduxjs/toolkit';
import moment from 'moment';
import cloneDeep from 'lodash/cloneDeep';
import { get, has, initial, isEmpty, mergeWith } from 'lodash';

// Libs
import undoable, { groupByActionTypes } from 'app/libs/redux-undo';
import set from 'lodash/set';

// Saga
import { mediaTemplateDesignSaga } from './saga';

// Utils
import { getObjSafely, random, reorder } from 'app/utils/common';
import { useInjectReducer, useInjectSaga } from 'utils/redux-injectors';
import { createSlice } from 'utils/@reduxjs/toolkit';

// Images
import Product1Image from 'assets/images/sample/product-1.jpeg';
import Product2Image from 'assets/images/sample/product-2.jpeg';

// Types
import {
  MediaTemplateDesignState,
  TToolbar,
  TRemoveBlockPayload,
  TCloneBlockPayload,
  TUpdateViewSetting,
  TViewPage,
  TWorkspace,
  TTemplate,
  THandleImportFromView,
  TDataPayload,
  THTMLCssPayload,
  TUpdateBlockByIdPayload,
  TUpdateBlockFieldsByIdPayload,
  TSetMediaTemplateDetail,
  TExportInfo,
  TSaveData,
  TSetSidePanelSettings,
  TSidePanel,
  TLeftSidePanel,
  TUpdateBlockTextPayload,
  TBlock,
  TUpdateCurrentPageTreeBlocksPayload,
  TUpdateBlockFieldsSelectedPayload,
  TTrackingModule,
  TAddBlockPayload,
  TReorderBlockPayload,
} from './types';
import { TBusinessObjectSettings, TGlobalSettings, TUtmTrackingSettings } from '../types';

// Query Client
import { queryClient } from 'index';

// Models
import { SavedBlock } from 'app/models';

// Config
import {
  BLOCK_SETTING_DEFAULT,
  BUSINESS_OBJECT_SETTINGS_DEFAULT,
  CONTENT_SOURCES_SETTINGS_DEFAULT,
  UTM_TRACKING_SETTING_DEFAULT,
  GLOBAL_SETTINGS_DEFAULT,
  SLIDE_ELEMENTS_DEFAULT,
  VIEW_SETTING_DEFAULT,
  VIEW_YES_NO_SETTING_DEFAULT,
  TRACKING_MODULE_DEFAULT,
  JOURNEY_SETTINGS_DEFAULT,
  OBJECTIVE_TYPES_DEFAULT,
  MIGRATE_BLOCK_SETTINGS,
} from '../config';

// Constants
import {
  DESIGN_TEMPLATE_MODE,
  LAYOUT_TEMPLATE,
  LEFT_PANEL_TABS,
  SIDE_PANEL_COLLAPSE,
  SIDE_PANEL_TYPE,
  STANDARDS_BLOCKS,
  VIEW_PAGE,
} from '../constants';
import { DATE_TIME_FORMAT } from 'constants/moment';
import { SIDE_PANEL_TABS } from '../components/organisms/SidePanel/constants';
import { DEVICE_TYPE } from 'app/modules/Dashboard/containers/MediaTemplate/constants';
import { QUERY_KEYS } from 'constants/queries';

// Utils
import { getCurrentPageIdx, removeBlocks, getCloneBlocks, migrateMediaTemplateDetail } from './utils';
import { TContentSourceGroup } from '../components/organisms/SidePanel/components/organisms/ContentSources/types';
import { initGroup as initContentSourceGroup } from '../components/organisms/SidePanel/components/organisms/ContentSources/utils';

const { COLUMN, SLIDE_SHOW, BUTTON, IMAGE, OPTIN_FIELDS, VIDEO, RATING, YES_NO, TEXT } = STANDARDS_BLOCKS;

export const INIT_PAGE_CODE_MODE = {
  id: VIEW_PAGE.OPTIN.name,
  title: VIEW_PAGE.OPTIN.label,
  rows: [],
  html: '',
  styles: '',
  url: '',
  thumbnail: '',
  blocks: {},
  tree: {},
  errors: {},
  settings: VIEW_SETTING_DEFAULT,
  codeModeSettings: {
    width: 100,
    widthSuffix: '%',
    height: 100,
    heightSuffix: '%',
    htmlAreaValue: BLOCK_SETTING_DEFAULT.HTML_CODE_MODE.htmlAreaValue,
    displayArea: 'safe-area',
  },
};

export const initialState: MediaTemplateDesignState = {
  isDraggingBlock: false,
  isLoadingWorkspace: false,
  isSavingTemplate: false,
  isShowErrorAlert: false,
  isShowWarningAlert: false,
  draggingBlockId: '',
  exportInfo: {
    isExporting: false,
    isExportPreview: false,
    isExportSaveAs: false,
    captureOnSave: '',
    exportPages: [],
  },
  leftSidePanel: {
    isCollapsed: true,
    activeTab: LEFT_PANEL_TABS.LAYERS.value,
  },
  sidePanel: {
    isCollapsed: false,
    type: SIDE_PANEL_TYPE.SETTINGS.name,
    blockSelectedId: '',
    activeTab: SIDE_PANEL_TABS.BASIC.name,
    activePanel: SIDE_PANEL_COLLAPSE.VIEW_STYLING,
    activeSectionId: '',
    slideShowId: '',
    slideId: '',
    settings: {
      optinFields: {
        editingFieldId: '',
        errors: {},
      },
    },
  },
  toolbar: {
    viewPageSelected: VIEW_PAGE.OPTIN.name,
    isPreviewMode: false,
    isOpenSaveAs: false,
  },
  workspace: {
    id: random(20),
    name: `Untitled template ${new Date().toISOString()}`,
    mode: DESIGN_TEMPLATE_MODE.GENERAL,
    isExternal: false,
    isInitial: false,
    thumbnail: '',
    defaultThumbnailIndex: 0,
    namespace: 'template',
    description: '',
    viewPages: [
      {
        id: VIEW_PAGE.YES_NO.name,
        title: VIEW_PAGE.YES_NO.label,
        rows: [],
        html: '',
        styles: '',
        url: '',
        thumbnail: '',
        blocks: {},
        tree: {},
        errors: {},
        settings: VIEW_YES_NO_SETTING_DEFAULT,
      },
      {
        id: VIEW_PAGE.OPTIN.name,
        title: VIEW_PAGE.OPTIN.label,
        rows: [],
        html: '',
        styles: '',
        url: '',
        thumbnail: '',
        blocks: {},
        tree: {},
        errors: {},
        settings: VIEW_SETTING_DEFAULT,
      },
      {
        id: VIEW_PAGE.SUCCESS.name,
        title: VIEW_PAGE.SUCCESS.label,
        rows: [],
        html: '',
        styles: '',
        url: '',
        thumbnail: '',
        blocks: {},
        tree: {},
        errors: {},
        settings: VIEW_SETTING_DEFAULT,
      },
    ],
    template: {
      id: LAYOUT_TEMPLATE.FULL_SCREEN.id,
      type: LAYOUT_TEMPLATE.FULL_SCREEN.name,
      name: LAYOUT_TEMPLATE.FULL_SCREEN.label,
    },
    errors: {},
    warnings: {},
    deviceType: DEVICE_TYPE.DESKTOP.value,
    settings: GLOBAL_SETTINGS_DEFAULT,
    boSettings: BUSINESS_OBJECT_SETTINGS_DEFAULT,
    contentSources: CONTENT_SOURCES_SETTINGS_DEFAULT,
    utmSettings: UTM_TRACKING_SETTING_DEFAULT, // todo: remove
    trackingModule: TRACKING_MODULE_DEFAULT,
    journeySettings: JOURNEY_SETTINGS_DEFAULT,
    // objectiveTypes: OBJECTIVE_TYPES_DEFAULT,
    categories: {
      goal: OBJECTIVE_TYPES_DEFAULT,
    },
    isEmbed: false,
  },
  data: {
    listSavedImages: [],
    promotionPool: {
      rows: [],
      total: 0,
    },
    promotionCodeAttributes: [],
    listFallbackBO: [],
    dataTracking: [
      {
        value: '#STORY_ID#',
      },
      {
        value: '#CAMPAIGN_ID#',
      },
      {
        value: '#VARIANT_ID#',
      },
      {
        value: '#STORY_NAME#',
      },
      {
        value: '#CAMPAIGN_NAME#',
      },
      {
        value: '#VARIANT_NAME#',
      },
    ],
    sources: [],
    events: [],
    attributes: [],
    personalizationAttributes: { customer: [], visitor: [] },
  },
  saveData: {
    template_setting: {},
  },
};

const slice = createSlice({
  name: 'mediaTemplateDesign',
  initialState,
  reducers: {
    // New actions
    addBlock(state, action: PayloadAction<TAddBlockPayload>) {
      const currentPageIdx = getCurrentPageIdx(state);
      const currentPage = state.workspace.viewPages[currentPageIdx];
      const { dragBlockType, dropBlockId, dropIndex, savedBlockId } = action.payload;

      let savedBlock: any = null;

      const dropBlock = (currentPage.blocks || {})[dropBlockId] || {};

      const block: TBlock = {
        id: random(20),
        type: dragBlockType,
        settings: {} as any,
        createdAt: moment().format(DATE_TIME_FORMAT.DATE_TIME),
      };

      if (!!savedBlockId) {
        const savedBlocksResponse: any = queryClient.getQueryData([QUERY_KEYS.GET_SAVED_BLOCKS]) || [];

        const savedBlocks: SavedBlock[] = savedBlocksResponse.pages.reduce(
          (savedBlocks: SavedBlock[], page: { body: SavedBlock[] }) => [...savedBlocks, ...page.body],
          [],
        );

        savedBlock = savedBlocks?.find(({ id }) => +id === +savedBlockId);
      }

      switch (dragBlockType) {
        case STANDARDS_BLOCKS.COLUMN.name:
          block.settings = BLOCK_SETTING_DEFAULT.ROW;
          const columnBlock = {
            id: random(20),
            type: 'col',
            settings: BLOCK_SETTING_DEFAULT.COLUMN,
            createdAt: moment().format(DATE_TIME_FORMAT.DATE_TIME),
          };

          currentPage.blocks = {
            ...currentPage.blocks,
            [block.id]: block,
            [columnBlock.id]: columnBlock,
          };

          const treeRoot = get(currentPage, 'tree.root', []);
          treeRoot.splice(dropIndex, 0, block.id);

          currentPage.tree = {
            ...currentPage.tree,
            root: treeRoot,
            [block.id]: [columnBlock.id],
            [columnBlock.id]: [],
          };

          state.sidePanel = {
            ...state.sidePanel,
            blockSelectedId: block.id,
            activeTab: SIDE_PANEL_TABS.CONTENT.name,
            type: block.type,
          };

          break;
        case STANDARDS_BLOCKS.TEXT.name:
          block.settings = BLOCK_SETTING_DEFAULT.TEXT;
          break;
        case STANDARDS_BLOCKS.BUTTON.name:
          block.settings = BLOCK_SETTING_DEFAULT.BUTTON;
          break;
        case STANDARDS_BLOCKS.OPTIN_FIELDS.name:
          block.settings = BLOCK_SETTING_DEFAULT.OPTIN_FIELDS;
          break;
        case STANDARDS_BLOCKS.IMAGE.name:
          block.settings = BLOCK_SETTING_DEFAULT.IMAGE;
          break;
        case STANDARDS_BLOCKS.COUPON_WHEEL.name:
          block.settings = BLOCK_SETTING_DEFAULT.COUPON_WHEEL;
          break;
        case STANDARDS_BLOCKS.COUNT_DOWN.name:
          block.settings = BLOCK_SETTING_DEFAULT.COUNT_DOWN;
          break;
        case STANDARDS_BLOCKS.DIVIDER.name:
          block.settings = BLOCK_SETTING_DEFAULT.DIVIDER;
          break;
        case STANDARDS_BLOCKS.HTML.name:
          block.settings = BLOCK_SETTING_DEFAULT.HTML;
          break;
        case STANDARDS_BLOCKS.ICON.name:
          block.settings = BLOCK_SETTING_DEFAULT.ICONS;
          break;
        case STANDARDS_BLOCKS.SPACER.name:
          block.settings = BLOCK_SETTING_DEFAULT.SPACER;
          break;
        case STANDARDS_BLOCKS.VIDEO.name:
          block.settings = BLOCK_SETTING_DEFAULT.VIDEO;
          break;
        case STANDARDS_BLOCKS.YES_NO.name:
          block.settings = BLOCK_SETTING_DEFAULT.YES_NO;
          break;
        case STANDARDS_BLOCKS.RATING.name:
          block.settings = BLOCK_SETTING_DEFAULT.RATING;
          break;
        case STANDARDS_BLOCKS.GROUP.name:
          block.settings = BLOCK_SETTING_DEFAULT.GROUP;

          (currentPage.tree || {})[block.id] = [];
          break;
        case STANDARDS_BLOCKS.TABLE.name:
          block.settings = BLOCK_SETTING_DEFAULT.TABLE;
          break;
        case STANDARDS_BLOCKS.SHAKE_AND_WIN.name:
          block.settings = BLOCK_SETTING_DEFAULT.SHAKE_AND_WIN;
          break;
        case STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.name:
          block.settings = BLOCK_SETTING_DEFAULT.SURPRISE_TREASURE_HUNT;
          break;
        case STANDARDS_BLOCKS.SLIDE_SHOW.name:
          block.settings = BLOCK_SETTING_DEFAULT.SLIDE_SHOW;

          const treeBlock: string[] = [];
          const treeParentBlock = get(currentPage, `tree.${dropBlockId}`, []);

          treeParentBlock.splice(dropIndex, 0, block.id);

          if (!!savedBlock) {
            block.settings = savedBlock.settings as any;
            block.type = savedBlock.type;
            block.savedBlockId = savedBlock.id;

            if (savedBlock.slides && savedBlock.slides.length) {
              cloneDeep(savedBlock.slides).forEach(slide => {
                const cloneSlide = cloneDeep(slide);
                cloneSlide.id = random(20);

                const cloneSlideTree: any[] = [];

                (currentPage.blocks || {})[cloneSlide.id] = cloneSlide;

                slide.elements.forEach(element => {
                  const cloneElement = cloneDeep(element);
                  cloneElement.id = random(20);

                  (currentPage.blocks || {})[cloneElement.id] = cloneElement;

                  cloneSlideTree.push(cloneElement.id);
                });

                (currentPage.tree || {})[cloneSlide.id] = cloneSlideTree;
                treeBlock.push(cloneSlide.id);
              });
            }

            if (!!savedBlock.children) {
              const { blockParentId = '', blocks = {}, tree = {} } = savedBlock.children;
              const cloneBlocks = {};
              const cloneTree = {};

              tree[blockParentId]?.forEach(childId => {
                const { childrenBlocks, childrenTree } = getCloneBlocks({
                  parentId: block.id,
                  blockId: childId,
                  blocks,
                  tree,
                });

                Object.assign(cloneBlocks, childrenBlocks);

                mergeWith(cloneTree, childrenTree, (objValue, srcValue) => {
                  if (Array.isArray(objValue)) {
                    return objValue.concat(srcValue);
                  }
                });
              });

              Object.assign(currentPage.blocks || {}, cloneBlocks);
              Object.assign(currentPage.tree || {}, cloneTree);
              treeBlock.push(...(cloneTree[block.id] || []));
            }
          } else {
            Array.from({ length: 5 }).forEach((_, slideIndex) => {
              const slideBlock = {
                id: random(20),
                type: 'slide',
                settings: {
                  name: 'Slide',
                  type: 'slide',
                  editor: 'SlideElementEditor',
                  component: 'SlideElement',
                  blockStyles: {} as any,
                  blockStylesSettings: {
                    hidden: false,
                  } as any,
                },
                createdAt: moment().format(DATE_TIME_FORMAT.DATE_TIME),
              };

              (currentPage.blocks || {})[slideBlock.id] = slideBlock;
              (currentPage.tree || {})[slideBlock.id] = [];

              // Add Default elements to slide
              SLIDE_ELEMENTS_DEFAULT.forEach(element => {
                const cloneElement: any = JSON.parse(JSON.stringify(element));
                cloneElement.id = random(20);

                // Set auto index for dynamic
                set(cloneElement, 'settings.blockStylesSettings.displayCondition.index', slideIndex + 1);

                // Set auto index
                switch (cloneElement.type) {
                  case BUTTON.name:
                  case IMAGE.name:
                    if (get(cloneElement, 'settings.dynamic', null)) {
                      for (let key in cloneElement.settings.dynamic) {
                        cloneElement.settings.dynamic[key].index = slideIndex + 1;
                      }
                    }
                    break;
                  case TEXT.name:
                    cloneElement.settings.defaultDynamicIndex = slideIndex + 1;
                    break;
                  default:
                    break;
                }

                if (cloneElement.type === STANDARDS_BLOCKS.IMAGE.name) {
                  const image = slideIndex % 2 === 0 ? Product1Image : Product2Image;

                  set(cloneElement, 'settings.uploadedImage.previewUrl', image);
                }

                (currentPage.blocks || {})[cloneElement.id] = cloneElement;

                (currentPage.tree || {})[slideBlock.id].push(cloneElement.id);
              });

              treeBlock.push(slideBlock.id);
            });
          }

          (currentPage.blocks || {})[block.id] = block;

          currentPage.tree = {
            ...currentPage.tree,
            [block.id]: treeBlock,
            [dropBlockId]: treeParentBlock,
          };
          break;

        case STANDARDS_BLOCKS.OTP_VERIFICATION.name:
          block.settings = BLOCK_SETTING_DEFAULT.OTP_VERIFICATION;
          break;

        default:
          break;
      }

      // Migrate block settings
      block.settings = { ...block.settings, ...MIGRATE_BLOCK_SETTINGS };

      // Handle for update settings data
      block.settings = cloneDeep(block.settings);
      // Handle auto set index
      switch (dropBlock.type) {
        case 'slide':
        case 'col':
          const parentBlockTree = Object.values({ ...currentPage.tree }).find(childrenIds =>
            childrenIds.includes(dropBlock.id),
          );
          const cloneBlockSettings = cloneDeep(block.settings) || {};
          const dropBlockIndex = parentBlockTree?.findIndex(blockId => blockId === dropBlock.id) || 0;
          const autoIndex = dropBlockIndex + 1;

          // Set auto index for display condition
          const displayCondition = get(cloneBlockSettings, 'blockStylesSettings.displayCondition');

          if (!!displayCondition) {
            set(block, 'settings.blockStylesSettings.displayCondition.index', autoIndex);
          }

          // Check Which is block has dynamic
          switch (dragBlockType) {
            case BUTTON.name:
            case IMAGE.name:
            case VIDEO.name:
            case RATING.name:
            case OPTIN_FIELDS.name:
            case YES_NO.name:
            case TEXT.name:
              set(block, 'settings.defaultDynamicIndex', autoIndex);

              break;
            default:
              break;
          }
          break;

        default:
          break;
      }
      if (![COLUMN.name, SLIDE_SHOW.name].includes(dragBlockType)) {
        if (!!savedBlock) {
          block.settings = savedBlock.settings as any;
          block.type = savedBlock.type;
          block.savedBlockId = savedBlock.id;

          if (!!savedBlock.children) {
            const { blockParentId = '', blocks = {}, tree = {} } = savedBlock.children;
            const cloneBlocks = {};
            const cloneTree = {};

            tree[blockParentId]?.forEach(childId => {
              const { childrenBlocks, childrenTree } = getCloneBlocks({
                parentId: block.id,
                blockId: childId,
                blocks,
                tree,
              });

              Object.assign(cloneBlocks, childrenBlocks);

              mergeWith(cloneTree, childrenTree, (objValue, srcValue) => {
                if (Array.isArray(objValue)) {
                  return objValue.concat(srcValue);
                }
              });
            });

            Object.assign(currentPage.blocks || {}, cloneBlocks);
            Object.assign(currentPage.tree || {}, cloneTree);
          }
        }

        currentPage.blocks = {
          ...currentPage.blocks,
          [block.id]: block,
        };

        const dropBlockChildIds = get(currentPage, `tree.${dropBlockId}`, []);

        dropBlockChildIds.splice(dropIndex || 0, 0, block.id);

        currentPage.tree = {
          ...currentPage.tree,
          [dropBlockId]: [...dropBlockChildIds],
        };
      }

      state.sidePanel = {
        ...state.sidePanel,
        blockSelectedId: block.id,
        activeTab: SIDE_PANEL_TABS.CONTENT.name,
        type: block.type,
      };
    },
    reorderBlock(state, action: PayloadAction<TReorderBlockPayload>) {
      const currentPageIdx = getCurrentPageIdx(state);
      const currentPage = state.workspace.viewPages[currentPageIdx];
      const { tree = {} } = currentPage;

      const { source, destination } = action.payload;
      const { id: draggableBlockId, index: sourceIdx } = source || {};
      const { id: desDropBlockId, index: desIdx } = destination || {};

      const sourceDropBlockId = Object.keys(tree).find(key => tree[key].includes(draggableBlockId.toString()));

      if (!sourceDropBlockId || !desDropBlockId) {
        return;
      }

      if (sourceDropBlockId === desDropBlockId) {
        let draftDesIdx = sourceIdx < desIdx ? desIdx - 1 : desIdx;

        const newTree = reorder(tree[sourceDropBlockId], sourceIdx, draftDesIdx);

        (currentPage.tree || {})[sourceDropBlockId] = newTree;
      } else {
        (currentPage.tree || {})[sourceDropBlockId]?.splice(sourceIdx, 1);

        // If desDropBlockId not exist in Tree, create it
        if (!(currentPage.tree || {})[desDropBlockId]) {
          (currentPage.tree || {})[desDropBlockId] = [];
        }

        (currentPage.tree || {})[desDropBlockId]?.splice(desIdx, 0, draggableBlockId);
      }
    },
    updateCurrentPageTreeBlocks(state, action: PayloadAction<TUpdateCurrentPageTreeBlocksPayload>) {
      const { tree = [], blocks = [] } = action.payload;
      const { workspace } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      for (const row of tree) {
        const { fieldPath, data } = row;

        if (fieldPath == null) {
          continue;
        }

        set(workspace.viewPages[currentPageIdx] || {}, `tree${fieldPath ? `.${fieldPath}` : ''}`, data);
      }

      for (const row of blocks) {
        const { fieldPath, data } = row;

        if (fieldPath == null) {
          continue;
        }

        set(workspace.viewPages[currentPageIdx] || {}, `blocks${fieldPath ? `.${fieldPath}` : ''}`, data);
      }
    },
    setMediaTemplateDesign(state, action: PayloadAction<Partial<MediaTemplateDesignState>>) {
      if (typeof action.payload === 'object') {
        Object.entries(action.payload).forEach(([key, value]) => {
          state[key] = value;
        });
      }
    },
    setDeviceType(state, action: PayloadAction<number>) {
      state.workspace.deviceType = action.payload;
    },
    /* SidePanel */
    setSidePanel(state, action: PayloadAction<Partial<TSidePanel>>) {
      if (typeof action.payload === 'object') {
        Object.entries(action.payload).forEach(([key, value]) => {
          state.sidePanel[key] = value;
        });
      }
    },
    /* Left SidePanel */
    setLeftSidePanel(state, action: PayloadAction<Partial<TLeftSidePanel>>) {
      if (typeof action.payload === 'object') {
        state.leftSidePanel = {
          ...state.leftSidePanel,
          ...action.payload,
        };
      }
    },
    setSidePanelSettings(state, action: PayloadAction<TSetSidePanelSettings>) {
      const { optinFields } = action.payload;

      if (optinFields) {
        state.sidePanel.settings.optinFields = {
          ...state.sidePanel.settings.optinFields,
          ...optinFields,
        };
      }
    },
    setToolbar(state, action: PayloadAction<TToolbar>) {
      if (typeof action.payload === 'object') {
        Object.entries(action.payload).forEach(([key, value]) => {
          state.toolbar[key] = value;
        });
      }
    },
    switchModeDesign(state, action: PayloadAction<string>) {
      if (!action.payload) return;
      const { workspace, data } = state;

      const currentNameTemplate = workspace?.name;
      const currentTemplateSelected = workspace?.template;

      let initialStatePayload = cloneDeep(initialState);

      initialStatePayload.workspace.name = currentNameTemplate;
      initialStatePayload.workspace.template = currentTemplateSelected;
      initialStatePayload.data = data;

      if (action.payload === DESIGN_TEMPLATE_MODE.CODE) {
        initialStatePayload = {
          ...initialStatePayload,
          workspace: {
            ...initialStatePayload.workspace,
            mode: DESIGN_TEMPLATE_MODE.CODE,
            viewPages: [INIT_PAGE_CODE_MODE],
          },
        };
      }

      return initialStatePayload;
    },
    updateCodeModeSetting(state, action: PayloadAction<any>) {
      const { workspace } = state;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { ignoreUndoAction = false, ...blockSetting } = action.payload;
      set(workspace, 'viewPages[0].codeModeSettings', blockSetting);
      set(workspace, 'viewPages[0].html', blockSetting?.htmlAreaValue?.value);
    },
    setDraggingBlock(state, action: PayloadAction<boolean>) {
      state.isDraggingBlock = action.payload;
    },
    setDraggingBlockId(state, action: PayloadAction<string>) {
      state.draggingBlockId = action.payload;
    },
    setGlobalSettings(state, action: PayloadAction<Partial<TGlobalSettings>>) {
      state.workspace.settings = {
        ...state.workspace.settings,
        ...action.payload,
      };
    },
    setBusinessObjectSettings(
      state,
      action: PayloadAction<Partial<TBusinessObjectSettings & { ignoreUndoAction?: boolean }>>,
    ) {
      const { ignoreUndoAction, ...restOfPayload } = action.payload;

      state.workspace.boSettings = {
        ...state.workspace.boSettings,
        ...restOfPayload,
      };
    },
    setUtmTrackingSetting(state, action: PayloadAction<Partial<TUtmTrackingSettings>>) {
      state.workspace.utmSettings = {
        ...state.workspace.utmSettings,
        ...action.payload,
      };
    },
    setTrackingModuleSetting(state, action: PayloadAction<Partial<TTrackingModule>>) {
      state.workspace.trackingModule = {
        ...state.workspace.trackingModule,
        ...action.payload,
      };
    },
    setIsLoadingDataBO(state, action: PayloadAction<boolean>) {
      state.workspace.contentSources.isLoadingDataBO = action.payload;
    },
    setIsExcludeDuplicate(state, action: PayloadAction<boolean>) {
      state.workspace.contentSources.isExcludeDuplicate = action.payload;
    },
    setContentSourceGroupBOData(state, action: PayloadAction<Record<string, any[]>>) {
      const dataByGroupId = action.payload;

      state.workspace.contentSources.groups.map(g => {
        if (has(dataByGroupId, g.groupId)) {
          g.data = dataByGroupId[g.groupId];
        }

        return g;
      });
    },
    setContentSourceGroup(
      state,
      action: PayloadAction<{ groupId: string; values: Partial<TContentSourceGroup>; ignoreUndoAction?: boolean }>,
    ) {
      const { groupId, values } = action.payload;

      const index = state.workspace.contentSources.groups.findIndex(g => g.groupId === groupId);

      if (index !== -1) {
        const group = state.workspace.contentSources.groups[index];
        state.workspace.contentSources.groups[index] = {
          ...group,
          ...values,
        };
      }
    },
    addContentSource(state) {
      let i = 1;
      let groupName = `Group ${i}`;

      while (state.workspace.contentSources.groups.some(g => g.groupName === groupName)) {
        ++i;
        groupName = `Group ${i}`;
      }

      const initContentSource: TContentSourceGroup = initContentSourceGroup(groupName);

      state.workspace.contentSources.groups.push(initContentSource);
      state.workspace.contentSources.expanded.push(initContentSource.groupId);
    },
    // NOTE: hard push new objective to categories.goal
    updateObjectiveTypes(state, action: PayloadAction<any>) {
      const dataObjectiveTypes = action.payload;
      if (dataObjectiveTypes) {
        state.workspace.categories = {
          ...state.workspace.categories,
          goal: dataObjectiveTypes,
        };
        // state.workspace.categories.goal = dataObjectiveTypes;
      }
    },
    deleteContentSourceGroup(state, action: PayloadAction<string>) {
      const groupId = action.payload;
      const index = state.workspace.contentSources.groups.findIndex(g => g.groupId === groupId);
      if (index !== -1) {
        state.workspace.contentSources.groups.splice(index, 1);
        state.workspace.contentSources.expanded = state.workspace.contentSources.expanded.filter(id => id !== groupId);
      }
    },
    expanedContentSource(state, action: PayloadAction<string[]>) {
      state.workspace.contentSources.expanded = action.payload;
    },
    setWorkspace(state, action: PayloadAction<Partial<TWorkspace>>) {
      state.workspace = {
        ...state.workspace,
        ...action.payload,
      };
      if (state.workspace.settings.lazyLoadImage === undefined) {
        state.workspace.settings.lazyLoadImage = true;
      }
    },
    setViewPagesThumbnail(state, action: PayloadAction<{ id: string; thumbnail: string }>) {
      const currentViewPage = state.workspace.viewPages.find(viewPage => action.payload.id === viewPage.id);

      if (currentViewPage) {
        currentViewPage.thumbnail = action.payload.thumbnail;
      }
    },
    setWorkspaceErrors(state, action: PayloadAction<Partial<TWorkspace['errors']>>) {
      state.workspace.errors = {
        ...action.payload,
      };
    },
    setWorkspaceWarnings(state, action: PayloadAction<Partial<TWorkspace['warnings']>>) {
      state.workspace.warnings = {
        ...action.payload,
      };
    },
    setTemplate(state, action: PayloadAction<Partial<TTemplate>>) {
      state.workspace.template = {
        ...state.workspace.template,
        ...action.payload,
      };
    },
    updateBlock(state, action: PayloadAction<any>) {
      const { workspace } = state;
      const blockSelectedId = state.sidePanel.blockSelectedId;
      const currentPageIdx = getCurrentPageIdx(state);

      (workspace.viewPages[currentPageIdx].blocks || {})[blockSelectedId] = action.payload;
    },
    updateBlockById(state, action: PayloadAction<TUpdateBlockByIdPayload>) {
      const { blockId, dataUpdate } = action.payload;
      const { workspace } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      (workspace.viewPages[currentPageIdx].blocks || {})[blockId] = dataUpdate;
    },
    updateBlockFieldsSelected(state, action: PayloadAction<TUpdateBlockFieldsSelectedPayload>) {
      const { dataUpdate } = action.payload;
      const { workspace, sidePanel } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      for (const row of dataUpdate) {
        const { fieldPath, data } = row;

        if (!fieldPath) {
          continue;
        }

        set((workspace.viewPages[currentPageIdx].blocks || {})[sidePanel.blockSelectedId], fieldPath, data);
      }
    },
    updateBlockFieldsById(state, action: PayloadAction<TUpdateBlockFieldsByIdPayload>) {
      const { blockId, dataUpdate } = action.payload;
      const { workspace } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      for (const row of dataUpdate) {
        const { fieldPath, data } = row;

        if (!fieldPath) {
          continue;
        }

        set((workspace.viewPages[currentPageIdx].blocks || {})[blockId], fieldPath, data);
      }
    },
    updateBlockSetting(state, action: PayloadAction<any>) {
      const { workspace } = state;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { ignoreUndoAction = false, ...blockSetting } = action.payload;

      const currentPageIdx = getCurrentPageIdx(state);

      (workspace.viewPages[currentPageIdx].blocks || {})[state.sidePanel.blockSelectedId].settings = blockSetting;
    },
    // action updateBlockText() used to group multiple update Redux action
    updateBlockText(state, action: PayloadAction<TUpdateBlockTextPayload>) {
      const { blockId, dataUpdate } = action.payload;
      const { workspace } = state;

      const currentPageIdx = getCurrentPageIdx(state);

      for (const row of dataUpdate) {
        const { fieldPath, data } = row;

        if (!fieldPath) {
          continue;
        }

        set((workspace.viewPages[currentPageIdx].blocks || {})[blockId], fieldPath, data);
      }
    },
    cloneBlock(state, action: PayloadAction<TCloneBlockPayload>) {
      const { workspace } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      if (currentPageIdx !== -1) {
        const { blockId, parentBlockId = '' } = action.payload;
        const currentPage = workspace.viewPages[currentPageIdx];
        const { blocks = {}, tree = {} } = currentPage;
        const blockIdx = tree[parentBlockId]?.indexOf(blockId);

        if (blockIdx !== -1) {
          const { childrenBlocks, childrenTree } = getCloneBlocks({
            blocks,
            tree,
            blockId,
            parentId: parentBlockId,
          });

          currentPage.blocks = { ...blocks, ...childrenBlocks };

          mergeWith(currentPage.tree, childrenTree, (objValue, srcValue) => {
            if (Array.isArray(objValue)) {
              const draftObjValue = cloneDeep(objValue);
              draftObjValue.splice(blockIdx + 1, 0, ...srcValue);

              return draftObjValue;
            }
          });
          // Handle per block type settings
          switch (blocks[blockId].type) {
            case 'slide':
              const emptySlideIdx = tree[parentBlockId].findIndex(id => tree[id]?.length === 0);

              (currentPage.tree || {})[parentBlockId].splice(emptySlideIdx, 1);
              break;
            case 'col':
              const remainingPercent =
                100 -
                get(blocks[parentBlockId], 'settings.blockStylesSettings.columnGap', 0) *
                  (tree[parentBlockId]?.length - 1);
              const newColWidth = remainingPercent / tree[parentBlockId]?.length;

              for (const colId of tree[parentBlockId]) {
                set(currentPage.blocks[colId], 'settings.width', newColWidth);
              }

              break;

            default:
              break;
          }
        }
      }
    },
    // removeBlock current viewPage
    removeBlock(state, action: PayloadAction<TRemoveBlockPayload>) {
      const { blockId, parentBlockId = '', blockType = '' } = action.payload;
      const { workspace } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      if (blockType === STANDARDS_BLOCKS.OPTIN_FIELDS.name) {
        state.data = {
          ...state.data,
          events: [],
          attributes: [],
        };
      }

      if (currentPageIdx !== -1) {
        const currentPage = workspace.viewPages[currentPageIdx];
        const { tree = {}, blocks = {} } = currentPage;

        switch (blocks[blockId].type) {
          case 'slide':
            const slideShowBlock = blocks[parentBlockId] || {};

            const newTotalItems = tree[parentBlockId]?.length - 1;

            slideShowBlock.settings.totalItems = newTotalItems;

            if (slideShowBlock.settings.displayItems > newTotalItems) {
              slideShowBlock.settings.displayItems = newTotalItems;
            }

            break;
          case 'col':
            const remainingPercent =
              100 -
              get(blocks[parentBlockId], 'settings.blockStylesSettings.columnGap', 0) *
                (tree[parentBlockId]?.length - 2);
            const newColWidth = remainingPercent / (tree[parentBlockId]?.length - 1);

            for (const colId of tree[parentBlockId]) {
              set((currentPage.blocks || {})[colId], 'settings.width', newColWidth);
            }

            break;

          default:
            break;
        }

        removeBlocks({
          tree,
          blocks,
          blockId,
          parentId: parentBlockId,
        });
      }
    },
    // removeBlock any viewPage
    removeBlockViews(state, action: PayloadAction<TRemoveBlockPayload>) {
      const { workspace } = state;

      const { blockId } = action.payload;

      for (const viewPage of workspace.viewPages) {
        const { tree = {}, blocks = {} } = viewPage;

        const parentId = Object.keys(tree).find(key => tree[key].includes(blockId));

        if (parentId) {
          removeBlocks({
            tree,
            blocks,
            blockId,
            parentId,
          });
        }
      }
    },
    updateViewSetting(state, action: PayloadAction<TUpdateViewSetting>) {
      // Payload
      const {
        global,
        isActive,
        container,
        closeButton,
        openButton,
        customCSS,
        customJS,
        sidebarRootActive,
        slideClosedContainer,
        fullscreenContainer,
      } = action.payload;

      const currentGlobal = state.workspace.viewPages[0].settings.global;

      const handleUpdateViewSetting = (viewPage: TViewPage) => {
        viewPage.settings = {
          ...viewPage.settings,
          ...(isActive && { isActive }),
          container: {
            ...viewPage.settings.container,
            ...container,
            styles: {
              ...viewPage.settings.container.styles,
              ...container?.styles,
            },
          },
          closeButton: {
            ...viewPage.settings.closeButton,
            ...closeButton,
          },
          openButton: {
            ...viewPage.settings.openButton,
            ...openButton,
          },
        };
      };
      // Update global settings
      for (const viewIdx in state.workspace.viewPages) {
        const viewPage = state.workspace.viewPages[viewIdx];

        if (global !== undefined) {
          viewPage.settings.global = global;
        }

        if (container?.overlayColor) {
          viewPage.settings.container.overlayColor = container?.overlayColor;
        }

        if (customCSS) {
          viewPage.settings.customCSS = {
            ...viewPage.settings.customCSS,
            ...customCSS,
          };
        }

        if (customJS) {
          viewPage.settings.customJS = {
            ...viewPage.settings.customJS,
            ...customJS,
          };
        }

        if (slideClosedContainer) {
          viewPage.settings.slideClosedContainer = {
            ...viewPage.settings.slideClosedContainer,
            ...slideClosedContainer,
            styles: {
              ...viewPage.settings.slideClosedContainer.styles,
              ...slideClosedContainer.styles,
            },
          };
        }

        if (sidebarRootActive) {
          viewPage.settings.sidebarRootActive = sidebarRootActive;
        }

        if (fullscreenContainer) {
          viewPage.settings.fullscreenContainer = {
            ...viewPage.settings.fullscreenContainer,
            ...fullscreenContainer,
            styles: {
              ...viewPage.settings.fullscreenContainer.styles,
              ...fullscreenContainer?.styles,
            },
          };
        }
      }

      // If view styling global and updated key not contain isActive then change all view styling page
      if (currentGlobal && !isActive) {
        for (const viewIdx in state.workspace.viewPages) {
          const viewPage = state.workspace.viewPages[viewIdx];

          handleUpdateViewSetting(viewPage);
        }
      } else {
        const currentPageIdx = getCurrentPageIdx(state);

        handleUpdateViewSetting(state.workspace.viewPages[currentPageIdx]);
      }
    },
    toggleActiveYesNoView(state, action: PayloadAction<{ isActive: boolean }>) {
      const yesNoViewIdx = state.workspace.viewPages.findIndex(({ id }) => id === VIEW_PAGE.YES_NO.name);

      if (yesNoViewIdx !== -1) {
        state.workspace.viewPages[yesNoViewIdx].settings.isActive = action.payload.isActive;
      }
    },
    handleImportFromView(state, action: PayloadAction<THandleImportFromView>) {
      const { viewId } = action.payload;
      const importedView = state.workspace.viewPages.find(({ id }) => viewId === id);
      const currentPageIdx = getCurrentPageIdx(state);
      const objectShakeReferral = {};
      const objectElement = { button: 'ButtonElement--', page: '' };
      if (currentPageIdx !== -1) {
        if (importedView) {
          const { blocks = {}, tree = {} } = importedView;
          const cloneBlocks = {};
          const cloneTree = {};
          (tree['root'] || []).forEach(blockId => {
            const { childrenBlocks, childrenTree } = getCloneBlocks({
              blocks,
              tree,
              blockId,
              parentId: 'root',
              objectShakeReferral,
            });
            Object.assign(cloneBlocks, childrenBlocks);

            mergeWith(cloneTree, childrenTree, (objValue, srcValue) => {
              if (Array.isArray(objValue)) {
                return objValue.concat(srcValue);
              }
            });
          });
          Object.keys(cloneBlocks).forEach(block => {
            if (cloneBlocks[block].type === 'shake-and-win') {
              cloneBlocks[block].settings = {
                ...cloneBlocks[block].settings,
                triggerSettings: {
                  ...getObjSafely(() => cloneBlocks[block].settings.triggerSettings),
                  referral: {
                    ...getObjSafely(() => cloneBlocks[block].settings.triggerSettings.referral),
                    value:
                      objectShakeReferral[
                        getObjSafely(() => cloneBlocks[block].settings.triggerSettings.referral.value)
                      ],
                    id: `${objectElement[getObjSafely(() => cloneBlocks[block].settings.triggerSettings.by)]}${
                      objectShakeReferral[
                        getObjSafely(() => cloneBlocks[block].settings.triggerSettings.referral.value)
                      ]
                    }`,
                  },
                },
              };
            }
          });
          state.workspace.viewPages[currentPageIdx].blocks = cloneBlocks;
          state.workspace.viewPages[currentPageIdx].tree = cloneTree;
        }
      }
    },
    setData(state, action: PayloadAction<TDataPayload>) {
      state.data = {
        ...state.data,
        ...action.payload,
      };
    },
    setSaveData(state, action: PayloadAction<TSaveData>) {
      state.saveData = {
        ...state.saveData,
        ...action.payload,
      };
    },
    setThumbnail(state, action: PayloadAction<string>) {
      if (action.payload !== state.workspace.thumbnail) {
        state.workspace = {
          ...state.workspace,
          thumbnail: action.payload,
        };
        state.saveData = {
          ...state.saveData,
          thumbnail: action.payload,
        };
      }
    },
    setLoadingWorkspace(state, action: PayloadAction<boolean | undefined>) {
      state.isLoadingWorkspace = action.payload === undefined ? true : action.payload;
    },
    setSavingTemplate(state, action: PayloadAction<boolean | undefined>) {
      state.isSavingTemplate = action.payload === undefined ? true : action.payload;
    },
    setMediaTemplateDetail(state, action: PayloadAction<TSetMediaTemplateDetail>) {
      const { mediaTemplateDetail } = action.payload;

      state.workspace = {
        ...state.workspace,
        ...migrateMediaTemplateDetail(mediaTemplateDetail as TWorkspace),
      };

      if (state.workspace.settings.lazyLoadImage === undefined) {
        state.workspace.settings.lazyLoadImage = true;
      }
    },
    resetMediaTemplate(state) {
      state.isShowErrorAlert = false;
      state.leftSidePanel = initialState.leftSidePanel;
      state.workspace = initialState.workspace;
      state.toolbar = initialState.toolbar;
      state.sidePanel = initialState.sidePanel;
    },
    setExportInfo(state, action: PayloadAction<TExportInfo>) {
      for (let key in action.payload) {
        state.exportInfo[key] = action.payload[key];
        if (key === 'isExporting' && !action.payload[key]) {
          state.workspace.isInitial = false;
        }
      }
    },
    setHtmlCss(state, action: PayloadAction<THTMLCssPayload>) {
      const currentViewPage = state.workspace.viewPages.find(
        viewPage => (action.payload.viewPageId || state.toolbar.viewPageSelected) === viewPage.id,
      );

      if (currentViewPage) {
        currentViewPage.html = action.payload.html;
        currentViewPage.styles = action.payload.styles || '';
      }
    },
    setEventTrigger(state, action) {
      const { workspace } = state;
      const currentPageIdx = getCurrentPageIdx(state);

      (workspace.viewPages[currentPageIdx].blocks || {})[state.sidePanel.blockSelectedId].settings.eventTrigger =
        action.payload;
    },
  },
});

export const { actions: mediaTemplateDesignActions } = slice;

export const useMediaTemplateDesignSlice = () => {
  useInjectReducer({
    key: slice.name,
    reducer: undoable(slice.reducer, {
      limit: 20,
      ignoreInitialState: true,
      syncFilter: true,
      nullifyStateProps: ['saveData', 'isDraggingBlock'],
      groupBy: groupByActionTypes([slice.actions.updateBlockText.type], 2500),
      filter: function filterActions(action, currentState, previousHistory) {
        const { payload, type } = action;

        if (!!payload?.ignoreUndoAction) {
          return false;
        }

        if (!isEmpty(payload)) {
          return [
            'updateBlockFieldsById',
            'updateBlockFieldsSelected',
            'updateCurrentPageTreeBlocks',
            'toggleActiveYesNoView',
            'updateViewSetting',
            'setGlobalSettings',
            'setBusinessObjectSettings',
            'addBlock',
            'reorderBlock',
            'updateBlockSetting',
            'updateBlockText',
            'updateBlock',
            'cloneBlock',
            'removeBlock',
          ]
            .map(action => `${slice.name}/${action}`)
            .includes(type);
        }

        return false;
      },
    }) as any,
  });
  useInjectSaga({ key: slice.name, saga: mediaTemplateDesignSaga });
  return { actions: slice.actions };
};
