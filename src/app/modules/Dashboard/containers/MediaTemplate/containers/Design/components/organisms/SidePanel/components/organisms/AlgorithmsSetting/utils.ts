import { OPERATOR_OPTIONS } from './constants';

export const isOperatorInRange = (operator: string) =>
  [
    OPERATOR_OPTIONS.GREATER_THAN_RANGE.value,
    OPERATOR_OPTIONS.LESS_THAN_RANGE.value,
    OPERATOR_OPTIONS.GREATER_THAN_EQUAL_RANGE.value,
    OPERATOR_OPTIONS.LESS_THAN_EQUAL_RANGE.value,
    OPERATOR_OPTIONS.ABSOLUTE_DISTANCE_RANGE.value,
  ].includes(operator);

export const isExistKey = (arr, val, keyArr = 'value') => arr.findIndex(item => item[keyArr] === val) > -1;
