// Libraries
import React, { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';
import isEmpty from 'lodash/isEmpty';

// Translations
import { translations } from 'locales/translations';
import { translate, translations as localesTranslations } from '@antscorp/antsomi-locales';

// Atoms
import { Button, Divider, Space, Switch, Text } from 'app/components/atoms';

// Molecules
import {
  Collapse,
  CollapsePanel,
  Select,
  Modal,
  InputNumberWithUnit,
  SelectOption,
  RadioGroup,
} from 'app/components/molecules';
import { ColorSetting } from '../../../../molecules/ColorSetting';
import { SettingWrapper } from '../../../../molecules/SettingWrapper';
import { tagRender } from 'app/components/molecules/Select';

// Organisms
import { TemplateSetting } from '../../../TemplateSetting';
import { BackgroundSetting } from '../../../BackgroundSetting';

// Styled
import { BasicWrapper, CollapseItemWrapper } from './styled';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Selectors
import {
  selectCurrentViewPage,
  selectGlobalSettings,
  selectViewPage,
  selectWorkspace,
  selectMediaTemplateTypes,
  selectSidePanel,
  // selectBusinessObjectSettings,
  // selectBusinessObjects,
  selectViewPages,
  selectListFallbackBO,
  selectTrackingData,
  selectTrackingModule,
  selectContentSources,
  selectIsShowErrorAlert,
  selectCSFallbackByGroupId,
  selectJourneySettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { handleError } from 'app/utils/handleError';

// Types
import {
  TGlobalSettings,
  TRanking,
  TTrackingModuleData,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import {
  TModuleTrackingMode,
  TUpdateViewSetting,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';
import { UpdateGroupAction } from '../../../ContentSources/types';

// Utils
import { getBackgroundSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Constants
import {
  LAYOUT_TEMPLATE,
  SIDE_PANEL_COLLAPSE,
  SIDE_PANEL_SECTION,
  VIEW_PAGE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { BUTTON_STYLE, FALLBACK_SELECTION, TRACKING_MODE } from './constants';
import { UNIT } from 'constants/variables';
import {
  getAllBlocksFromAllViews,
  getBlocksUseBO,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import {
  ARTICLE_RANKING_DEFAULT,
  FILTERS_DEFAULT,
  GET_TOP_RANKING_DEFAULT,
  ITEM_TYPE_NAME,
  PRODUCT_ITEM_TYPE_ID,
  PRODUCT_RANKING_DEFAULT,
  TRACKING_MODULE_DEFAULT,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

import ContentSources from '../../../ContentSources';

// Queries
import { useGetListBO } from 'app/queries/BusinessObject';
import { AutoCloseTemplate } from '../../../AutoCloseTemplate';

const { VIEW_STYLING, TEMPLATE_DETAILS, DISPLAY_SETTINGS } = SIDE_PANEL_COLLAPSE;

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Settings/Basic/index.tsx';

interface BasicProps {}

const { INLINE, GAMIFIED, FULL_SCREEN } = LAYOUT_TEMPLATE;

const OPEN_SELECT_INIT = {
  source: false,
  medium: false,
  campaign: false,
  term: false,
  content: false,
};

const SEARCH_TEXT_INIT = {
  source: '',
  medium: '',
  campaign: '',
  term: '',
  content: '',
};

const TRACKING_MODULE_LIST: string[] = ['source', 'medium', 'campaign', 'term', 'content'];

// Actions
const {
  updateViewSetting,
  setGlobalSettings,
  toggleActiveYesNoView,
  setTemplate,
  setSidePanel,
  removeBlockViews,
  setTrackingModuleSetting,
  addContentSource,
  expanedContentSource,
  setContentSourceGroup,
  setIsExcludeDuplicate,
  deleteContentSourceGroup,
} = mediaTemplateDesignActions;

export const Basic: React.FC<BasicProps> = memo(props => {
  const dispatch = useDispatch();
  const [isOpenSelect, setIsOpenSelect] = useState(OPEN_SELECT_INIT);
  const [searchText, setSearchText] = useState(SEARCH_TEXT_INIT);

  // I18n
  const { t } = useTranslation();

  // Selectors
  const sidePanel = useSelector(selectSidePanel);
  const workspace = useSelector(selectWorkspace);
  const currentViewPage = useSelector(selectCurrentViewPage);
  const viewPages = useSelector(selectViewPages);
  const yesNoViewPage = useSelector(selectViewPage(VIEW_PAGE.YES_NO.name));
  const globalSettings = useSelector(selectGlobalSettings);
  const storeMediaTemplateTypes = useSelector(selectMediaTemplateTypes);
  // const businessObjectSettings = useSelector(selectBusinessObjectSettings);
  const listFallbackBO = useSelector(selectListFallbackBO);
  const trackingModule = useSelector(selectTrackingModule) || TRACKING_MODULE_DEFAULT;
  const trackingData = useSelector(selectTrackingData);
  const contentSources = useSelector(selectContentSources);
  const csFallbackByGroupId = useSelector(selectCSFallbackByGroupId);
  const journeySettings = useSelector(selectJourneySettings);

  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // const { source, medium, campaign, term, content } = utmTracking;
  // const { itemTypeId, fallback, itemTypeName } = businessObjectSettings;

  // Queries
  const { data: listBO, isLoading: isLoadingGetListBO } = useGetListBO();
  // is Check Archive or Delete
  // Init default active panel
  useEffect(() => {
    Object.entries(csFallbackByGroupId).forEach(([groupId, fallback]) => {
      if (!fallback) {
        dispatch(
          setContentSourceGroup({
            groupId,
            values: { fallback: FALLBACK_SELECTION.MOST_SEEN.value },
          }),
        );
      }
    });

    if (![TEMPLATE_DETAILS, DISPLAY_SETTINGS, VIEW_STYLING, workspace.template.type].includes(sidePanel.activePanel)) {
      dispatch(
        setSidePanel({
          activePanel: VIEW_STYLING,
        }),
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const activeSectionEl = document.getElementById(sidePanel.activeSectionId);

    if (sidePanel.activeSectionId && activeSectionEl) {
      setTimeout(() => {
        activeSectionEl.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 300);

      dispatch(setSidePanel({ activeSectionId: '' }));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sidePanel.activeSectionId]);

  if (currentViewPage) {
    const viewPageSettings = currentViewPage.settings;
    const { global, closeButton, container } = viewPageSettings;

    const onChangeViewSettings = (payload: TUpdateViewSetting) => {
      try {
        dispatch(updateViewSetting(payload));
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeViewStyling',
          args: {},
        });
      }
    };

    const onChangeModuleTrackingMode = (value: TModuleTrackingMode) => {
      try {
        dispatch(
          setTrackingModuleSetting({
            mode: value,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeModuleTrackingMode',
          args: {},
        });
      }
    };

    const onChangeTrackingItem = (value: boolean) => {
      try {
        dispatch(
          setTrackingModuleSetting({
            isTrackingItem: value,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeTrackingItem',
          args: {},
        });
      }
    };

    const onChangeGlobalSettings = (payload: Partial<TGlobalSettings>) => {
      try {
        dispatch(setGlobalSettings(payload));
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeGlobalSettings',
          args: { payload },
        });
      }
    };

    const onChangeSwitchGlobalViewStyling = (checked: boolean) => {
      try {
        Modal.confirm({
          icon: null,
          centered: true,
          title: t(translations.switchGlobalStylingModal[checked ? 'enableTitle' : 'disableTitle']),
          content: t(translations.switchGlobalStylingModal[checked ? 'enableDescription' : 'disableDescription']),
          onOk() {
            onChangeViewSettings({ global: checked });
          },
        });
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeSwitchGlobalViewStyling',
          args: {},
        });
      }
    };

    const onChangeItemTypeId = ({
      groupId,
      id,
      isDelete = false,
      callback,
    }: {
      groupId: string;
      id: number | null;
      isDelete?: boolean;
      callback?: () => void;
    }) => {
      try {
        const selected = listBO?.find((bo: any) => bo.value === id);
        const itemTypeDisplay = selected?.label;
        const itemTypeName = selected?.name;
        const allBlocksFromAllViews = getAllBlocksFromAllViews(viewPages);
        const blocksUseBO = getBlocksUseBO({
          listBlocksChecking: allBlocksFromAllViews,
          groupBoId: groupId || '',
        });

        let valueRanking: TRanking = GET_TOP_RANKING_DEFAULT;

        if (id === PRODUCT_ITEM_TYPE_ID) {
          valueRanking = PRODUCT_RANKING_DEFAULT;
        } else if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
          valueRanking = ARTICLE_RANKING_DEFAULT;
        }

        const newRanking: TRanking = valueRanking;

        if (blocksUseBO.length > 0) {
          const keyTranslation = isDelete ? 'confirmDeleteContentSourceGroup' : 'confirmChangeContentSource';

          Modal.confirm({
            icon: null,
            centered: true,
            title: t(translations[keyTranslation]?.title),
            content: (
              <div>
                <Text>{t(translations[keyTranslation]?.blocksDescription)}</Text>
                <div className="ants-my-1">
                  {blocksUseBO.map(({ settings }) => (
                    <Text>- {settings.name}</Text>
                  ))}
                </div>
                <Text>{t(translations[keyTranslation]?.deleteDescription)}</Text>
              </div>
            ),
            onOk() {
              if (groupId) {
                dispatch(
                  setContentSourceGroup({
                    groupId,
                    values: {
                      itemTypeId: id,
                      itemTypeDisplay,
                      itemTypeName,
                      ranking: newRanking,
                      filters: FILTERS_DEFAULT,
                    },
                  }),
                );
              }

              blocksUseBO.forEach(element => {
                dispatch(
                  removeBlockViews({
                    blockId: element.id as string,
                    blockType: element.type,
                  }),
                );
              });

              if (callback) callback();
            },
          });

          return;
        }

        if (groupId) {
          dispatch(
            setContentSourceGroup({
              groupId,
              values: {
                itemTypeId: id,
                itemTypeDisplay,
                itemTypeName,
                ranking: newRanking,
                filters: FILTERS_DEFAULT,
              },
            }),
          );

          if (callback) callback();
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeItemTypeId',
          args: {},
        });
      }
    };

    const handleChangeContentSourceGroup = (groupId: string, action: UpdateGroupAction) => {
      if (action.type === 'BO_TYPE') {
        const { itemTypeId } = action.payload;
        if (itemTypeId) onChangeItemTypeId({ groupId, id: itemTypeId });
      }

      if (action.type === 'ALGORITHMS') {
        const { ranking } = action.payload;

        dispatch(setContentSourceGroup({ groupId, values: { ranking } }));
      }

      if (action.type === 'FILTER') {
        const { conditions } = action.payload;
        dispatch(setContentSourceGroup({ groupId, values: { filters: conditions } }));
      }

      if (action.type === 'FALLBACK') {
        const { fallback } = action.payload;
        dispatch(setContentSourceGroup({ groupId, values: { fallback } }));
      }

      if (action.type === 'NAME') {
        const { name } = action.payload;
        dispatch(setContentSourceGroup({ groupId, values: { groupName: name } }));
      }

      if (action.type === 'LEVEL') {
        const { level } = action.payload;
        dispatch(setContentSourceGroup({ groupId, values: { level } }));
      }
    };

    const handleDeleteContentSourceGroup = (groupId: string) => {
      const group = contentSources.groups.find(g => g.groupId === groupId);

      if (group)
        // Re-use function in case change content source type
        // for case remove content source
        onChangeItemTypeId({
          groupId,
          id: group.itemTypeId,
          isDelete: true,
          callback: () => dispatch(deleteContentSourceGroup(groupId)),
        });
    };

    const onChangeOpenSelect = (utmType: string, value: boolean) => {
      setIsOpenSelect(prev => {
        return { ...prev, [utmType]: value };
      });
    };

    const onChangeSearchText = (utmType: string, value: string) => {
      setSearchText(prev => {
        return {
          ...prev,
          [utmType]: value,
        };
      });
    };

    const onChangeTrackingModule = (
      type: string,
      values: string[],
      options: any,
      trackingModuleSelect: TTrackingModuleData,
      trackingModuleContent,
    ) => {
      const newTrackingModule = options.map((option, index) => {
        if (
          isEmpty(option) ||
          trackingModuleContent.findIndex(item => item.type === 'tag' && item.value === option.value) > -1
        ) {
          return {
            type: 'tag',
            value: values[index],
          };
        } else {
          return {
            type: 'list',
            value: option.value,
          };
        }
      });

      dispatch(
        setTrackingModuleSetting({
          data: produce(trackingModuleSelect, draft => {
            switch (type) {
              case 'source':
                draft.source = [...newTrackingModule];
                break;
              case 'medium':
                draft.medium = [...newTrackingModule];
                break;
              case 'campaign':
                draft.campaign = [...newTrackingModule];
                break;
              case 'term':
                draft.term = [...newTrackingModule];
                break;
              case 'content':
                draft.content = [...newTrackingModule];
                break;
              default:
                break;
            }
          }),
        }),
      );
    };

    // const tagRender = (props: any, optionsUtm: any[]) => {
    //   const { label, value, closable, onClose, maxTagTextLength } = props;

    //   return (
    //     <>
    //       <Tag
    //         color={
    //           optionsUtm.findIndex(option => option.type === 'tag' && option.value === value) !== -1
    //             ? '#F5F5F5'
    //             : '#CAE5FE'
    //         }
    //         closable={closable}
    //         onClose={onClose}
    //         style={{ marginRight: 3 }}
    //         isUnsetMaxWidth={!!maxTagTextLength}
    //       >
    //         {label}
    //       </Tag>
    //     </>
    //   );
    // };
    return (
      <BasicWrapper>
        {/* <div
          style={{
            padding: '15px 15px 15px 10px',
          }}
          className="ants-flex ants-justify-between ants-items-center"
        >
          <div className="ants-flex ants-items-center">
            <Text style={{ lineHeight: 1, marginRight: 5 }}>{t(translations.thumbnailCapture.title)}</Text>
            <Tooltip title={t(translations.thumbnailCapture.tooltip)}>
              <Icon type="icon-ants-info" size={12} style={{ color: '#666', cursor: 'pointer' }} />
            </Tooltip>
          </div>
          <Switch
            checked={globalSettings.thumbnailCapture}
            onChange={checked =>
              onChangeGlobalSettings({
                thumbnailCapture: checked,
              })
            }
          />
        </div> */}

        <Divider style={{ margin: 0, borderColor: '#d9d9d9' }}></Divider>

        <Collapse
          accordion
          defaultActiveKey={SIDE_PANEL_COLLAPSE.VIEW_STYLING}
          // activeKey={SIDE_PANEL_COLLAPSE.BUSINESS_OBJECT}
          activeKey={sidePanel.activePanel}
          onChange={activePanel => {
            dispatch(
              setSidePanel({
                activePanel: activePanel as string,
              }),
            );
          }}
        >
          <CollapsePanel header={t(translations.templateDetails.title)} key={SIDE_PANEL_COLLAPSE.TEMPLATE_DETAILS}>
            <Select
              value={workspace.template.type === GAMIFIED.name ? FULL_SCREEN.name : workspace.template.type} // If gamified, default to full screen
              label={t(translations.displayType.title)}
              options={
                storeMediaTemplateTypes
                  ?.filter(({ id }) => id !== GAMIFIED.id)
                  .map(({ id, name, label }) => ({ id, value: name, label })) || []
              }
              onChange={(value, option: any) =>
                dispatch(
                  setTemplate({
                    id: option.id,
                    type: value,
                    name: option.label,
                  }),
                )
              }
            />
          </CollapsePanel>
          <CollapsePanel
            header={t(translations.viewStylingPanel.title, {
              viewName: !currentViewPage.settings.global ? currentViewPage.title : '',
            })}
            key={SIDE_PANEL_COLLAPSE.VIEW_STYLING}
          >
            <div className="ants-flex ants-flex-col ants-space-y-5">
              <SettingWrapper label={`${t(translations.globalViewStyling.title)}?`}>
                <Switch checked={global} onChange={checked => onChangeSwitchGlobalViewStyling(checked)} />
              </SettingWrapper>
              <SettingWrapper label={`${t(translations.campaignWidth.title)}:`}>
                <InputNumberWithUnit
                  unit={container.containerWidthSuffix}
                  min={0}
                  max={container.containerWidthSuffix === UNIT.PX.value ? 2000 : 100}
                  value={container.containerWidth}
                  onChange={value =>
                    onChangeViewSettings({
                      container: {
                        containerWidth: value,
                      },
                    })
                  }
                  onChangeUnit={unit =>
                    onChangeViewSettings({
                      container: {
                        containerWidth: unit === UNIT.PX.value ? 700 : 100,
                        containerWidthSuffix: unit,
                      },
                    })
                  }
                />
              </SettingWrapper>
              <BackgroundSetting
                label={t(translations.style.title)}
                settings={getBackgroundSettings(container as any)}
                styles={{ background: container.styles.background }}
                imageLayerFirst={container.imageLayerFirst}
                onChange={(settings, styles) =>
                  onChangeViewSettings({
                    container: { ...settings, styles: styles },
                  })
                }
              />
              <SettingWrapper label={`${t(translations.setImagesLayerFirst.title)}?`}>
                <Switch
                  checked={container.imageLayerFirst}
                  onChange={checked => onChangeViewSettings({ container: { imageLayerFirst: checked } })}
                />
              </SettingWrapper>
              <SettingWrapper label={`${t(translations.setImageLazyLoad.title)}?`}>
                <Switch
                  checked={globalSettings.lazyLoadImage}
                  onChange={checked =>
                    onChangeGlobalSettings({
                      lazyLoadImage: checked,
                    })
                  }
                />
              </SettingWrapper>
              {/* <SettingWrapper label={`${t(translations.hideContainerOverflow.title)}?`}>
                <Switch
                  checked={container.overflowHidden}
                  onChange={checked => onChangeViewSettings({ container: { overflowHidden: checked } })}
                />
              </SettingWrapper> */}
            </div>
            {workspace.template.type !== INLINE.name && (
              <div id={SIDE_PANEL_SECTION.CLOSE_BUTTON_STYLING}>
                <Divider type="horizontal" dot />
                <div className="ants-flex ants-flex-col ants-space-y-5">
                  <Text size="medium" className="!ants-text-cus-dark">
                    {t(translations.closeTemplate.title)}
                  </Text>
                  <SettingWrapper label={`${t(translations.displayCloseButton.title)}?`}>
                    <Switch
                      checked={closeButton.closeButtonActive}
                      onChange={checked =>
                        onChangeViewSettings({
                          closeButton: {
                            closeButtonActive: checked,
                          },
                        })
                      }
                    />
                  </SettingWrapper>
                  {closeButton.closeButtonActive && (
                    <>
                      <div className="ants-grid ants-grid-cols-2">
                        <ColorSetting
                          labelClassName="ants-w-full !ants-text-cus-base"
                          label={t(translations.xColor.title)}
                          color={closeButton.iconColor}
                          positionInput="right"
                          onChange={color =>
                            onChangeViewSettings({
                              closeButton: {
                                iconColor: color,
                              },
                            })
                          }
                        />
                        <ColorSetting
                          labelClassName="!ants-text-cus-base"
                          label={t(translations.backgroundColor.title)}
                          color={closeButton.backgroundColor}
                          positionInput="right"
                          onChange={color =>
                            onChangeViewSettings({
                              closeButton: {
                                backgroundColor: color,
                              },
                            })
                          }
                        />
                      </div>
                      <SettingWrapper label={`${t(translations.buttonStyle.title)}:`}>
                        <Select
                          value={closeButton.buttonStyle}
                          options={Object.values(BUTTON_STYLE)}
                          className="ants-w-[100px]"
                          onChange={value =>
                            onChangeViewSettings({
                              closeButton: {
                                buttonStyle: value,
                              },
                            })
                          }
                        />
                      </SettingWrapper>
                    </>
                  )}

                  <AutoCloseTemplate
                    setting={globalSettings.autoCloseTemplate}
                    onChange={setting => dispatch(setGlobalSettings({ autoCloseTemplate: setting }))}
                  />
                </div>
              </div>
            )}
          </CollapsePanel>
          <CollapsePanel header={t(translations.displaySettings.title)} key={SIDE_PANEL_COLLAPSE.DISPLAY_SETTINGS}>
            <div className="ants-flex ants-flex-col ants-space-y-5">
              <SettingWrapper label={`${t(translations.displayYesNoView.title)}?`}>
                <Switch
                  checked={yesNoViewPage?.settings.isActive}
                  onChange={checked => {
                    dispatch(
                      toggleActiveYesNoView({
                        isActive: checked,
                      }),
                    );
                  }}
                />
              </SettingWrapper>
              {/* <CookieDurationSelect
                label={<Trans i18nKey={translations.campaignHasBeenClosed.title} />}
                duration={globalSettings.interactionCookieDuration}
                type={globalSettings.interactionCookieType}
                onChange={({ duration, type }) =>
                  onChangeGlobalSettings({
                    interactionCookieDuration: duration,
                    interactionCookieType: type,
                  })
                }
              />
              <CookieDurationSelect
                label={<Trans i18nKey={translations.campaignHasBeenConverted.title} />}
                duration={globalSettings.successCookieDuration}
                type={globalSettings.successCookieType}
                onChange={({ duration, type }) =>
                  onChangeGlobalSettings({
                    successCookieDuration: duration,
                    successCookieType: type,
                  })
                }
              />
              <CookieDurationSelect
                label={<Trans i18nKey={translations.howLowBrowserWillRemember.title} />}
                duration={globalSettings.seenCookieDuration}
                type={globalSettings.seenCookieType}
                onChange={({ duration, type }) =>
                  onChangeGlobalSettings({
                    seenCookieDuration: duration,
                    seenCookieType: type,
                  })
                }
              />
              <SettingWrapper label={`${t(translations.crossSubdomainCookie.title)}?`}>
                <Switch
                  checked={!!globalSettings.crossSubdomainCookies}
                  onChange={checked =>
                    onChangeGlobalSettings({
                      crossSubdomainCookies: +checked,
                    })
                  }
                />
              </SettingWrapper> */}
            </div>
          </CollapsePanel>

          {![INLINE.name].includes(workspace.template.type) ? (
            <CollapsePanel
              header={t(translations.templateSettings.title, {
                template: Object.values(LAYOUT_TEMPLATE).find(({ name }) => workspace.template.type === name)?.label,
              })}
              key={workspace.template.type}
            >
              <TemplateSetting
                type={workspace.template.type}
                globalSettings={globalSettings}
                slideClosedContainerSettings={currentViewPage.settings.slideClosedContainer}
                onChange={({ globalSettings, slideClosedContainerSettings }) => {
                  onChangeGlobalSettings({
                    ...globalSettings,
                  });

                  onChangeViewSettings({
                    slideClosedContainer: {
                      ...slideClosedContainerSettings,
                    },
                  });
                }}
              />
            </CollapsePanel>
          ) : null}
          <CollapsePanel header={t(translations.contentSources.title)} key={SIDE_PANEL_COLLAPSE.CONTENT_SOURCES}>
            <ContentSources
              groups={contentSources.groups}
              listBO={listBO}
              expanded={contentSources.expanded}
              journeySettings={journeySettings}
              onChangeExpand={expaned => dispatch(expanedContentSource(expaned))}
              onChangeGroup={(groupId, action) => handleChangeContentSourceGroup(groupId, action)}
              onDeleteGroup={groupId => handleDeleteContentSourceGroup(groupId)}
              isShowErrorAlert={isShowErrorAlert}
              isLoadingListBO={isLoadingGetListBO}
              listFallbackBO={listFallbackBO ? listFallbackBO : []}
              isExcludeDuplicate={contentSources?.isExcludeDuplicate}
              onChangeExcludeDuplicate={isExcludeDuplicate => {
                dispatch(setIsExcludeDuplicate(isExcludeDuplicate));
              }}
              onAddGroup={() => dispatch(addContentSource())}
            />
          </CollapsePanel>

          <CollapsePanel
            header={translate(localesTranslations.MEDIA_DATA_TRACKING)}
            key={SIDE_PANEL_COLLAPSE.TRACKING_MODULE}
          >
            <Space direction="vertical" size={20}>
              <SettingWrapper
                labelColor={'var(--text-dark-color)'}
                label={translate(localesTranslations.MEDIA_TRACKING_LEVEL_ITEM)}
              >
                <Switch
                  checked={!!trackingModule?.isTrackingItem}
                  onChange={e => {
                    onChangeTrackingItem(e);
                  }}
                />
              </SettingWrapper>
              <SettingWrapper label={t(translations.mode.title)}>
                <RadioGroup
                  options={Object.values(TRACKING_MODE)}
                  onChange={e => {
                    onChangeModuleTrackingMode(e.target.value);
                  }}
                  value={trackingModule.mode}
                />
              </SettingWrapper>
            </Space>
            <Divider style={{ borderColor: '#e0e0e0' }} />
            {TRACKING_MODULE_LIST.map(trackingModuleItem => {
              return (
                <CollapseItemWrapper key={trackingModuleItem}>
                  <Select
                    mode="tags"
                    maxTagCount={6}
                    maxTagTextLength={18}
                    className="select-auto-height left-12"
                    filterOption={true}
                    open={isOpenSelect[trackingModuleItem]}
                    value={trackingModule.data[trackingModuleItem]}
                    searchValue={searchText[trackingModuleItem]}
                    label={t(translations.trackingModule.inner[trackingModuleItem].title, {
                      prefix: trackingModule.mode.toUpperCase(),
                    })}
                    tagRender={props =>
                      tagRender({
                        ...props,
                        style: {
                          background:
                            trackingModule.data[trackingModuleItem].findIndex(
                              option => option.type === 'tag' && option.value === props.value,
                            ) !== -1
                              ? '#F5F5F5'
                              : '#CAE5FE',
                        },
                      })
                    }
                    placeholder={t(translations.trackingModule.inner[trackingModuleItem].placeholder)}
                    onBlur={() => onChangeOpenSelect(trackingModuleItem, false)}
                    onSelect={() => onChangeOpenSelect(trackingModuleItem, false)}
                    onSearch={(searchText: string) => {
                      if (searchText.trim().startsWith('#') && searchText.trim().length === 1) {
                        onChangeOpenSelect(trackingModuleItem, true);
                        return;
                      }

                      onChangeSearchText(trackingModuleItem, searchText);
                      onChangeOpenSelect(trackingModuleItem, false);
                    }}
                    onKeyDown={(event: any) => {
                      // const spaces = event.target.value;
                      if (event.key === 'Enter') {
                        onChangeSearchText(trackingModuleItem, '');
                        // dispatch(
                        //   setUtmTrackingSetting(
                        //     produce(utmTracking, draft => {
                        //       draft[ut  mTrackingItem] = [
                        //         ...draft[trackingModuleItem],
                        //         {
                        //           type: 'tag',
                        //           value: spaces,
                        //         },
                        //       ];
                        //     }),
                        //   ),
                        // );
                      }
                    }}
                    onChange={(valueChanges: string[], options) => {
                      onChangeTrackingModule(
                        trackingModuleItem,
                        valueChanges,
                        options,
                        trackingModule.data,
                        trackingModule.data[trackingModuleItem],
                      );
                      onChangeSearchText(trackingModuleItem, '');
                    }}
                  >
                    {trackingData &&
                      trackingData.map((srcItem, index) => (
                        <SelectOption key={`${trackingModuleItem}${index}`} value={srcItem.value}>
                          {srcItem.value}
                        </SelectOption>
                      ))}
                  </Select>
                </CollapseItemWrapper>
              );
            })}
          </CollapsePanel>
        </Collapse>
      </BasicWrapper>
    );
  }

  return null;
});
