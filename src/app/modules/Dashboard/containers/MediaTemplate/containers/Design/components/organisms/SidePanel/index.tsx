// Libraries
import React, { memo, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { theme } from 'twin.macro';

// Selectors
import {
  selectBlockSelected,
  selectDesignTemplateMode,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Atoms
import { Button, Icon, ScrollBox, Text } from 'app/components/atoms';

// Organisms
import Blocks from './components/organisms/Blocks';
import {
  SettingsEditing,
  ColumnsEditing,
  ButtonEditing,
  CountdownEditing,
  CouponWheelEditing,
  DividerEditing,
  HtmlEditing,
  IconsEditing,
  ImageEditing,
  OptinFieldsEditing,
  SpacerEditing,
  TextEditing,
  VideoEditing,
  YesNoEditing,
  SlideShowEditing,
  RatingEditing,
  GroupEditing,
  TableEditing,
  ShakeAndWinEditing,
  SurpriseTreasureHuntEditing,
  OTPVerificationEditing,
  HtmlCodeModeEditing,
} from './components/organisms/BlockEditing';

// Icons
import { ToggleSidePanel } from 'app/components/icons';

// Actions
import { mediaTemplateDesignActions } from '../../../slice';

// Utils
import { handleError } from 'app/utils/handleError';

// Hooks queries
import { useGetSavedBlocks } from 'app/queries/SavedBlock';

// Styled
import { SidePanelHeader, SidePanelWrapper, ToggleSidePanelButton } from './styled';

// Constants
import {
  DESIGN_TEMPLATE_MODE,
  SIDE_PANEL_TYPE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Workspace/components/organisms/SidePanel/index.tsx';

const { BLOCKS, SETTINGS } = SIDE_PANEL_TYPE;

interface SidePanelProps extends React.HtmlHTMLAttributes<HTMLDivElement> {}

type TSidePanelType = {
  name?: string;
  label?: string;
  icon?: string;
  image?: string;
};

export const SidePanel: React.FC<SidePanelProps> = memo(props => {
  const dispatch = useDispatch();

  // Props
  const { ...restOf } = props;

  // Actions
  const { setSidePanel } = mediaTemplateDesignActions;

  // Selectors
  const sidePanel = useSelector(selectSidePanel);
  const blockSelected = useSelector(selectBlockSelected);
  const designTemplateMode = useSelector(selectDesignTemplateMode);

  // Use Hooks queries
  const { data: savedBlocks, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetSavedBlocks();

  const isCodeModeDesign = designTemplateMode === DESIGN_TEMPLATE_MODE.CODE;

  const sidePanelType: TSidePanelType = useMemo(() => {
    return Object.values(SIDE_PANEL_TYPE).find(type => type.name === sidePanel.type) || {};
  }, [sidePanel.type]);

  const isShowPanelHeader = useMemo(() => {
    return ![BLOCKS.name].includes(sidePanel.type);
  }, [sidePanel.type]);

  const renderSidePanelContent = () => {
    try {
      // code mode design
      if (isCodeModeDesign) {
        return <HtmlCodeModeEditing />;
      }

      // Render Blocks
      if (sidePanel.type === SIDE_PANEL_TYPE.BLOCKS.name) {
        return <Blocks savedBlocks={savedBlocks} />;
      }

      if (sidePanel.type === SIDE_PANEL_TYPE.SETTINGS.name) {
        return <SettingsEditing />;
      }

      if (blockSelected?.settings) {
        switch (sidePanel.type) {
          case SIDE_PANEL_TYPE.BUTTON.name:
            return <ButtonEditing />;

          case SIDE_PANEL_TYPE.SETTINGS.name:
            return <SettingsEditing />;

          case SIDE_PANEL_TYPE.COLUMN.name:
            return <ColumnsEditing />;

          case SIDE_PANEL_TYPE.TEXT.name:
            return <TextEditing />;

          case SIDE_PANEL_TYPE.COUNT_DOWN.name:
            return <CountdownEditing />;

          case SIDE_PANEL_TYPE.COUPON_WHEEL.name:
            return <CouponWheelEditing />;

          case SIDE_PANEL_TYPE.DIVIDER.name:
            return <DividerEditing />;

          case SIDE_PANEL_TYPE.HTML.name:
            return <HtmlEditing />;

          case SIDE_PANEL_TYPE.ICON.name:
            return <IconsEditing />;

          case SIDE_PANEL_TYPE.IMAGE.name:
            return <ImageEditing />;

          case SIDE_PANEL_TYPE.OPTIN_FIELDS.name:
            return <OptinFieldsEditing />;

          case SIDE_PANEL_TYPE.SPACER.name:
            return <SpacerEditing />;

          case SIDE_PANEL_TYPE.VIDEO.name:
            return <VideoEditing />;

          case SIDE_PANEL_TYPE.YES_NO.name:
            return <YesNoEditing />;

          case SIDE_PANEL_TYPE.SLIDE_SHOW.name:
            return <SlideShowEditing />;

          case SIDE_PANEL_TYPE.RATING.name:
            return <RatingEditing />;

          case SIDE_PANEL_TYPE.GROUP.name:
            return <GroupEditing />;

          case SIDE_PANEL_TYPE.TABLE.name:
            return <TableEditing />;
          case SIDE_PANEL_TYPE.SHAKE_AND_WIN.name:
            return <ShakeAndWinEditing />;
          case SIDE_PANEL_TYPE.SURPRISE_TREASURE_HUNT.name:
            return <SurpriseTreasureHuntEditing />;
          case SIDE_PANEL_TYPE.OTP_VERIFICATION.name:
            return <OTPVerificationEditing />;

          default:
            return null;
        }
      }

      // If BlockSelected not detect redirect to Blocks editing
      dispatch(
        setSidePanel({
          blockSelectedId: '',
          type: SIDE_PANEL_TYPE.BLOCKS.name,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };
  const renderSidePanelHeader = () => {
    try {
      return (
        isShowPanelHeader && (
          <SidePanelHeader>
            <div className="ants-flex ants-items-center ants-space-x-2.5">
              {isCodeModeDesign ? (
                <>
                  <Icon type="icon-ants-html" />
                  <Text size={16}>HTML</Text>
                </>
              ) : (
                <>
                  {![SETTINGS.name].includes(sidePanel.type) ? (
                    sidePanelType.icon ? (
                      <Icon type={sidePanelType.icon} />
                    ) : (
                      <div
                        style={{
                          backgroundImage: `url(${sidePanelType?.image})`,
                          height: '20px',
                          width: '20px',
                          backgroundSize: 'contain',
                        }}
                      />
                    )
                  ) : null}
                  <Text size={16}>{sidePanelType.label}</Text>
                </>
              )}
            </div>
            {!isCodeModeDesign && (
              <Button
                type="text"
                icon={<Icon type="icon-ants-home" />}
                onClick={() => {
                  dispatch(
                    setSidePanel({
                      type: SIDE_PANEL_TYPE.BLOCKS.name,
                      blockSelectedId: '',
                    }),
                  );
                }}
              />
            )}
          </SidePanelHeader>
        )
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderSidePanelHeader',
        args: {},
      });
    }
  };

  return (
    <SidePanelWrapper
      {...restOf}
      style={{
        ...restOf.style,
        width: sidePanel.isCollapsed ? 0 : 343,
      }}
    >
      {/* Toggle side panel button */}
      <ToggleSidePanelButton
        onClick={() =>
          dispatch(
            setSidePanel({
              isCollapsed: !sidePanel.isCollapsed,
            }),
          )
        }
      >
        <ToggleSidePanel />
        <Icon
          type="icon-ants-angle-right"
          className="!ants-absolute ants-left-[14px] ants-transition-transform ants-duration-500"
          style={{
            transform: sidePanel.isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
          }}
          size={10}
        />
      </ToggleSidePanelButton>

      {/* Side panel header */}
      {renderSidePanelHeader()}

      {/* Side panel content */}
      <ScrollBox
        className={`ants-absolute ants-flex ants-flex-col`}
        style={{ height: isShowPanelHeader ? `calc(100% - ${theme('space.12')})` : '' }}
        loadMore={() => {
          if (hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        }}
      >
        {renderSidePanelContent()}
      </ScrollBox>
    </SidePanelWrapper>
  );
});
