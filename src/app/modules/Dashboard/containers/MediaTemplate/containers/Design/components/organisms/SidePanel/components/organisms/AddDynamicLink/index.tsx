// Libraries
import React, { Fragment, useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { flatMapDeep, get, isEmpty, omit, set } from 'lodash';
import classnames from 'classnames';

// Components
import { TreeNode } from 'rc-tree-select';

import { Checkbox, Input, Spin, Text } from 'app/components/atoms';
import { Form, Modal, RadioGroup, Select, TreeSelect } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Selectors
import {
  // selectBusinessObjectSettings,
  selectContentSources,
  selectCSDataOfGroup,
  selectJourneySettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Types
import { ListAttribute } from 'app/models/BusinesObject';

// Styled
import { AddDynamicLinkWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';
import { buildOptionAttrArchive } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Constants
import {
  DYNAMIC_LINK_SETTING_KEY,
  DYNAMIC_LINK_TYPE,
  DYNAMIC_CONTENT_TYPE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { ATTRIBUTE_TYPE } from 'constants/variables';

// Queries
import {
  // useGetDataTableBO,
  useGetListAllEvents,
  useGetListBO,
  useGetListEventAttr,
  useGetListPromotionCodeAttr,
  useGetListSourceByEvent,
} from 'app/queries/BusinessObject';
import { useGetDynamicContentAttr } from 'app/queries/DynamicContentAttribute';
import { useGetListPromotionPool } from 'app/queries/PromotionPool';
import { isCheckStatusAttr } from '../../molecules/DynamicSetting/constants';

// Queries
import { useGetListAttributeBO } from 'app/queries/BusinessObject/useGetListAttributeBO';
import { useGetEventTrackingAttributes } from 'app/queries/ThirdParty';
import { useDeepCompareEffect, useDeepCompareMemo } from 'app/hooks';

// Utils
import { getAvailableAttrs } from './utils';
import { regexCSType, serilizeBOAttr, serializeCSSelectedString } from '../AddDynamicContent/utils';
import { queryClient } from 'index';
import { QUERY_KEYS } from 'constants/queries';

interface LabeledValue {
  [key: string]: any;
  label: string;
  value: string;
  disabled?: boolean;
  status?: any;
}
interface LabeledTreeValue {
  [key: string]: any;
  value: string;
  label: string;
  children: LabeledTreeValue[];
  disabled?: boolean;
  disableCheckbox?: boolean;
  selectable?: boolean;
  checkable?: boolean;
  status?: number;
}

interface AddDynamicLinkProps {
  defaultDynamicIndex?: number;
  defaultData?: Record<string, any>;
  visible: boolean;

  onCancel?: () => void;
  onOk?: (value: Record<string, any>) => void;
}

const getMembers = (member: any) => {
  if (!member.children || !member.children.length) {
    return member;
  }

  return [member, member.children];
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/AddDynamicLink/index.tsx';

const defaultDynamicTypes = [...Object.values(DYNAMIC_CONTENT_TYPE)]
  .filter(i => i.value !== 'bo-settings')
  .sort((a, b) => a.index - b.index);

export const AddDynamicLink: React.FC<AddDynamicLinkProps> = props => {
  //* Props
  const { defaultData = {}, defaultDynamicIndex = 1, visible } = props;
  const { onCancel, onOk } = props;

  //* Hooks
  const { i18n, t } = useTranslation();
  const [itemTypeId, setItemTypeId] = useState<number | null>(null);

  //* Selectors
  const journeySettings = useSelector(selectJourneySettings);
  const contentSources = useSelector(selectContentSources);
  const { triggerEvent } = journeySettings;

  // Queries
  // const { data: dataTableBO, isFetching: isLoadingTableBO } = useGetDataTableBO();
  const { data: dynamicContentAttributes } = useGetDynamicContentAttr();
  const { data: promotionPool } = useGetListPromotionPool();
  const { data: promotionCodeAttributes } = useGetListPromotionCodeAttr();
  const { data: listBO } = useGetListBO();

  // Form states
  const [form] = Form.useForm();
  const [formSubmitDisabled, setFormSubmitDisabled] = useState<boolean>(true);

  const [hasError, setHasError] = useState<Record<string, boolean>>({
    [DYNAMIC_LINK_SETTING_KEY.EVENT]: false,
    [DYNAMIC_LINK_SETTING_KEY.INDEX]: false,
    [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: false,
    [DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL]: false,
    [DYNAMIC_LINK_SETTING_KEY.SOURCE]: false,
  });

  const selectedAttr = Form.useWatch<LabeledValue | undefined>(DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE, form);

  const selectedEventSource = Form.useWatch<string | undefined>(DYNAMIC_LINK_SETTING_KEY.EVENT, form);

  const selectedSource = Form.useWatch<string | undefined>(DYNAMIC_LINK_SETTING_KEY.SOURCE, form);

  const selectedDynamicContentType = Form.useWatch<string | undefined>(
    DYNAMIC_LINK_SETTING_KEY.DYNAMIC_CONTENT_TYPE,
    form,
  );

  const selectedPromotionPool = Form.useWatch<string | undefined>(DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL, form);

  // Variables
  const [eventActionId, eventCategoryId] = selectedEventSource?.split(':').map(Number) || [];

  // Selection Options states
  const [listDynamicContentTypes, setListDynamicContentTypes] = useState<LabeledValue[]>(defaultDynamicTypes);

  // BO Attribute Selection Option states
  // const [listBoAttributes, setListBoAttributes] = useState<LabeledValue[]>([]);
  // const [loadingListBoAttributes, setLoadingListBoAttributes] = useState<boolean>(false);
  const [isActionArchive, setActionArchive] = useState<boolean>(false);
  const [errorMessageAttr, setErrorMessageAttr] = useState<string>();
  // Custom Attribute Selection Option states
  const [listCustomerAttributes, setListCustomerAttributes] = useState<LabeledValue[]>([]);

  // Promotion Code Attribute Select Option states
  const [listPromotionPools, setListPromotionPools] = useState<LabeledValue[]>([]);
  const [listPromotionCodeAttributes, setListPromotionCodeAttributes] = useState<LabeledValue[]>([]);

  // Visitor Attribute Select Option states
  const [listVisitorAttributes, setListVisitorAttributes] = useState<LabeledValue[]>([]);

  // Form Fields states
  const [isDynamicLink, setIsDynamicLink] = useState<boolean>(false);

  // Event Attributes TreeSelect states
  const [expandedEventAttributes, setExpandedEventAttributes] = useState<string[]>([]); // Expanded keys in tree select

  // Queries
  const {
    data: listAttribute = [],
    isFetching: isFetchingListBoAttr,
    // isError: isErrorBoAttr,
  } = useGetListAttributeBO<LabeledValue[]>({
    itemTypeIds: itemTypeId ? [itemTypeId] : [],
    options: {
      onSuccess: data => {
        setHasError({
          [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: false,
          [DYNAMIC_LINK_SETTING_KEY.INDEX]: false,
        });

        if (data.length && selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
          if (
            form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE) &&
            form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.INDEX)
          )
            return;

          form.setFieldsValue({
            [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: data[0],
            [DYNAMIC_LINK_SETTING_KEY.INDEX]: defaultDynamicIndex,
          });
        }
      },
      select: data => serilizeBOAttr(data),
      onError: error => {
        setHasError({
          [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: true,
          [DYNAMIC_LINK_SETTING_KEY.INDEX]: true,
        });

        handleError(error, {
          component: PATH,
          action: 'useGetListAttributeBO',
          errorMessage: error?.message,
        });
      },
      enabled: !!itemTypeId,
    },
  });

  const { data: eventTrackingAttributes } = useGetEventTrackingAttributes({
    eventActionId,
    eventCategoryId,
  });
  const { data: listEvents, isFetching: isFetchingEvents } = useGetListAllEvents({
    options: {
      onSuccess: data => {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.EVENT]: false });

        if (!isEmpty(data) && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
          form.setFieldsValue({
            [DYNAMIC_LINK_SETTING_KEY.EVENT]: data[0].value,
          });
        }
      },
      onError: error => {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.EVENT]: true });

        handleError(error, {
          component: PATH,
          action: 'useGetListEventBySource',
          errorMessage: error?.message,
        });
      },
      select: data => {
        const { rows = [] } = data || {};
        return rows.map(event => {
          return {
            label: event.label,
            value: `${event.eventActionId}:${event.eventCategoryId}`,
          };
        });
      },
    },
  });

  const { data: listSources, isFetching: isFetchingListSources } = useGetListSourceByEvent({
    eventActionId,
    eventCategoryId,
    options: {
      onSuccess: data => {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.SOURCE]: false });
      },
      select: data => {
        const { rows } = data || {};

        return rows.map((row: any) => {
          return {
            label: row.label,
            value: row.value,
          };
        });
      },
      onError: error => {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.SOURCE]: true });
        handleError(error, {
          component: PATH,
          action: 'useGetListSourceBO',
          errorMessage: error?.message,
        });
      },
    },
  });

  const { data: listEventAttributes, isFetching: isFetchingEventAttr } = useGetListEventAttr<LabeledTreeValue[]>(
    selectedEventSource || '',
    Array.isArray(selectedSource) ? selectedSource.join(',') : selectedSource || '',
    {
      onSuccess: () => setHasError({ [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: false }),
      onError: error => {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: true });

        handleError(error, {
          component: PATH,
          action: 'useGetListEventAttr',
          errorMessage: error?.message,
        });
      },
      select: data => {
        return data.map(row => {
          const event = new ListAttribute(row);

          return {
            value: `${event.id}`,
            itemTypeId: event.itemTypeId,
            propertyName: event.eventPropertyName,
            label: event.eventPropertyDisplay,
            children: event.items?.map(child => ({
              value: `${event.id}.${child.itemPropertyName}`,
              itemTypeId: child.itemTypeId,
              itemTypeName: event.itemTypeName,
              propertyName: child.itemPropertyName,
              eventPropertySyntax: child.eventPropertySyntax,
              label:
                child.propertyDisplayMultilang[i18n.language] ||
                child.propertyDisplayMultilang[get(child, "propertyDisplayMultilang['DEFAULT_LANG']")] ||
                child.itemPropertyDisplay,
              children: [],
              status: +child.status,
              type: +child.type,
            })),
            status: 1,
            type: event.propertyType,
          };
        });
      },
    },
  );

  // Update form field data with default data/ or reset it to initial states
  const resetFieldsValue = useCallback(() => {
    form.setFieldsValue({
      [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: undefined,
    });
  }, [form]);

  // Memo
  const isShowEventIndexField = useDeepCompareMemo(() => {
    if (selectedAttr && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      if (!!selectedAttr.itemTypeId) {
        return !!get(selectedAttr, 'eventPropertySyntax', '').includes('items');
      }

      return selectedAttr.type === ATTRIBUTE_TYPE.ITEM;
    }

    return false;
  }, [selectedAttr, selectedDynamicContentType, eventTrackingAttributes?.mainObjects]);

  const isValidatePromotionSelected = useDeepCompareMemo(() => {
    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value && selectedPromotionPool) {
      return (listPromotionPools || []).some(
        attr => attr.value === selectedPromotionPool || attr.label === selectedPromotionPool,
      );
    } else {
      return true;
    }
  }, [selectedDynamicContentType, selectedPromotionPool, listPromotionPools]);

  // Add BO settings (if exists) to list Dynamic content Type, or removed from the previous states
  useEffect(() => {
    const temp = [...defaultDynamicTypes];

    contentSources.groups.forEach(g => {
      if (!g.itemTypeId) return;

      temp.push({
        index: temp.length,
        label: `${g.groupName} (${g.itemTypeDisplay})`,
        value: `content-source::${g.groupId}::${g.itemTypeId}`,
      });
    });

    setListDynamicContentTypes(temp);
  }, [contentSources.groups]);

  // Init list Promotion Pools
  useEffect(() => {
    const { rows = [] } = promotionPool || {};

    if (!rows?.length) {
      setHasError({ [DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL]: true });
    } else {
      setListPromotionPools(rows.map(({ code, name }) => ({ label: name, value: code })));
    }
  }, [promotionPool]);

  useEffect(() => {
    let listTmp = [] as any;
    switch (true) {
      case selectedDynamicContentType && regexCSType.test(selectedDynamicContentType):
        listTmp = [...listAttribute];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
        listTmp = [...listPromotionCodeAttributes];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
        listTmp = [...listCustomerAttributes];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
        listTmp = [...listVisitorAttributes];
        break;
      default:
        break;
    }
    const { errorMessage, isDisable } = isCheckStatusAttr(listBO, itemTypeId, listTmp, selectedAttr?.value);
    setActionArchive(isDisable);
    setErrorMessageAttr(errorMessage);
  }, [selectedAttr]);

  // Init list BO Attributes
  // useEffect(() => {
  //   const { header = [] } = dataTableBO || {};

  //   setLoadingListBoAttributes(true);

  //   if (header?.length) {
  //     setListBoAttributes(
  //       header.map(h => ({
  //         label:
  //           h.propertyDisplayMultilang[i18n.language.toUpperCase()] ||
  //           h.propertyDisplayMultilang[get(h, "propertyDisplayMultilang['DEFAULT_LANG']", 'EN')],
  //         value: h.itemPropertyName,
  //       })),
  //     );
  //   }

  //   setLoadingListBoAttributes(false);
  // }, [dataTableBO, i18n.language]);

  // Init list Promotion Code Attributes
  useEffect(() => {
    if (promotionCodeAttributes?.length) {
      setListPromotionCodeAttributes(
        promotionCodeAttributes
          .filter(attr => +attr?.status === 1)
          .map(attr => ({
            label:
              attr.propertyDisplayMultilang[i18n.language] ||
              attr.propertyDisplayMultilang[get(attr, "propertyDisplayMultilang['DEFAULT_LANG']", 'EN')],
            value: attr.itemPropertyName,
            disabled: parseInt(attr.status) === 4,
            status: attr.status,
          })),
      );
    } else {
      setHasError({ [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: true });
    }
  }, [promotionCodeAttributes, i18n.language]);

  // Init list Visitor/Customer Attributes
  useEffect(() => {
    if (dynamicContentAttributes) {
      const { customer, visitor } = dynamicContentAttributes;
      if (customer.length) {
        setListCustomerAttributes(
          customer.map(c => ({
            label: c.label[i18n.language.toUpperCase()] || c.label[get(c, "label['DEFAULT_LANG']")] || c.defaultLabel,
            value: c.value,
            disabled: parseInt(c.status) === 4,
            status: c.status,
          })),
        );
      } else {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: true });
      }

      if (visitor.length) {
        setListVisitorAttributes(
          visitor.map(v => ({
            label: v.label[i18n.language.toUpperCase()] || v.label[get(v, "label['DEFAULT_LANG']")] || v.defaultLabel,
            value: v.value,
            disabled: parseInt(v.status) === 4,
            status: v.status,
          })),
        );
      } else {
        setHasError({ [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: true });
      }
    } else {
      setHasError({ [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: true });
    }
  }, [dynamicContentAttributes, i18n.language]);

  // useEffect(() => {
  //   if (!selectedSource && listSources[0]?.value) {
  //     setSelectedSource(listSources[0].value);
  //   }

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [listSources]);

  // ------------------------ //

  // Update form field data with default data/ or reset it to initial states
  useEffect(() => {
    if (!visible) {
      form.resetFields();
      return;
    }

    if (Object.keys({ ...defaultData })?.length) {
      let draftDefaultData = { ...defaultData };

      // Check if trigger event of journey has event then don's set current event and source
      if (triggerEvent?.eventActionId) {
        draftDefaultData = omit(draftDefaultData, ['event', 'source']);
      }

      form.setFieldsValue(draftDefaultData);

      if (defaultData.hasOwnProperty(DYNAMIC_LINK_SETTING_KEY.LINK_TYPE)) {
        setIsDynamicLink(defaultData[DYNAMIC_LINK_SETTING_KEY.LINK_TYPE] === DYNAMIC_LINK_TYPE.DYNAMIC);
      }
    }
    // Reset default selected states
    else {
      form.resetFields();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultData, visible]);

  useDeepCompareEffect(() => {
    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      const { eventActionId, eventCategoryId, insightPropertyIds } = triggerEvent || {};

      if (!!eventActionId && !!eventCategoryId) {
        form.setFieldsValue({
          [DYNAMIC_LINK_SETTING_KEY.EVENT]: `${eventActionId}:${eventCategoryId}`,
        });
      }

      if (Array.isArray(insightPropertyIds) && insightPropertyIds.length) {
        form.setFieldsValue({
          [DYNAMIC_LINK_SETTING_KEY.SOURCE]: insightPropertyIds,
        });
      }
    }
  }, [triggerEvent, selectedDynamicContentType]);

  // Disable Apply button if there's any error
  useEffect(() => {
    form.validateFields().then(values => {
      const shouldDisable =
        Object.values(hasError).some(error => error) ||
        !values[DYNAMIC_LINK_SETTING_KEY.TEXT]?.toString().trim().length ||
        (values[DYNAMIC_LINK_SETTING_KEY.LINK_TYPE] === DYNAMIC_LINK_TYPE.STATIC &&
          !values[DYNAMIC_LINK_SETTING_KEY.URL]?.toString().trim().length);

      setFormSubmitDisabled(shouldDisable);
    });
  }, [form, hasError]);

  useDeepCompareEffect(() => {
    if (listSources && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      if (!isEmpty(form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.SOURCE))) return;

      const defaultValue = !!listSources[0] ? [listSources[0].value] : [];

      form.setFieldsValue({
        [DYNAMIC_LINK_SETTING_KEY.SOURCE]: defaultValue,
      });
    }
  }, [selectedEventSource, listSources, form, selectedDynamicContentType]);

  useDeepCompareEffect(() => {
    if (listEventAttributes && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      const initEventAttr = listEventAttributes[0]?.children.length
        ? getAvailableAttrs(listEventAttributes[0]?.children)[0]
        : listEventAttributes[0];

      if (form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE)) {
        const eventAttr = form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE);

        setExpandedEventAttributes([eventAttr.value.split('.')[0]]);

        return;
      }

      form.setFieldsValue({
        [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: initEventAttr,
      });

      setExpandedEventAttributes([initEventAttr.value.split('.')[0]]);
    }
  }, [selectedSource, selectedEventSource, listEventAttributes, form, selectedDynamicContentType]);

  useDeepCompareEffect(() => {
    if (selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
      const { itemTypeId: selectedItemTypeId } = serializeCSSelectedString(selectedDynamicContentType);

      if (itemTypeId !== +selectedItemTypeId) {
        setItemTypeId(+selectedItemTypeId);
      }
    }
  }, [selectedDynamicContentType]);

  //* Handlers
  const handleOnCancelModal = () => {
    if (onCancel) {
      onCancel();
    }
  };
  const handleOnOkModal = () => {
    if (isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      return;
    }
    form.validateFields().then((values: Record<string, any>) => {
      if (onOk) {
        // Handle Merge tag for event attributes
        if (values.type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
          if (values.index != null && !get(values, 'attribute.itemTypeName', null)) {
            const itemTypeName =
              listBO?.find(({ id }) =>
                get(eventTrackingAttributes, 'mainObjects', []).some(({ itemTypeId }) => itemTypeId === id),
              )?.name || null;

            if (!!itemTypeName) {
              set(values, 'attribute.itemTypeName', itemTypeName);
            }
          }
        }

        onOk(values);
      }
    });
  };

  const handleOnClickParentNode = (item: LabeledTreeValue) => {
    if (item.children.length) {
      if (expandedEventAttributes.includes(item.value)) {
        // close expand
        const newExpandedKeys = expandedEventAttributes.filter(key => key !== item.value);
        handleTreeExpand(newExpandedKeys);
      } else {
        // open expand
        handleTreeExpand(prev => [...prev, item.value]);
      }
    }
  };
  const handleTreeExpand = expandedKeys => {
    setExpandedEventAttributes(expandedKeys);
  };

  const handleFormValuesChanges = changedValues => {
    const [fieldName, fieldValue] = Object.entries(changedValues)[0] as [string, any];

    switch (fieldName) {
      case DYNAMIC_LINK_SETTING_KEY.EVENT:
        form.setFieldsValue({
          [DYNAMIC_LINK_SETTING_KEY.SOURCE]: undefined,
        });

        resetFieldsValue();

        break;
      case DYNAMIC_LINK_SETTING_KEY.SOURCE:
        resetFieldsValue();

        break;
      case DYNAMIC_LINK_SETTING_KEY.LINK_TYPE:
        setIsDynamicLink(fieldValue === DYNAMIC_LINK_TYPE.DYNAMIC);

        break;
      case DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE:
        let listAttributes: any[] = [];

        switch (true) {
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
            listAttributes = flatMapDeep(listEventAttributes, getMembers);

            form.setFieldsValue({ [DYNAMIC_LINK_SETTING_KEY.INDEX]: defaultDynamicIndex });
            break;
          case selectedDynamicContentType && regexCSType.test(selectedDynamicContentType):
            form.setFieldsValue({ [DYNAMIC_LINK_SETTING_KEY.INDEX]: defaultDynamicIndex });
            break;
          default:
            break;
        }

        // add dataType to
        if (listAttributes.length) {
          const attr = listAttributes.find(attr => {
            if (attr.value === fieldValue.value) {
              return attr;
            }

            return null;
          });

          form.setFieldsValue({
            [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: attr,
          });
        }

        break;
      case DYNAMIC_LINK_SETTING_KEY.DYNAMIC_CONTENT_TYPE:
        resetFieldsValue();

        switch (true) {
          case regexCSType.test(fieldValue):
            const { itemTypeId: selectedItemTypeId } = serializeCSSelectedString(String(fieldValue));

            if (isNaN(selectedItemTypeId)) break;

            setItemTypeId(Number(selectedItemTypeId));

            const key = [QUERY_KEYS.GET_EVENT_ATTRIBUTE_BO, [selectedItemTypeId]];
            const listAttBOByItemTypeId = serilizeBOAttr(queryClient.getQueryData(key));

            form.setFieldsValue({
              [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: listAttBOByItemTypeId[0],
              [DYNAMIC_LINK_SETTING_KEY.INDEX]: defaultDynamicIndex,
            });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
            form.setFieldsValue({
              [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: listCustomerAttributes[0],
            });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
            if (!listEvents?.length) break;

            form.setFieldsValue({
              [DYNAMIC_LINK_SETTING_KEY.EVENT]: listEvents[0]?.value,
              [DYNAMIC_LINK_SETTING_KEY.SOURCE]: undefined,
              [DYNAMIC_LINK_SETTING_KEY.INDEX]: defaultDynamicIndex,
            });

            // setSelectedSource(listSources[0]?.value);

            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
            form.setFieldsValue({
              [DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL]: listPromotionPools[0]?.value,
              [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: listPromotionCodeAttributes[0],
            });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
            form.setFieldsValue({
              [DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]: listVisitorAttributes[0],
            });
            break;
        }

        break;
    }

    // Disable submit button whenever each of the input values got empty
    const allValues = form.getFieldsValue(true);

    const shouldDisable =
      !allValues[DYNAMIC_LINK_SETTING_KEY.TEXT]?.toString().trim().length ||
      (allValues[DYNAMIC_LINK_SETTING_KEY.LINK_TYPE] === DYNAMIC_LINK_TYPE.STATIC &&
        !allValues[DYNAMIC_LINK_SETTING_KEY.URL]?.toString().trim().length);

    setFormSubmitDisabled(shouldDisable);
  };

  //* Render functions
  const renderBoAttrFields = () => {
    if (isFetchingListBoAttr) {
      return <Spin className="ants-flex ants-justify-center" spinning />;
    }

    if (hasError[DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.boAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listAttribute[0]}
        label={t(translations.dynamicLink.modal.label.attribute)}
        name={DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingListBoAttr}
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listAttribute, selectedAttr)}
          showSearch
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };
  const renderIndexFields = ({ isLoading = false, disabled = false }) => {
    if (isLoading) {
      return <Spin className="ants-flex ants-justify-center" spinning />;
    }

    if (hasError[DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]) {
      return null;
    }

    return (
      <Form.Item
        className="ants-items-center"
        label={t(translations.dynamicLink.modal.label.index)}
        name={DYNAMIC_LINK_SETTING_KEY.INDEX}
        required
      >
        <Select
          className={classnames('ants-h-7')}
          disabled={disabled}
          options={Array.from({ length: 90 }, (_, i) => ({ value: i + 1, label: i + 1 }))}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderCustomerAttrFields = () => {
    if (hasError[DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.customerAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listCustomerAttributes[0]}
        label={t(translations.dynamicLink.modal.label.attribute)}
        name={DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listCustomerAttributes, selectedAttr)}
          showSearch
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };

  const renderEventAttrFields = () => {
    // if (loadingListEventAttributes) {
    //   return <Spin className="ants-flex ants-justify-center" spinning />;
    // }

    if (hasError[DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.eventAttr)}</Text>;
    }

    return (
      <Fragment>
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicLink.modal.label.selectEventAttr)}
          name={DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE}
          required
        >
          <TreeSelect
            className={classnames('ants-h-7')}
            // filterTreeNode={(val, option) => {
            //   if (option?.children?.length) {
            //     return option?.children?.some(
            //       child => (child?.title?.toString() || '').toLowerCase().indexOf(val.toLowerCase().trim()) >= 0,
            //     );
            //   } else {
            //     return (option?.title?.toString() || '').toLowerCase().indexOf(val.toLowerCase().trim()) >= 0;
            //   }
            // }}
            labelInValue
            showSearch
            // treeExpandedKeys={expandedEventAttributes}
            // onSearch={handleTreeSearch}
            // onTreeExpand={handleTreeExpand}
            loading={isFetchingEventAttr}
          >
            {listEventAttributes?.map(attr => (
              <TreeNode
                key={attr.value}
                value={attr.value}
                selectable={!attr?.children?.length}
                title={
                  attr.children?.length ? (
                    <div onClick={() => handleOnClickParentNode(attr)}>{attr.label}</div>
                  ) : (
                    attr.label
                  )
                }
              >
                {buildOptionAttrArchive(attr?.children, selectedAttr)?.map(item => (
                  <TreeNode key={item.value} value={item.value} title={item.label} />
                ))}
              </TreeNode>
            ))}
          </TreeSelect>
        </Form.Item>
        {form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE) &&
          form.getFieldValue(DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE)?.status !== 1 && (
            <Text type="warning">{t(translations.dynamicContent.modal.message.selectDisableEventAttr)}</Text>
          )}
      </Fragment>
    );
  };
  const renderEventFields = () => {
    if (hasError[DYNAMIC_LINK_SETTING_KEY.EVENT]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.eventBySource)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listEvents[0]?.value}
        label={t(translations.dynamicLink.modal.label.selectEvent)}
        name={DYNAMIC_LINK_SETTING_KEY.EVENT}
        required
      >
        <Select
          disabled={!!triggerEvent?.eventActionId && !!triggerEvent?.eventCategoryId}
          className={classnames('ants-h-7')}
          options={listEvents}
          showSearch
          loading={isFetchingEvents}
        />
      </Form.Item>
    );
  };
  const renderSourceFields = () => {
    if (hasError[DYNAMIC_LINK_SETTING_KEY.SOURCE]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.source)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listSources[0]?.value}
        label={t(translations.dynamicLink.modal.label.selectSource)}
        name={DYNAMIC_LINK_SETTING_KEY.SOURCE}
        required
      >
        <Select
          disabled={!isEmpty(triggerEvent?.insightPropertyIds)}
          mode="multiple"
          options={listSources}
          showSearch
          loading={isFetchingListSources}
        />
      </Form.Item>
    );
  };

  const renderPromotionCodeAttr = () => {
    if (hasError[DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.promotionCodeAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listPromotionCodeAttributes[0]}
        label={t(translations.dynamicLink.modal.label.promotionCodeAttr)}
        name={DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listPromotionCodeAttributes, selectedAttr)}
          showSearch
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };
  const renderPromotionPoolFields = () => {
    if (hasError[DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.promotionPools)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listPromotionPools[0]?.value}
        label={t(translations.dynamicLink.modal.label.promotionPools)}
        name={DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL}
        required
      >
        <Select className={classnames('ants-h-7')} options={listPromotionPools} showSearch />
      </Form.Item>
    );
  };

  const renderVisitorAttrFields = () => {
    if (hasError[DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE]) {
      return <Text type="error">{t(translations.dynamicLink.modal.error.visitorAttr)}</Text>;
    }

    return (
      <Form.Item
        className="ants-items-center"
        initialValue={listVisitorAttributes[0]}
        label={t(translations.dynamicLink.modal.label.attribute)}
        name={DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          className={classnames('ants-h-7')}
          labelInValue
          options={buildOptionAttrArchive(listVisitorAttributes, selectedAttr)}
          showSearch
          errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };

  const renderDynamicFields = () => {
    if (isDynamicLink) {
      return (
        <Fragment>
          <Form.Item
            className="ants-items-center"
            initialValue={listDynamicContentTypes[0].value}
            label={t(translations.dynamicLink.modal.label.contentSource)}
            name={DYNAMIC_LINK_SETTING_KEY.DYNAMIC_CONTENT_TYPE}
            required
          >
            <Select className={classnames('ants-h-7')} options={listDynamicContentTypes} />
          </Form.Item>

          {selectedDynamicContentType && regexCSType.test(selectedDynamicContentType) && (
            <Fragment>
              {renderBoAttrFields()}
              {renderIndexFields({ isLoading: isFetchingListBoAttr, disabled: isActionArchive })}
            </Fragment>
          )}

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value && (
            <Fragment>{renderCustomerAttrFields()}</Fragment>
          )}

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value && (
            <Fragment>
              {renderEventFields()}
              {renderSourceFields()}
              {renderEventAttrFields()}

              {isShowEventIndexField ? renderIndexFields({ isLoading: false, disabled: false }) : null}
            </Fragment>
          )}

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value && (
            <Fragment>
              {renderPromotionPoolFields()}
              {renderPromotionCodeAttr()}
            </Fragment>
          )}

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value && (
            <Fragment>{renderVisitorAttrFields()}</Fragment>
          )}
        </Fragment>
      );
    } else {
      return (
        <Form.Item
          className="ants-items-center"
          label={t(translations.dynamicLink.modal.label.url)}
          name={DYNAMIC_LINK_SETTING_KEY.URL}
          required
        >
          <Input placeholder={t(translations.dynamicLink.modal.placeholder.enterValue)} />
        </Form.Item>
      );
    }
  };

  return (
    <AddDynamicLinkWrapper>
      <Modal
        closable={false}
        destroyOnClose={true}
        forceRender
        mask={true}
        okButtonProps={{ disabled: formSubmitDisabled || !isValidatePromotionSelected }}
        okText={t(translations.dynamicLink.button.insert)}
        title={t(translations.dynamicLink.modal.title.insertLink)}
        visible={visible}
        onCancel={handleOnCancelModal}
        onOk={handleOnOkModal}
      >
        <Form
          className={classnames('ants-w-full')}
          form={form}
          labelAlign="left"
          labelCol={{ span: 9 }}
          wrapperCol={{ span: 15 }}
          onValuesChange={handleFormValuesChanges}
        >
          <Form.Item className="ants-items-center" initialValue="static" name={DYNAMIC_LINK_SETTING_KEY.LINK_TYPE}>
            <RadioGroup
              options={[
                {
                  label: t(translations.dynamicLink.modal.linkType.static),
                  value: DYNAMIC_LINK_TYPE.STATIC,
                },
                {
                  label: t(translations.dynamicLink.modal.linkType.dynamic),
                  value: DYNAMIC_LINK_TYPE.DYNAMIC,
                },
              ]}
            />
          </Form.Item>

          {renderDynamicFields()}

          <Form.Item
            className="ants-items-center"
            label={t(translations.dynamicLink.modal.label.text)}
            name={DYNAMIC_LINK_SETTING_KEY.TEXT}
            required
          >
            <Input
              disabled={isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value}
              placeholder={t(translations.dynamicLink.modal.placeholder.enterValue)}
            />
          </Form.Item>

          <Form.Item
            className="ants-items-center"
            label={t(translations.dynamicLink.modal.label.title)}
            name={DYNAMIC_LINK_SETTING_KEY.TITLE}
          >
            <Input
              disabled={isActionArchive && selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value}
              placeholder={t(translations.dynamicLink.modal.placeholder.enterValue)}
            />
          </Form.Item>

          <Form.Item className="ants-items-center" name={DYNAMIC_LINK_SETTING_KEY.OPEN_NEW_TAB} valuePropName="checked">
            <Checkbox>
              <Text>{t(translations.dynamicLink.modal.label.openNewTab)}</Text>
            </Checkbox>
          </Form.Item>
        </Form>
      </Modal>
    </AddDynamicLinkWrapper>
  );
};
