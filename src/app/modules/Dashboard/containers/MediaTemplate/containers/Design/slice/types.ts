// Types
import { DraggableLocation } from 'react-beautiful-dnd';
import {
  TColumnSettings,
  TDynamicTextSettings,
  TGlobalSettings,
  THTMlSettings,
  TImageSettings,
  TBusinessObjectSettings,
  TUtmTrackingSettings,
  TRowSettings,
  TSettings,
  TSlideShowSettings,
  TViewSettings,
  TTrackingModuleData,
  TContentSourceSettings,
  TCouponWheelSettings,
  TTextStyling,
} from '../types';
import { MediaTemplateType } from 'app/models/MediaTemplateType';

// Models
import { FallbackBO, SavedImage } from 'app/models';
import { PromotionPool } from 'app/models/PromotionPool';
import { BusinessObject } from 'app/models/BusinessObject';
import { ListSource, ListEvent, ListAttribute } from 'app/models/BusinesObject';

/* --- STATE --- */
export type TToolbar = {
  viewPageSelected?: string;
  isPreviewMode?: boolean;
  isOpenSaveAs?: boolean;
};

export type TErrorSidePanel = {
  fieldName?: string;
  fieldLabel?: string;
};

export type TSidePanel = {
  isCollapsed: boolean;
  type: string;
  blockSelectedId: string | number;
  activeTab: string;
  activePanel: string;
  activeSectionId: string;
  slideShowId: string;
  slideId: string;
  settings: {
    optinFields: {
      editingFieldId: string;
      errors?: TErrorSidePanel;
    };
  };
  keyActivePanel?: number;
};

export type TLeftSidePanel = {
  isCollapsed: boolean;
  activeTab: string;
};

export type TSlide = {
  id: string | number;
  type: string;
  settings: TSettings<{
    blockStylesSettings: {
      hidden: boolean;
    };
  }>;
  elements: TElement[];
};

export type TElement = {
  id: string | number;
  type: string;
  savedBlockId?: number;
  settings: TSettings<TDynamicTextSettings | THTMlSettings | TSlideShowSettings | TImageSettings | TTextStyling>;
  slides?: TSlide[];
  createdAt: string;
};

export type TColumn = {
  id: string;
  rowId: string;
  elements: TElement[];
  settings: TSettings<TColumnSettings>;
};

export type TRow = {
  id: string;
  columns: TColumn[];
  settings: TSettings<TRowSettings>;
};

export type TBlockError = string[];

export type TViewPage = {
  id: string;
  title: string;
  rows: TRow[];
  html: string;
  styles: string;
  url: string;
  settings: TViewSettings;
  blocks?: Record<string, TBlock>;
  tree?: Record<string, string[]>;
  errors?: Record<string, TBlockError>;
  thumbnail?: string;

  // code mode setting
  codeModeSettings?: Record<string, any>;
};

export type TViewPageCodeMode = {
  id: string;
  title: string;
  html: string;
  url: string;
  settings: Record<string, any>;
  thumbnail?: string;
};

export type TTemplate = {
  id: number;
  type: string;
  name: string;
};

export type TViewPageError = Record<string, string[]>;

export type TModuleTrackingMode = 'utm' | 'atm';

export type TTrackingModule = {
  isTrackingItem?: boolean;
  mode: TModuleTrackingMode;
  data: TTrackingModuleData;
};

export type TWorkspace = {
  id: string;
  name: string;
  namespace: string;
  mode: string;
  viewPages: TViewPage[];
  template: TTemplate;
  deviceType: number;
  settings: TGlobalSettings;
  boSettings: TBusinessObjectSettings;
  contentSources: TContentSourceSettings;
  utmSettings: TUtmTrackingSettings;
  trackingModule: TTrackingModule;
  isExternal?: boolean;
  isInitial?: boolean;
  thumbnail?: string;
  defaultThumbnailIndex?: number;
  errors?: {
    viewPages?: Record<string, TViewPageError>;
    boSettings?: string[];
    promotionPool?: Record<string, string>;
  };
  warnings?: {
    viewPages?: Record<string, TViewPageError>;
    promotionPool?: Record<string, string>;
  };
  journeySettings: {
    unsubscribeSegmentType?: string | null;
    triggerType?: string | null;
    triggerEvent?: {
      eventActionId: number | string;
      eventCategoryId: number | string;
      insightPropertyIds: any[];
    };
  };
  // NOTE: migrate all current objectiveTypes to categories.goal
  objectiveTypes?: number[];
  categories: {
    objective?: number[];
    seasonality?: number[];
    industry?: number[];
    goal: number[];
    journeyType?: number[];
    lifecycleStage?: number[];
  };
  description: string;

  isEmbed: boolean;
};

export type TWorkspaceCodeMode = {
  id: string;
  name: string;
  namespace: string;
  deviceType: number;
  thumbnail?: string;
  viewPages: TViewPageCodeMode[];
  description: string;
  template: TTemplate;
  errors?: {
    viewPages?: Record<string, TViewPageError>;
    boSettings?: string[];
    promotionPool?: Record<string, string>;
  };
  warnings?: {
    viewPages?: Record<string, TViewPageError>;
    promotionPool?: Record<string, string>;
  };
  isEmbed: boolean;
};

export type TExportInfo = {
  isExporting?: boolean;
  isExportPreview?: boolean;
  isExportSaveAs?: boolean;
  captureOnSave?: string;
  exportPages?: string[];
};

export type TSaveData = {
  template_name?: string;
  template_setting: Record<string, any>;
  properties?: TWorkspace;
  template_type?: number;
  thumbnail?: string;
  viewPages?: Record<string, any>[];
  device_type?: number;
};

export interface MediaTemplateDesignState {
  isDraggingBlock: boolean;
  isLoadingWorkspace: boolean;
  isSavingTemplate: boolean;
  isShowErrorAlert: boolean;
  isShowWarningAlert: boolean;
  draggingBlockId: string;
  exportInfo: TExportInfo;
  sidePanel: TSidePanel;
  leftSidePanel: TLeftSidePanel;
  toolbar: TToolbar;
  workspace: TWorkspace;
  data: TDataPayload;
  saveData: TSaveData;
}

/* --- PAYLOAD ACTION --- */
export type TSetSidePanelPayload = {
  type?: string;
  blockSelectedId?: string;
};

export type TAddElementPayload = {
  draggableId: string;
  sourceDroppableId?: string;
  droppableId: string;
  droppableIndex: number;
};

export type TAddRowPayload = {
  droppableId: string;
};

export type TSetMediaTemplateDesignPayload = {
  isDraggingBlock?: boolean;
  draggingBlockId?: string;
  sidePanel?: TSidePanel;
  toolbar?: TToolbar;
  workspace?: TWorkspace;
};

export type TReorderRow = {
  startIndex: number;
  endIndex: number;
};

export type TReorderElement = {
  destination: DraggableLocation;
  source: DraggableLocation;
  draggableId: string;
};

export type TReorderSLideElement = {
  destination: DraggableLocation;
  source: DraggableLocation;
  draggableId: string;
};

export type TCloneBlockPayload = {
  parentBlockId?: string;
  blockId: string;
  blockType?: string;
};

export type TRemoveBlockPayload = {
  parentBlockId?: string;
  blockId: string;
  blockType: string;
};

export type TUpdateViewSetting = {
  isActive?: boolean;
  global?: boolean;
  container?: Record<string, any>;
  closeButton?: Record<string, any>;
  openButton?: Record<string, any>;
  customCSS?: Record<string, any>;
  customJS?: Record<string, any>;
  slideClosedContainer?: Record<string, any>;
  fullscreenContainer?: Record<string, any>;
  sidebarRootActive?: boolean;
};

export type TSetSidePanelSettings = {
  optinFields: Record<string, any>;
};

export type THandleImportFromView = {
  viewId: string;
};

export type TDataPayload = {
  listSavedImages?: SavedImage[];
  mediaTemplateTypes?: MediaTemplateType[];
  listFallbackBO?: FallbackBO[];
  dataTracking?: any[];
  promotionPool?: {
    rows: PromotionPool[];
    total: number;
  };
  promotionCodeAttributes?: Record<string, any>[];
  sources?: ListSource[];
  events?: ListEvent[];
  attributes?: ListAttribute[];
  businessObjects?: BusinessObject[];
  personalizationAttributes?: { customer: Record<string, any>[]; visitor: Record<string, any>[] };
};

export type TUpdateBlockByIdPayload = {
  blockId: string;
  blockType?: string;
  dataUpdate: any;
};

export type TDataUpdate = {
  fieldPath: any;
  data: any;
};

export type TAddBlockPayload = {
  dragBlockType: string;
  dropBlockId: string;
  dropIndex: number;
  savedBlockId?: string | number;
};

export type TReorderBlockPayload = {
  source: {
    id: string;
    index: number;
  };
  destination: {
    id: string;
    index: number;
  };
};

export type TUpdateCurrentPageTreeBlocksPayload = {
  tree?: TDataUpdate[];
  blocks?: TDataUpdate[];
  ignoreUndoAction?: boolean;
};

export type TUpdateBlockFieldsSelectedPayload = {
  dataUpdate: Array<{
    fieldPath: string;
    data: any;
  }>;
  ignoreUndoAction?: boolean;
};

export type TUpdateBlockFieldsByIdPayload = {
  blockId: string;
  blockType?: string;
  dataUpdate: Array<{
    fieldPath: string;
    data: any;
  }>;
  ignoreUndoAction?: boolean;
};

export type TUpdateBlockTextPayload = {
  blockId: string | number;
  dataUpdate: Array<{
    fieldPath: string;
    data: any;
  }>;
  ignoreUndoAction?: boolean;
};

export type THTMLCssPayload = {
  html: string;
  styles?: string;
  viewPageId?: string;
};

export type TElementActionOption = {
  name: string;
  track: boolean;
  url?: string;
  pass: boolean;
  close?: boolean;
  phone?: string;
  copy?: string;
};

export type TElementDynamicData = {
  textData?: Record<string, any>;
  linkData?: Record<string, any>;
};

export type TElementAction = {
  element_id: string;
  selector: string;
  element_type: string;
  type: string;
  event?: string;
  delay?: number;
  script?: string;
  option?: TElementActionOption;
  dynamic?: TElementDynamicData;
  pageId?: string;
};

export type TSetMediaTemplateDetail = {
  mediaTemplateDetail: Partial<TWorkspace>;
};

export type TBlock = {
  id: string;
  type: string;
  savedBlockId?: number;
  settings: TSettings<
    TDynamicTextSettings | THTMlSettings | TSlideShowSettings | TImageSettings | TCouponWheelSettings | TTextStyling
  >;
  createdAt?: string;
};

export type TGetCloneBlocksPayload = {
  blocks: Record<string, TBlock>;
  tree: Record<string, string[]>;
  blockId: string;
  parentId?: string;
  objectShakeReferral?: {};
};

export type TRemoveBlocks = TGetCloneBlocksPayload;

export type TBuildRelationShip = {
  itemTypeId: number | string | null;
  objectType: number | string;
  objectName?: number | string | null;
  objectPropertyName: string | null;
};

export type TField = {
  id: string;
  order: number;
  inputWidth: string | number;
  name?: string;
  label?: string;
  type?: string;
  [key: string]: any;
};
