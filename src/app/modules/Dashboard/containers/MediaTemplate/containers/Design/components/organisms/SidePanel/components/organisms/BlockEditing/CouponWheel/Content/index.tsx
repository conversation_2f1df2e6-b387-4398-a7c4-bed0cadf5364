/* eslint-disable no-loop-func */
// Libraries
import React, { memo, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';
import { groupBy, omit, pick } from 'lodash';
import classnames from 'classnames';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from 'app/hooks';

// Locales
import { translations } from 'locales/translations';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Selectors
import {
  selectBlockSelected,
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectSidePanel,
  selectWorkspaceErrors,
  selectWorkspaceWarnings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Queries
import { useGetListPromotionPool } from 'app/queries/PromotionPool';
import { useGetListPromotionCodeAttr } from 'app/queries/BusinessObject';

// Atoms
import { Button, Icon, Input, Switch, Text } from 'app/components/atoms';

// Molecules
import { Modal, ImageIconSelection, InputNumber, Select } from 'app/components/molecules';
import {
  MultipleColorsSetting,
  ColorSetting,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules';
import { Collapse, Panel } from 'app/components/molecules/Collapse';

// Components
import SelectCouponCode from './components/SelectCouponCode';

// Types
import LimitSpinning, { TLimitSpinning } from './components/LimitSpinning';

// Styled
import { ContentWrapper, StyledCellWrapper, StyledTable } from './styled';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Utils
import { handleError } from 'app/utils/handleError';
import { randomColor, random } from 'app/utils/common';
import { getTranslateMessage } from 'utils/messages';
import {
  calculateWinChance,
  generateInternalCode,
  getCappingLevelOptions,
  getFrequencyOptions,
  roundNumber,
  sortValidateSectionsResults,
  validateSections,
} from '../utils';
import { TCouponWheelSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { TUpdateBlockFieldsSelectedPayload } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';
import { getPreventKeyboardAction } from 'app/utils/web';
import { Flex, Tooltip } from '@antscorp/antsomi-ui';
import { ErrorIcon, WarningIcon } from 'app/components/icons';
// import { Drawer } from 'antd';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Settings/Basic/index.tsx';

export type TSectionValidate = {
  id: string;
  key: keyof TWheelItem;
  msg: string;
  status: 'warning' | 'error';
};

export interface TWheelItem {
  sectionId: string;
  label: string;
  backgroundColor: string;
  couponCode: string;
  canWin: boolean;
  pool: boolean;
  winChance: number;
  saved: boolean;
  couponCodeAttr: string;
  internalCode: string;
  limitSpinning: TLimitSpinning;
  cappingLevel: 'journey' | 'campaign' | 'variant' | null;
  frequency: 'this hour' | 'this day' | 'this week' | 'this month' | 'lifetime' | null;
  validateInfo: Omit<TSectionValidate, 'index'>[];
  goToView: string;
}
interface BasicProps {}

const ERROR_POSITION = {
  TOP: {
    value: 'top',
    label: getTranslateMessage(translations.top.title),
  },
  CENTER: {
    value: 'center',
    label: getTranslateMessage(translations.center.title),
  },
  BOTTOM: {
    value: 'bottom',
    label: getTranslateMessage(translations.bottom.title),
  },
};

export const Content: React.FC<BasicProps> = memo(_props => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateBlockFieldsSelected, setSidePanel, setData } = mediaTemplateDesignActions;

  // Queries
  const { data: promotionPools = { rows: [], total: 0 } } = useGetListPromotionPool();
  const { data: promotionAttrs = [] } = useGetListPromotionCodeAttr<any[]>();

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const sidePanel = useSelector(selectSidePanel);
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);
  const workspaceErrors = useSelector(selectWorkspaceErrors);
  const workspaceWarnings = useSelector(selectWorkspaceWarnings);

  // const [isOpenDrawer, setIsOpenDrawer] = useState(false);

  // State
  const { sections = [] } = blockSelected?.settings as TCouponWheelSettings;
  const [openCustomizeWheel, setOpenCustomizeWheel] = useState<boolean>(false);
  const [isShowSectionValidate, setIsShowSectionValidate] = useState<boolean>(false);
  // const [sectionsValidate, setSectionsValidate] = useState<TSectionValidate[]>([]);
  // const [sections, setSections] = useState<TWheelItem[]>( || []);

  const [wheelColors, setWheelColors] = useState([
    {
      label: 'Outer Wheel Color',
      color: blockSelected.settings.outerWheelColor,
    },
    {
      label: 'Inner Wheel Color',
      color: blockSelected.settings.innerWheelColor,
    },
    {
      label: 'Flipper Color',
      color: blockSelected.settings.flipperColor,
    },
  ]);

  const errorMessage = blockSelected.settings.errorMessage || {};

  useEffect(() => {
    // Init default active panel
    if (
      ![SIDE_PANEL_COLLAPSE.COUPON_WHEEL_SETTING, SIDE_PANEL_COLLAPSE.COUPON_SETTING].includes(sidePanel.activePanel)
    ) {
      dispatch(
        setSidePanel({
          activePanel: SIDE_PANEL_COLLAPSE.COUPON_WHEEL_SETTING,
        }),
      );
    }

    if (!errorMessage || !errorMessage.position) {
      onChangeTableBlock('errorMessage', {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (promotionPools?.total) {
      dispatch(
        setData({
          promotionPool: promotionPools,
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotionPools]);

  useEffect(() => {
    const preventHandleKeyDown = getPreventKeyboardAction(['undo']).onKeyDown;
    const preventHandleKeyUp = getPreventKeyboardAction(['undo']).onKeyUp;

    if (openCustomizeWheel) {
      window.addEventListener('keydown', preventHandleKeyDown);
      window.addEventListener('keyup', preventHandleKeyUp);
    }

    return () => {
      window.removeEventListener('keydown', preventHandleKeyDown);
      window.removeEventListener('keyup', preventHandleKeyUp);
    };
  }, [openCustomizeWheel]);

  useDeepCompareEffect(() => {
    if (sections.length) {
      const validateResult = validateSections(sections, {
        promotionPools: promotionPools.rows,
        promotionErrors: workspaceErrors?.promotionPool,
        promotionWarnings: workspaceWarnings?.promotionPool,
      });

      const validateById = groupBy(validateResult, 'id');

      const dataUpdate: TUpdateBlockFieldsSelectedPayload['dataUpdate'] = [];

      sections.forEach((s, idx) => {
        dataUpdate.push({
          fieldPath: `settings.sections[${idx}].validateInfo`,
          data: validateById[s.sectionId]
            ? sortValidateSectionsResults(validateById[s.sectionId]).map(sValidate => omit(sValidate, 'id'))
            : [],
        });
      });

      if (dataUpdate.length)
        dispatch(
          updateBlockFieldsSelected({
            dataUpdate,
            ignoreUndoAction: true,
          }),
        );

      if (validateResult.length === 0) {
        setIsShowSectionValidate(false);
      }
    }
  }, [
    sections.map(s => omit(s, 'validateInfo')),
    promotionPools.rows,
    dispatch,
    updateBlockFieldsSelected,
    workspaceErrors,
    workspaceWarnings,
  ]);

  const winChangeRemain = useDeepCompareMemo(() => {
    const sum = sections.reduce((result, cur) => (cur.canWin && cur.winChance ? result + cur.winChance : result), 0);

    return roundNumber(100 - sum);
  }, [sections.map(s => pick(s, ['canWin', 'winChance']))]);

  const genWheelItem = (item: TWheelItem | null = null, idx: number) => {
    let randomLabel = `Section 1`;
    let randomInternalCode = `section_1`;

    let i = 1;
    while (sections.some(section => section.label === randomLabel || section.internalCode === randomInternalCode)) {
      ++i;
      randomLabel = `Section ${i}`;
      randomInternalCode = `section_${i}`;
    }

    const sectionId = item?.sectionId || random(5);

    return {
      key: sectionId,
      sectionId,
      idx,
      no: `${idx + 1}`,
      saved: item?.saved ?? false,
      internalCode: item ? item.internalCode : randomInternalCode,
      label: item ? item.label || '' : randomLabel,
      backgroundColor: item ? item.backgroundColor : randomColor(),
      canWin: item ? item.canWin || false : true,
      winChance: item ? +item.winChance || 0 : winChangeRemain,
      pool: item?.pool ?? true,
      couponCode: item ? item.couponCode || '' : '',
      couponCodeAttr: item ? item.couponCodeAttr || '' : '',
      delete: idx,
      limitSpinning: item?.limitSpinning || { type: 'out_of_code' },
      cappingLevel: item?.cappingLevel || null,
      frequency: item?.frequency || null,
      validateInfo: item?.validateInfo || [],
    };
  };

  const addSection = () => {
    const newSections = calculateWinChance([...sections, genWheelItem(null, sections.length)]);

    // setSections(newSections);
    onChangeTableBlock('sections', newSections);
  };

  const footer = () => (
    <Flex align="center" justify="space-between" className="ants-w-full">
      <Button type="text" onClick={addSection}>
        + {t(translations.addSection.title)}
      </Button>
      <div className="ants-w-[43%] ants-relative">
        <Icon
          className="ants-text-[#005fb8] ants-absolute ants-right-full ants-mr-2 ants-mt-0.5"
          size={20}
          type="icon-ants-info-outline"
        />

        <Text>Notes on Limit Spinning:</Text>
        <Text>The difference between the values of Limit Spins and the actual spins is less than 2%.</Text>
        <Text>
          When the value in 'Set value' or 'Out of code' runs out, it will automatically switch to available sections.
        </Text>
      </div>
    </Flex>
  );

  // Handlers
  const closeCustomizeWheel = () => {
    if (sections.every(s => s.validateInfo.filter(v => v.status === 'error').length === 0)) {
      setOpenCustomizeWheel(false);
      return;
    }

    setIsShowSectionValidate(true);
  };

  const onChangeTableBlock = useCallback(
    (type: string, value: any) => {
      try {
        const dataUpdate: TUpdateBlockFieldsSelectedPayload['dataUpdate'] = [];

        if (!blockSelected.settings.actions.CouponWheelElement) {
          dataUpdate.push({
            fieldPath: 'settings.actions',
            data: {
              CouponWheelElement: {
                event: 'wheel',
                scripts: '',
                type: '',
                options: {
                  name: '',
                  track: false,
                  url: '',
                  pass: false,
                  close: false,
                  phone: '',
                  copy: '',
                  spinDuration: 1e4,
                },
              },
            },
          });
        }

        switch (type) {
          case 'sectionColors':
            dataUpdate.push(
              {
                fieldPath: 'settings.sections',
                data: blockSelected.settings.sections.map((item, idx) => ({
                  ...item,
                  backgroundColor: value[idx],
                })),
              },
              {
                fieldPath: 'settings.sectionColors',
                data: value,
              },
            );
            break;
          case 'wheelColors':
            if (value && value.length === 3) {
              dataUpdate.push(
                { fieldPath: 'settings.outerWheelColor', data: value[0] },
                { fieldPath: 'settings.innerWheelColor', data: value[1] },
                { fieldPath: 'settings.flipperColor', data: value[2] },
              );
            }
            setWheelColors(prev => [...prev].map((item, idx) => ({ ...item, backgroundColor: value[idx] })));
            break;
          case 'sections':
            dataUpdate.push(
              { fieldPath: 'settings.sections', data: value },
              { fieldPath: 'settings.sectionColors', data: value.map(item => item.backgroundColor) },
            );
            break;
          case 'icon':
            dataUpdate.push(
              { fieldPath: 'settings.innerWheelIcon.icon', data: value },
              { fieldPath: 'settings.innerWheelImage.previewUrl', data: '' },
            );
            break;
          case 'image':
            dataUpdate.push(
              { fieldPath: 'settings.innerWheelImage.previewUrl', data: value.url },
              { fieldPath: 'settings.innerWheelIcon.icon', data: '' },
            );
            break;
          case 'iconColor':
            dataUpdate.push({ fieldPath: 'settings.innerWheelIcon.iconColor', data: value });
            break;
          case 'errorMessage':
            dataUpdate.push({
              fieldPath: 'settings.errorMessage',
              data: {
                ...(blockSelected.settings.errorMessage || {
                  position: 'bottom',
                  message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
                }),
                ...value,
              },
            });
            break;
        }
        if (dataUpdate.length) {
          dispatch(updateBlockFieldsSelected({ dataUpdate, ignoreUndoAction: false }));
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeIconColor',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockFieldsSelected],
  );

  const onChangeTable = (key: string, value: any, item: any) => {
    try {
      const { idx } = item;

      const newSections = produce(sections, draft => {
        const oldValueByKey = draft[idx][key];

        draft[idx][key] = key === 'winChance' ? roundNumber(value) : value;

        if (key === 'label' && !draft[idx]['saved']) {
          draft[idx]['internalCode'] = generateInternalCode(
            value,
            sections.map(s => s.internalCode),
          );
        }

        if (key === 'canWin') {
          draft[idx]['winChance'] = value ? winChangeRemain : 0;
        }

        if (key === 'pool') {
          draft[idx]['couponCode'] = '';
          draft[idx]['limitSpinning'] = value ? { type: 'out_of_code' } : { type: 'unlimited' };
          draft[idx]['cappingLevel'] = null;
          draft[idx]['frequency'] = null;
          draft[idx]['couponCodeAttr'] = '';
        }

        if (key === 'couponCode') {
          draft[idx]['couponCode'] = value.pool;
          draft[idx]['couponCodeAttr'] = value.attr;
        }

        if (key === 'limitSpinning' && value?.type === 'set_value' && value?.type !== oldValueByKey.type) {
          draft[idx]['cappingLevel'] = 'journey';
          draft[idx]['frequency'] = 'lifetime';
        }
      });

      onChangeTableBlock('sections', newSections);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTable',
        args: {},
      });
    }
  };

  const dataSource = sections.map((item, idx) => genWheelItem(item, idx));

  const columns: any[] = [
    {
      title: 'No.',
      dataIndex: 'no',
      key: 'no',
      width: '42px',
      className: 'no',
      render: value => (
        <StyledCellWrapper>
          <div className="no-label">{value}</div>
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Label',
      dataIndex: 'label',
      key: 'label',
      className: 'label',
      render: (value, item) => (
        <StyledCellWrapper>
          <Input
            value={value}
            maxLength={255}
            debounce={700}
            onAfterChange={value => onChangeTable('label', value, item)}
            disableUndo
          />
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Internal code',
      className: 'internal-code',
      dataIndex: 'internalCode',
      render: (value: string, item: ReturnType<typeof genWheelItem>) => {
        const helperValidateProps: Record<string, any> = {};
        const couponCodeValidate = item.validateInfo.filter(v => v.key === 'internalCode');

        if (couponCodeValidate.length) {
          helperValidateProps.status = couponCodeValidate[0].status;
          helperValidateProps.errorMsg = couponCodeValidate[0].msg;
        }

        return (
          <StyledCellWrapper>
            <Input
              value={value}
              disabled={!!item.saved}
              maxLength={255}
              onAfterChange={value => onChangeTable('internalCode', value, item)}
              debounce={700}
              required
              disableUndo
              {...helperValidateProps}
            />
          </StyledCellWrapper>
        );
      },
    },
    {
      title: 'Can Win?',
      dataIndex: 'canWin',
      key: 'canWin',
      width: 75,
      className: 'can-win',
      render: (value, item) => (
        <StyledCellWrapper>
          <Switch className="switch-btn" checked={value} onChange={checked => onChangeTable('canWin', checked, item)} />
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Win Chance (%)',
      dataIndex: 'winChance',
      key: 'winChance',
      width: 110,
      className: 'win-chance',
      render: (value, item) => (
        <StyledCellWrapper>
          <div className="ants-flex ants-flex-row-reverse ants-gap-[7px] ants-items-center">
            <InputNumber
              value={value}
              className={'!ants-w-[60px]'}
              type="number"
              max={value + winChangeRemain}
              min={0}
              disabled={!item.canWin}
              onChange={value => onChangeTable('winChance', value, item)}
              disableUndo
            />
            {/* {item.canWin ? '%' : null} */}
          </div>
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Pool',
      dataIndex: 'pool',
      key: 'pool',
      width: 60,
      className: 'pool',
      render: (value, item) => (
        <StyledCellWrapper>
          <Switch
            className="switch-btn"
            checked={value}
            disabled={!item.canWin || item.winChance === 0}
            onChange={checked => onChangeTable('pool', checked, item)}
          />
        </StyledCellWrapper>
      ),
    },
    {
      title: 'Coupon code',
      dataIndex: 'couponCode',
      key: 'couponCode',
      className: 'coupon-code',
      render: (_value: string, item: ReturnType<typeof genWheelItem>) => {
        const helperValidateProps: Record<string, any> = {};
        const couponCodeValidate = item.validateInfo.filter(v => v.key === 'couponCode' || v.key === 'couponCodeAttr');

        if (couponCodeValidate.length) {
          helperValidateProps.status = couponCodeValidate[0].status;
          helperValidateProps.helperMsg = couponCodeValidate[0].msg;

          // if (
          //   couponCodeValidate[0].msg ===
          //   getTranslateMessage(translations.messageError.promotionPoolDeactivated.message)
          // ) {
          //   helperValidateProps.forceShowHelperMsg = true;
          // }
          if (couponCodeValidate[0].msg) {
            helperValidateProps.forceShowHelperMsg = true;
          }
        }

        return (
          <StyledCellWrapper>
            <SelectCouponCode
              usePool={item.pool}
              promotionAttrs={promotionAttrs}
              promotionPools={promotionPools.rows}
              poolValue={item.couponCode}
              attrValue={item.couponCodeAttr}
              onChange={values => {
                onChangeTable('couponCode', values, item);
              }}
              disabled={!item.canWin || item.winChance === 0}
              forceShowHelperMsg={isShowSectionValidate}
              {...helperValidateProps}
            />
          </StyledCellWrapper>
        );
      },
    },
    {
      title: t(translations.limitSpinning.title),
      dataIndex: 'limitSpinning',
      key: 'limitSpinning',
      className: 'limit-spinning',
      width: 200,
      render: (value, item: ReturnType<typeof genWheelItem>) => (
        <StyledCellWrapper>
          <LimitSpinning
            usePool={item.pool}
            values={item.limitSpinning}
            showOptions={item.pool ? ['set_value', 'out_of_code'] : ['unlimited', 'set_value']}
            onChange={values => onChangeTable('limitSpinning', values, item)}
            disabled={!item.canWin || item.winChance === 0}
          />
        </StyledCellWrapper>
      ),
    },
    {
      title: t(translations.cappingLevel.title),
      dataIndex: 'cappingLevel',
      key: 'cappingLevel',
      className: 'capping-level',
      render: (value, item: ReturnType<typeof genWheelItem>) => {
        const options = getCappingLevelOptions();

        return (
          <StyledCellWrapper>
            {item.limitSpinning.type === 'set_value' ? (
              <Select
                options={options}
                value={item.cappingLevel}
                onChange={value => onChangeTable('cappingLevel', value, item)}
                disabled={!item.canWin || item.winChance === 0}
              />
            ) : (
              <Input disabled />
            )}
          </StyledCellWrapper>
        );
      },
    },
    {
      title: t(translations.frequency.title),
      dataIndex: 'frequency',
      key: 'frequency',
      className: 'frequency',
      render: (value, item: ReturnType<typeof genWheelItem>) => {
        const options = getFrequencyOptions();

        return (
          <StyledCellWrapper>
            {item.limitSpinning.type === 'set_value' ? (
              <Select
                options={options}
                value={item.frequency}
                onChange={value => onChangeTable('frequency', value, item)}
                disabled={!item.canWin || item.winChance === 0}
              />
            ) : (
              <Input disabled />
            )}
          </StyledCellWrapper>
        );
      },
    },
    {
      title: '',
      dataIndex: 'delete',
      key: 'delete',
      className: 'delete',
      render: (value, item) => (
        <StyledCellWrapper>
          <Icon
            disabled={sections.length <= 2}
            onClick={
              sections.length > 2
                ? () => {
                    const newSections = [...sections];
                    newSections.splice(value, 1);
                    // setSections(calculateWinChance(newSections));
                    onChangeTableBlock('sections', calculateWinChance(newSections));
                  }
                : undefined
            }
            type="icon-ants-outline-delete"
            className="icon-delete-section ants-block ants-text-primary ants-cursor-pointer"
            size={20}
          />
        </StyledCellWrapper>
      ),
      width: 55,
    },
  ];

  if (blockSelected) {
    return (
      <>
        <ContentWrapper>
          <Collapse
            accordion
            defaultActiveKey={SIDE_PANEL_COLLAPSE.COUPON_WHEEL_SETTING}
            activeKey={sidePanel.activePanel}
            onChange={activePanel => {
              dispatch(
                setSidePanel({
                  activePanel: activePanel as string,
                }),
              );
            }}
          >
            <Panel header={t(translations.couponWheelSetting.title)} key={SIDE_PANEL_COLLAPSE.COUPON_WHEEL_SETTING}>
              <div className="ants-flex ants-flex-col ants-space-y-5">
                <Button onClick={() => setOpenCustomizeWheel(true)} type="primary">
                  {t(translations.customizeWheelSections.title)}
                  {errors[blockSelected.id] ? (
                    <Tooltip title={'This pool is removed'}>
                      <ErrorIcon />
                    </Tooltip>
                  ) : warnings[blockSelected.id] ? (
                    <Tooltip title={'You do not have permission on this pool anymore'}>
                      <WarningIcon />
                    </Tooltip>
                  ) : null}
                </Button>
                <MultipleColorsSetting
                  label={t(translations.sectionColors.title)}
                  listColorItems={sections.map(item => ({ ...item, color: item.backgroundColor }))}
                  onChange={value => onChangeTableBlock('sectionColors', value)}
                />
                <MultipleColorsSetting
                  label={t(translations.wheelColors.title)}
                  listColorItems={wheelColors}
                  onChange={value => onChangeTableBlock('wheelColors', value)}
                />
                <ImageIconSelection
                  icon={blockSelected.settings?.innerWheelIcon?.icon}
                  onChangeIcon={value => onChangeTableBlock('icon', value)}
                  selectedImage={blockSelected.settings?.innerWheelImage?.previewUrl}
                  onChangeImage={value => onChangeTableBlock('image', value)}
                />
                {blockSelected.settings?.innerWheelIcon?.icon ? (
                  <ColorSetting
                    label={t(translations.iconColor.title)}
                    color={blockSelected.settings?.innerWheelIcon?.iconColor}
                    onChange={color => onChangeTableBlock('iconColor', color)}
                  />
                ) : null}
              </div>
            </Panel>
            <Panel header={t(translations.couponSetting.title)} key={SIDE_PANEL_COLLAPSE.COUPON_SETTING}>
              <div className="ants-flex ants-flex-col ants-space-y-5">
                <div>
                  <Text className="!ants-text-gray-4 ants-mb-5px">{t(translations.errorMessage.title)}</Text>
                  <Input
                    placeholder={t(translations.errorMessage.title)}
                    value={errorMessage?.message}
                    onChange={e => onChangeTableBlock('errorMessage', { message: e.target.value })}
                  />
                </div>

                <Select
                  label={t(translations.position.title)}
                  options={Object.values(ERROR_POSITION)}
                  // disabled={!item.canWin}
                  onChange={position => onChangeTableBlock('errorMessage', { position })}
                  value={errorMessage?.position}
                  className={`ants-w-full ants-h-[30px]`}
                />
              </div>
            </Panel>
          </Collapse>
        </ContentWrapper>

        <Modal
          wrapClassName="icons-selection-modal"
          title={
            <div className="ants-flex ants-gap-2 ants-items-center">{t(translations.customizeWheelSections.title)}</div>
          }
          headerStyle={{
            padding: '20px 20px 0',
            border: 'none',
          }}
          bodyStyle={{
            padding: '20px 0 0',
          }}
          width="min(90%, 1300px)"
          footer={null}
          // getContainer={}
          visible={openCustomizeWheel}
          onCancel={closeCustomizeWheel}
          destroyOnClose
        >
          <StyledTable
            className="ants-w-full"
            bordered
            footer={footer}
            pagination={false}
            scroll={{ y: 350, x: 1150 }}
            dataSource={dataSource}
            columns={columns}
            rowClassName={(record: ReturnType<typeof genWheelItem>, index) =>
              classnames({
                'use-pool': record.pool,
                'coupon-code-has-value': record.couponCode || record.couponCodeAttr,
                'limit-spinning-set-value': record.limitSpinning.type === 'set_value',
              })
            }
          />
        </Modal>
        {/* <button onClick={() => setIsOpenDrawer(true)}>Open drawer</button> */}
        {/* <Drawer width="90%" visible={isOpenDrawer} placement="right" bodyStyle={{ padding: 0 }}> */}
        {/*   <iframe */}
        {/*     style={{ height: '100%', width: '100%' }} */}
        {/*     src="https://localhost:8081/#/1600001262/media-template/embed" */}
        {/*   /> */}
        {/* </Drawer> */}
      </>
    );
  }

  return null;
});

Content.displayName = 'Content';
