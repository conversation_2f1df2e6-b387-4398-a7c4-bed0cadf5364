// Libraries
import { MenuProps } from 'antd';
import { cloneDeep, isEmpty, omit } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components/macro';
import tw from 'twin.macro';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { LoadingOutlined } from '@ant-design/icons';
import get from 'lodash/get';
import {
  camelCaseToSnakeCase,
  Dropdown,
  Flex,
  snakeCaseToCamelCase,
  TemplateSaveAsModal,
  TemplateValueOptions,
  useGetObjectTemplateDetail,
  useGetSaveAsGalleryPermissionEmails,
  useTemplateSave,
} from '@antscorp/antsomi-ui';
import classNames from 'classnames';

// Libs
import { ActionCreators } from 'app/libs/redux-undo';

// Locales
import { translations } from 'locales/translations';

// Components
import { Divider, Icon, Space, Spin } from 'app/components/atoms';
import { message, /* Modal, */ RadioGroup } from 'app/components/molecules';
import { usePersistTemplate, Button, Switch, Typography, Tooltip } from '@antscorp/antsomi-ui';

// Icons
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import createSagaAction, {
  ACTIONS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/sagaActions';

// Selectors
import {
  selectToolbar,
  selectUndoableIndex,
  selectUndoableLimit,
  selectIsSavingTemplate,
  selectDeviceType,
  selectWorkspace,
  selectIsShowErrorAlert,
  selectGlobalSettings,
  selectCategoriesGoal,
  selectExportInfo,
  selectIsShowWarningAlert,
  selectPromotionPool,
  selectDesignTemplateMode,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from 'app/hooks';
import { useUserInfoV2 } from 'app/hooks/useUserInfoV2';

// Utils
import { handleError } from 'app/utils/handleError';
import { random } from 'app/utils/common';
import { getTranslateMessage } from 'utils/messages';
import { getTemplateSetting } from '../../../slice/utils';

// Constants
import {
  DESIGN_TEMPLATE_MODE,
  SIDE_PANEL_TYPE,
  VIEW_PAGE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { DEVICE_TYPE } from 'app/modules/Dashboard/containers/MediaTemplate/constants';

// Types
import { TToolbar } from '../../../slice/types';
import { TGlobalSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// import { ChooseObjectTypes } from './components/organisms/ChooseObjectTypes';
import { GET_LIST_TYPE, OBJECT_TYPES, PUBLIC_LEVEL, globalToken } from '@antscorp/antsomi-ui/es/constants';
import { MENU_PERMISSION } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/constants';
import { useExternalServiceAuth } from 'app/hooks/useExternalServiceAuth';
import { initAccessInfoDefault } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/utils';
import { permissionServices } from 'app/services/Permission';
import { ObjectAccessInfo } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/types';
import { buildUpdatedCategories } from 'modules/Dashboard/containers/MediaTemplate/utils';
import { saveBase64File } from 'app/services/MediaTemplateDesign/FileSave';
import { handleUploadThumbnail } from '../../../utils';
import isEqual from 'react-fast-compare';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Toolbar/index.tsx';

interface ToolbarProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  leftToolbar?: React.ReactNode;
  rightToolbar?: React.ReactNode;
  onClickCancel?: () => void;
  onClickSave?: () => void;
  saveText?: string;
  showCloneButton?: boolean;
  dropdownOnSave?: MenuProps['items'];
  isEmbed?: boolean;
}

export const Toolbar: React.FC<ToolbarProps> = props => {
  const dispatch = useDispatch();
  const serviceAuth = useExternalServiceAuth();

  const { mutateAsync: persistTemplate, isLoading: loadingPersistTemplate } = usePersistTemplate({});

  const [isOpenSaveTemplate, setIsOpenSaveTemplate] = useState<boolean>(false);
  const [isForceBuild, setIsForceBuild] = useState<boolean>(false);
  const [objectiveTypesTemp, setObjectiveTypesTemp] = useState<Record<string, any>>({
    isSaveTemp: false,
    list: [],
  });
  const [isOnClickSave, setIsOnClickSave] = useState<boolean>(false);
  const [isOnClickApplyEmbed, setIsOnClickApplyEmbed] = useState<boolean>(false);
  const [isLoadingUploadThumbnail, setIsLoadingUploadThumbnail] = useState<boolean>(false);

  // Props
  const { leftToolbar, rightToolbar, saveText, showCloneButton, dropdownOnSave, isEmbed = false } = props;

  // I18next
  const { t } = useTranslation();
  const userInfo = useUserInfoV2();

  // Ref
  const renderGroupRef = useRef(<></>);

  // Actions
  const { setToolbar, setSidePanel, setDeviceType, setGlobalSettings, updateObjectiveTypes, setLoadingWorkspace } =
    mediaTemplateDesignActions;

  // Selectors
  const toolbar = useSelector(selectToolbar);
  const deviceType = useSelector(selectDeviceType);
  const undoableLimit = useSelector(selectUndoableLimit);
  const undoableIndex = useSelector(selectUndoableIndex);
  const isSavingTemplate = useSelector(selectIsSavingTemplate);
  const workspace = useSelector(selectWorkspace);
  const promotionPool = useSelector(selectPromotionPool);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);
  const isShowWarningAlert = useSelector(selectIsShowWarningAlert);
  const globalSettings = useSelector(selectGlobalSettings);
  const objectiveTypes = useSelector(selectCategoriesGoal);
  const exportInfo = useSelector(selectExportInfo);
  const designTemplateMode = useSelector(selectDesignTemplateMode);
  const isCodeModeDesign = designTemplateMode === DESIGN_TEMPLATE_MODE.CODE;

  const [selectedTemplate, setSelectedTemplate] = useState<string>();
  const [tempThumbnails, setTempThumbnails] = useState<any[]>(
    workspace.viewPages.flatMap(item => (item.settings.isActive ? item.thumbnail : [])).filter(Boolean),
  );

  const { isLoading: isLoadingSaveAsGalleryPermissionEmails } = useGetSaveAsGalleryPermissionEmails({
    args: {
      auth: serviceAuth,
    },
  });

  // NOTE: TEMPLATE SAVE AS V2
  const { data: objectTemplateDetail } = useGetObjectTemplateDetail({
    args: {
      auth: serviceAuth,
      params: {
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: PUBLIC_LEVEL.RESTRICTED,
        template_id: selectedTemplate || workspace.id,
      },
    },
  });

  // Memo
  const shareAccessInfo: ObjectAccessInfo = useDeepCompareMemo(() => {
    const { shareAccess } = objectTemplateDetail || {};

    if (!isEmpty(shareAccess)) {
      return snakeCaseToCamelCase(shareAccess || {}, true);
    }

    return initAccessInfoDefault(userInfo || {});
  }, [objectTemplateDetail]);

  const defaultThumbnail = useDeepCompareMemo(() => {
    const thumbnailIdx = workspace.viewPages
      .filter(page => page.settings?.isActive)
      .map(item => item.thumbnail)
      .findIndex(item => item === workspace.thumbnail);

    return thumbnailIdx === -1 ? workspace.defaultThumbnailIndex || 0 : thumbnailIdx;
  }, [workspace]);

  const templateSaveDefaultValue: Partial<TemplateValueOptions> = useDeepCompareMemo(() => {
    const defaultValue: Partial<TemplateValueOptions> = {
      accessInfo: shareAccessInfo,
      categories: workspace.categories,
      defaultThumbnail,
      description: workspace.description,
      templateName: {
        id: +workspace.id,
        label: undefined,
      },
      saveOption: 'save-exist',
    };

    return defaultValue;
  }, [workspace, isEmbed, shareAccessInfo]);

  const {
    categoryItems,
    templateItems,
    value: templateValue,
    onChange: onChangeTemplateValue,
    setValue,
    searchNameProps,
    form,
  } = useTemplateSave({
    service: serviceAuth,
    config: {
      getListType: GET_LIST_TYPE.OWNER,
      objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
      publicLevel: PUBLIC_LEVEL.RESTRICTED,
      limitListPerPage: 20,
    },
    defaultValue: {
      defaultThumbnail,
      categories: (workspace.categories as any) || {},
      saveOption: 'save-exist',
      templateName: { id: +workspace.id, label: undefined },
      description: objectTemplateDetail?.description || '',
    },
  });

  // Variables
  const { saveOption } = templateValue;

  const viewPages = useMemo(() => {
    return Object.values(VIEW_PAGE).map((viewPage, index) => ({
      value: viewPage.name,
      label: (
        <div>
          {Object.keys(get(workspace.errors, `viewPages.${viewPage.name}`, {})).length && isShowErrorAlert ? (
            <ErrorIcon style={{ display: 'inline-block', marginRight: 5, marginTop: -3 }} />
          ) : Object.keys(get(workspace.warnings, `viewPages.${viewPage.name}`, {})).length && isShowWarningAlert ? (
            <WarningIcon style={{ display: 'inline-block', marginRight: 5, marginTop: -3 }} />
          ) : (
            ''
          )}
          {viewPage.label}
        </div>
      ),
    }));
  }, [isShowErrorAlert, workspace.errors]);
  const { exportPages = [] } = exportInfo;

  useEffect(() => {
    const newThumbnails = workspace.viewPages
      .flatMap(item => (item.settings.isActive ? item.thumbnail : []))
      .filter(Boolean);

    if (!isEqual(newThumbnails, tempThumbnails)) {
      setTempThumbnails(newThumbnails);
    }
  }, [workspace.viewPages]);

  useEffect(() => {
    if (exportPages?.length) {
      setIsForceBuild(true);
    }
  }, [exportPages?.length]);

  useEffect(() => {
    if (isForceBuild && !isSavingTemplate) {
      // const newListObject = handleDetectObjectiveTypes(workspace, [1]);
      // dispatch(updateObjectiveTypes(newListObject));
      // setObjectiveTypesTemp(prev => ({
      //   ...prev,
      //   isSaveTemp: false,
      //   list: newListObject,
      // }));
      setIsForceBuild(false);
      if (isEmbed && isOnClickApplyEmbed) {
        handleSaveMediaTemplate();
      } else if (isOnClickSave) {
        setIsOnClickSave(false);
        setIsOpenSaveTemplate(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSavingTemplate]);

  useDeepCompareEffect(() => {
    if (!!templateSaveDefaultValue) {
      onChangeTemplateValue(templateSaveDefaultValue);
    }
  }, [omit(templateSaveDefaultValue, 'accessInfo')]);

  useDeepCompareEffect(() => {
    if (saveOption === 'save-exist') {
      // Set timeout for get updated value
      const currentShareAccess = snakeCaseToCamelCase(
        objectTemplateDetail?.shareAccess || {},
        true,
      ) as ObjectAccessInfo;

      setValue(prev => ({
        ...prev,
        description: objectTemplateDetail?.description,
        accessInfo: !isEmpty(currentShareAccess) ? currentShareAccess : initAccessInfoDefault(userInfo || {}),
      }));
    }
  }, [objectTemplateDetail, saveOption]);

  const onChangeToolbar = (payload: Partial<TToolbar>) => {
    try {
      dispatch(
        setToolbar({
          ...toolbar,
          ...payload,
        }),
      );

      dispatch(
        setSidePanel({
          type: SIDE_PANEL_TYPE.BLOCKS.name,
          blockSelectedId: '',
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangePage',
        args: {},
      });
    }
  };
  const onChangeGlobalSettings = (payload: Partial<TGlobalSettings>) => {
    try {
      dispatch(setGlobalSettings(payload));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeGlobalSettings',
        args: { payload },
      });
    }
  };

  const handleSaveMediaTemplate = (isSelectPreview: boolean = false) => {
    dispatch(createSagaAction(ACTIONS.SAVE_MEDIA_TEMPLATE, { isSelectPreview }));
  };

  const handleTogglePopupObjective = (isOnSave = false) => {
    if (isEmbed) {
      dispatch(setLoadingWorkspace(true));

      if (exportPages.length) {
        dispatch(createSagaAction(ACTIONS.SAVE_MEDIA_TEMPLATE, { isForceBuild: true }));
      } else {
        dispatch(updateObjectiveTypes(objectiveTypes));
        handleSaveMediaTemplate();
      }
      setIsOnClickApplyEmbed(true);
      return;
    }
    if (isForceBuild) {
      dispatch(createSagaAction(ACTIONS.SAVE_MEDIA_TEMPLATE, { isForceBuild: true }));
    } else {
      if (!objectiveTypesTemp.isSaveTemp) {
        // setObjectiveTypesTemp(prev => ({
        //   isSaveTemp: true,
        //   list: objectiveTypes,
        // }));
      }
      setIsOpenSaveTemplate(true);
    }
    setIsOnClickSave(isOnSave);
  };

  const handleCancel = () => {
    onChangeTemplateValue(templateSaveDefaultValue);
    setSelectedTemplate(workspace.id);
    setIsOpenSaveTemplate(false);
    setIsOnClickSave(false);
  };

  if (!isSavingTemplate) {
    renderGroupRef.current = !isCodeModeDesign ? (
      <RadioGroup
        label={`${t(translations.pages.title)}:`}
        value={toolbar.viewPageSelected}
        options={viewPages}
        onChange={e =>
          onChangeToolbar({
            viewPageSelected: e.target.value,
          })
        }
        optionType="button"
      />
    ) : (
      <></>
    );
  }

  const handleUpdateTemplate = async (value: TemplateValueOptions) => {
    const { templateName, accessInfo, categories, defaultThumbnail, description, saveOption } = value;
    const updatedCategories = buildUpdatedCategories({
      categories,
      categoryItems,
    });
    const persistType: 'create' | 'update' = saveOption === 'save-new' ? 'create' : 'update';
    const isCreateNew = saveOption === 'save-new';

    let thumbnail = '';
    try {
      let cloneWorkspace = cloneDeep(workspace);

      // Handle Update new thumbnails
      cloneWorkspace.viewPages
        .filter(viewPage => viewPage.settings.isActive)
        .forEach((viewPage, idx) => {
          viewPage.thumbnail = tempThumbnails[idx] || '';
        });

      let updateData = {
        ...(isCreateNew ? { template_name: templateName?.label?.trim() } : { template_id: +templateName?.id! }),
        properties: {},
        template_setting: getTemplateSetting(cloneWorkspace, { promotionPool }),
        template_type: cloneWorkspace.template.id,
        device_type: deviceType,
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: PUBLIC_LEVEL.RESTRICTED,
        share_access: camelCaseToSnakeCase(accessInfo!, true),
        description,
        thumbnail: '',
        ...updatedCategories,
      };

      // Case Update
      if (!isCreateNew) {
        setIsLoadingUploadThumbnail(true);

        // Handle upload images
        cloneWorkspace = await handleUploadThumbnail({ templateId: +templateName?.id!, workspace: cloneWorkspace });

        setIsLoadingUploadThumbnail(false);

        // Set thumbnail
        thumbnail =
          cloneWorkspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
            defaultThumbnail || 0
          ] || '';

        updateData = {
          ...updateData,
          properties: {
            ...cloneWorkspace,
            viewPages: cloneWorkspace.viewPages,
            categories: categories,
            defaultThumbnailIndex: defaultThumbnail,
            thumbnail,
            ...(persistType === 'create' ? { isInitial: true } : {}),
          },
          thumbnail,
        };
      }

      const { id: templateId } = await persistTemplate({
        persistType,
        params: {
          auth: serviceAuth,
          data: updateData,
        },
      });

      if (templateId && isCreateNew) {
        setIsLoadingUploadThumbnail(true);

        cloneWorkspace = await handleUploadThumbnail({ templateId: templateId, workspace: cloneWorkspace });

        setIsLoadingUploadThumbnail(false);

        // Set thumbnail
        thumbnail =
          cloneWorkspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
            defaultThumbnail || 0
          ] || '';

        await persistTemplate({
          persistType: 'update',
          params: {
            auth: serviceAuth,
            data: {
              template_id: templateId,
              properties: {
                ...cloneWorkspace,
                viewPages: cloneWorkspace.viewPages,
                categories: categories,
                defaultThumbnailIndex: defaultThumbnail,
                thumbnail,
                ...(persistType === 'create' ? { isInitial: true } : {}),
              },
              thumbnail,
            },
          },
        });
      }

      message.success(
        t(
          isCreateNew
            ? translations.createTemplate.notification.createSuccess
            : translations.updateTemplate.notification.updateSuccess,
        ),
      );

      handleCancel();
    } catch (error) {
      const errorMessage = get(error, 'response.data.data', {});
      message.error(errorMessage);

      handleError(error, {
        path: PATH,
        name: 'handleUpdateTemplate',
        args: { value },
      });
    }
  };

  const handleCloneTemplate = async () => {
    try {
      const { template, deviceType, name, categories } = workspace;

      await persistTemplate({
        persistType: 'create',
        params: {
          auth: serviceAuth,
          data: {
            template_name: `${name}-copy ${random(6)}`,
            template_type: template?.id,
            device_type: deviceType,
            template_setting: getTemplateSetting(workspace, { promotionPool }),
            properties: {
              ...workspace,
              isInitial: true,
            },
            object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
            public_level: PUBLIC_LEVEL.RESTRICTED,
            thumbnail: workspace.thumbnail,
            description: workspace.description,
            ...categories,
          },
        },
      });

      message.success(t(translations.cloneMediaTemplate.cloneTemplateSuccess.title));
    } catch (error) {
      message.error(t(translations.cloneMediaTemplate.cloneTemplateFailed.title));
    }
  };

  return (
    <ToolbarWrapper>
      <div className="__left ants-gap-10px">
        {leftToolbar}
        {!isCodeModeDesign && <Divider type="vertical" height={28} dot />}
        {renderGroupRef.current}
      </div>
      <div className="__right">
        {rightToolbar}

        <Flex align="center" gap={10}>
          <Icon
            type="icon-ants-undo-2"
            className="ants-text-primary ants-cursor-pointer"
            disabled={undoableIndex === 0}
            onClick={() => dispatch(ActionCreators.undo())}
          />
          <Icon
            type="icon-ants-redo-2"
            className="ants-text-primary ants-cursor-pointer"
            disabled={undoableLimit ? undoableIndex === undoableLimit - 1 : false}
            onClick={() => dispatch(ActionCreators.redo())}
          />
        </Flex>

        <Divider type="vertical" dot height={28} />

        <Flex align="center" gap={10}>
          {showCloneButton && (
            <Tooltip overlay={t(translations.cloneMediaTemplate.cloneTemplate.title)}>
              <Button
                loading={loadingPersistTemplate}
                icon={
                  <Icon
                    type="icon-ants-material-outline-content-copy"
                    overlayStyle={{ color: '#005FB8', fontSize: '20px' }}
                  />
                }
                onClick={handleCloneTemplate}
              />
            </Tooltip>
          )}
          {/* <Tooltip
            overlay={t(
              deviceType === DEVICE_TYPE.MOBILE.value
                ? translations.switchDesktopMode.title
                : translations.switchMobileMode.title,
            )}
          >
            <Button
              icon={
                <Icon type={deviceType === DEVICE_TYPE.MOBILE.value ? 'icon-ants-laptop' : 'icon-ants-smart-phone'} />
              }
              onClick={() => {
                dispatch(
                  setDeviceType(
                    deviceType === DEVICE_TYPE.DESKTOP.value ? DEVICE_TYPE.MOBILE.value : DEVICE_TYPE.DESKTOP.value,
                  ),
                );
              }}
            />
          </Tooltip> */}

          {Object.values(DEVICE_TYPE).map(({ value, label }) => {
            return (
              <Tooltip overlay={label}>
                <Button
                  className={classNames({
                    '!ants-border-primary': deviceType === value,
                    '!ants-text-accent-4': deviceType !== value,
                  })}
                  icon={
                    <Icon type={value === DEVICE_TYPE.DESKTOP.value ? 'icon-ants-laptop' : 'icon-ants-smart-phone'} />
                  }
                  onClick={() => {
                    dispatch(setDeviceType(value));
                  }}
                />
              </Tooltip>
            );
          })}
        </Flex>

        <Divider type="vertical" dot height={28} />

        <Flex align="center" gap={10}>
          <Button icon={<Icon type="icon-ants-view" />} onClick={() => handleSaveMediaTemplate(true)}>
            {t(translations.preview.title)}
          </Button>

          <Flex align="center" gap={5}>
            <Switch
              checked={globalSettings.thumbnailCapture}
              onChange={checked =>
                onChangeGlobalSettings({
                  thumbnailCapture: checked,
                })
              }
            />
            <Typography.Text>{t(translations.thumbnailCapture.title)}</Typography.Text>
            <Tooltip title={t(translations.thumbnailCapture.tooltip)}>
              <Icon
                type="icon-ants-info-outline"
                size={16}
                className="ants-m-[2px]"
                style={{ color: globalToken?.colorPrimary, cursor: 'pointer' }}
              />
            </Tooltip>
          </Flex>
        </Flex>

        {/* <div className="ants-flex ants-justify-between ants-items-center">
          <Switch
            className="!ants-mr-2"
            checked={globalSettings.thumbnailCapture}
            onChange={checked =>
              onChangeGlobalSettings({
                thumbnailCapture: checked,
              })
            }
          />
          <div className="ants-flex ants-items-center">
            <Text className="!ants-mr-[5px]">{t(translations.thumbnailCapture.title)}</Text>
            <Tooltip title={t(translations.thumbnailCapture.tooltip)}>
              <Icon type="icon-ants-info" size={12} style={{ color: '#666', cursor: 'pointer' }} />
            </Tooltip>
          </div>
        </div> */}

        {/* If Template Editor is Embed */}
        {isEmbed && (
          <>
            <Divider type="vertical" dot height={28} />
            <Space size={10}>
              {/* <Spin
                spinning={isSavingTemplate}
                indicator={<LoadingOutlined className="ants-text-primary" style={{ fontSize: 18 }} spin />}
              > */}
              {dropdownOnSave?.length ? (
                <Dropdown.Button
                  icon={<Icon type="icon-ants-expand-more" size={20} />}
                  menu={{
                    items: dropdownOnSave,
                    style: { width: 150 },
                  }}
                  disabled={isLoadingSaveAsGalleryPermissionEmails}
                  trigger={['click']}
                  onClick={() => handleTogglePopupObjective(true)}
                  type="primary"
                >
                  {saveText}
                </Dropdown.Button>
              ) : (
                <Button type="primary" onClick={() => handleTogglePopupObjective(true)}>
                  {saveText}
                </Button>
              )}
              {/* </Spin> */}
              {typeof props.onClickCancel === 'function' && (
                <Button type="default" onClick={props.onClickCancel}>
                  {t(translations.cancel.title, 'Cancel')}
                </Button>
              )}
            </Space>
          </>
        )}
      </div>
      <TemplateSaveAsModal
        open={isOpenSaveTemplate}
        destroyOnClose
        okButtonProps={{ loading: loadingPersistTemplate || isLoadingUploadThumbnail }}
        cancelButtonProps={{ disabled: loadingPersistTemplate || isLoadingUploadThumbnail }}
        onCancel={() => {
          !loadingPersistTemplate && handleCancel();
          setTempThumbnails(
            workspace.viewPages.flatMap(item => (item.settings.isActive ? item.thumbnail : [])).filter(Boolean),
          );
        }}
        onOk={(e, value) => value && handleUpdateTemplate(value)}
        templateProps={{
          form,
          value: templateValue,
          onChange: onChangeTemplateValue,
          onEvent: event => {
            if (event.templateName && event.templateName.id) setSelectedTemplate(event.templateName.id.toString());
          },
          imageReview: {
            thumbnails: tempThumbnails,
            skeleton: true,
          },
          onSaveThumbnail(data) {
            const { thumbnails } = data;

            setTempThumbnails(thumbnails);
          },
          templateNames: templateItems,
          categories: categoryItems,
          omitCategories: ['device_type', 'template_type'],
          shareAccess: {
            getUserInfo: email => permissionServices.getUserInfo(email, userInfo?.user_id!) as any,
            show: true,
            userId: +userInfo?.user_id!,
            userPermission: {
              edit: MENU_PERMISSION.CREATED_BY_USER,
              view: MENU_PERMISSION.CREATED_BY_USER,
            },
            placeholder: 'Add people',
            generalAccessSettings: {
              publicOnlyWith: true,
            },
            allowAddUser: true,
          },
          templateNamesOptions: {
            ...searchNameProps,
          },
        }}
      />
    </ToolbarWrapper>
  );
};

Toolbar.defaultProps = {
  saveText: getTranslateMessage(translations.save.title, 'Save'),
  showCloneButton: true,
};

const ToolbarWrapper = styled.div`
  ${tw`ants-relative ants-flex ants-flex-shrink-0 ants-items-center ants-justify-between ants-w-full ants-h-50px ants-px-15px ants-bg-background ants-shadow-cus-sm ants-z-[390]`}

  .__left {
    ${tw`ants-flex ants-items-center`}
  }

  .__right {
    ${tw`ants-flex ants-items-center`}
  }
`;
