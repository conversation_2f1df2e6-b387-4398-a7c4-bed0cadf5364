// Libraries
import React, { useEffect, useState } from 'react';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

// Molecules
import { Collapse, Panel } from 'app/components/molecules/Collapse';
import MobileWarning from '../../../molecules/MobileWarning';

// Organisms
import { AddDynamicLink } from '../../AddDynamicLink';
import { AddDynamicContent } from '../../AddDynamicContent';
import { DynamicContent } from '../../DynamicContent';
import { TextStyling } from '../../TextStyling';
import { DisplayCondition } from '../../DisplayCondition';

// Styled
import { TextWrapper } from './styled';

// Selectors
import {
  selectBlockSelected,
  selectContentSources,
  selectJourneySettings,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Hooks
import { useUpdateEffect } from 'app/hooks';

// Types
import {
  TStyleBlockSetting,
  TStylesSettings,
  TTextStyling,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { handleError } from 'app/utils/handleError';

// translate
import { translations } from 'locales/translations';

// Utils
import { waitForElement } from 'app/utils/dom';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { BlockStyling } from '../Common/BlockStyling';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

interface TextProps {}

export const Text: React.FC<TextProps> = props => {
  // hook
  const dispatch = useDispatch();
  const { t } = useTranslation();

  // States
  const [modalAddPersonalizationVisible, setModalAddPersonalizationVisible] = useState<boolean>(false);
  const [modalAddPersonalizationDefaultData, setModalAddPersonalizationDefaultData] = useState<Record<string, any>>({});

  const [modalInsertLinkVisible, setModalInsertLinkVisible] = useState<boolean>(false);
  const [modalInsertLinkDefaultData, setModalInsertLinkDefaultData] = useState<Record<string, any>>({});

  // Selector
  const { activePanel } = useSelector(selectSidePanel);
  const blockSelected = useSelector(selectBlockSelected);
  const journeySettings = useSelector(selectJourneySettings);
  const contentSources = useSelector(selectContentSources);

  const id = blockSelected?.id || '';
  const blockSettings = blockSelected?.settings as {
    blockStylesSettings: TStylesSettings;
    blockStyles: React.CSSProperties;
    dynamic: {
      selectedId: string;
      data: Record<string, any>;
    };
    link: {
      selectedId: string;
      data: Record<string, Record<string, any>>;
    };
    defaultDynamicIndex?: number;
    textStyling: TTextStyling;
    blockHoverStyles: React.CSSProperties;
    blockHoverStylesSettings: TStyleBlockSetting;
  };
  const { selectedId: selectedDataDynamicId = '', data: dataDynamic = {} } = blockSettings?.dynamic || {};
  const { selectedId: selectedLinkId = '', data: dataLink = {} } = blockSettings?.link || {};

  // Actions
  const { setSidePanel, updateBlockSetting, updateBlockText } = mediaTemplateDesignActions;

  const {
    displayCondition = {
      condition: '',
      field: '',
      index: 1,
      operator: '',
      dataType: '',
      value: '',
    },
  } = blockSettings.blockStylesSettings;

  const onChangeDisplayCondition = (settings = {}) => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              draft.blockStylesSettings.displayCondition = {
                ...draft.blockStylesSettings.displayCondition,
                ...settings,
              };
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  // Effects

  // Init default side panel expand
  useEffect(() => {
    dispatch(
      setSidePanel({
        activePanel: SIDE_PANEL_COLLAPSE.DISPLAY_CONDITION,
      }),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Change Add Personalization modal visible when a Dynamic Text creates/updates/cancel, Delete dynamic text node
  useEffect(() => {
    // Only show modal Add Personalization when selected dynamic ID is not a cancel/remove ID
    if (blockSelected && selectedDataDynamicId) {
      const [dataDynamicId, action] = selectedDataDynamicId.split(':');

      if (['create', 'edit'].includes(action)) {
        setModalAddPersonalizationDefaultData(dataDynamic[dataDynamicId]);
        setModalAddPersonalizationVisible(true);
      }
    }
  }, [blockSelected, selectedDataDynamicId, dataDynamic]);

  // Change Insert Link modal visible when a Link creates/updates/cancel, Delete link node, Unlink
  useEffect(() => {
    // Only show modal Insert Link when selected link ID is not a cancel/remove ID
    if (blockSelected && selectedLinkId) {
      const [dataLinkId, action] = selectedLinkId.split(':');

      if (['create', 'edit'].includes(action)) {
        setModalInsertLinkDefaultData(dataLink[dataLinkId]);
        setModalInsertLinkVisible(true);
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blockSelected, selectedLinkId, dataLink]);

  // Open Personalization Panel whenever AddPersonalization/AddInsertLink modal is closes
  useUpdateEffect(() => {
    if (!modalAddPersonalizationVisible || !modalInsertLinkVisible) {
      waitForElement(`#root #text-editor-${id}__bound .fr-element.fr-view`).then(node => {
        // Place the cursor at the end of the input text
        let range = new Range();
        range.setStartAfter((node as Node).lastChild as Node);
        range.setEndAfter((node as Node).lastChild as Node);

        let selection = window?.getSelection();
        selection?.removeAllRanges();
        selection?.addRange(range);
        selection?.collapseToEnd();

        dispatch(
          setSidePanel({
            activePanel: SIDE_PANEL_COLLAPSE.DYNAMIC_CONTENT,
          }),
        );
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalAddPersonalizationVisible, modalInsertLinkVisible]);

  const handleModalAddPersonalizationCancelButtonClick = () => {
    if (blockSelected) {
      const [dataDynamicId] = selectedDataDynamicId.split(':');

      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.dynamic.selectedId',
              data: `${dataDynamicId}:cancel`,
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }

    setModalAddPersonalizationVisible(false);
    setModalAddPersonalizationDefaultData({});
  };
  const handleModalAddPersonalizationOkButtonClick = (values: Record<string, any>) => {
    if (blockSelected) {
      const [dataDynamicId] = selectedDataDynamicId.split(':');

      // Update data dynamic settings
      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.dynamic.selectedId',
              data: dataDynamicId,
            },
            {
              fieldPath: 'settings.dynamic.data',
              data: produce(dataDynamic, draft => {
                draft[dataDynamicId] = {
                  ...values,
                  mappingKey: `${id}-${dataDynamicId}`,
                };
              }),
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }

    setModalAddPersonalizationVisible(false);
    setModalAddPersonalizationDefaultData({});
  };

  const handleModalInsertLinkCancelButtonClick = () => {
    if (blockSelected) {
      const [dataLinkId] = selectedLinkId.split(':');

      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.link.selectedId',
              data: `${dataLinkId}:cancel`,
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }

    setModalInsertLinkVisible(false);
  };
  const handleModalInsertLinkOkButtonClick = (values: Record<string, any>) => {
    if (blockSelected) {
      const [dataLinkId] = selectedLinkId.split(':');

      // Update data dynamic settings
      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.link.selectedId',
              data: dataLinkId,
            },
            {
              fieldPath: 'settings.link.data',
              data: produce(dataLink, draft => {
                draft[dataLinkId] = values;
              }),
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }

    setModalInsertLinkVisible(false);
  };

  const handleOnClickPanel = activePanel => {
    dispatch(
      setSidePanel({
        activePanel: activePanel,
      }),
    );
  };

  return (
    <TextWrapper>
      <MobileWarning />

      <Collapse
        accordion
        activeKey={activePanel}
        defaultActiveKey={SIDE_PANEL_COLLAPSE.DISPLAY_CONDITION}
        onChange={handleOnClickPanel}
      >
        <Panel key={SIDE_PANEL_COLLAPSE.DISPLAY_CONDITION} header={t(translations.displayCondition.title)}>
          <DisplayCondition
            displayCondition={displayCondition}
            valueCondition={displayCondition.condition}
            onChange={onChangeDisplayCondition}
          />
        </Panel>
        <Panel key={SIDE_PANEL_COLLAPSE.DYNAMIC_CONTENT} header={t(translations.dynamicContent.sidePanel.title)}>
          <div className="ants-flex ants-flex-col ants-space-y-5">
            <DynamicContent />
          </div>
        </Panel>
        <Panel key={SIDE_PANEL_COLLAPSE.TEXT_STYLING} header={t(translations.textStyling.sidePanel.title)}>
          <div className="ants-flex ants-flex-col ants-space-y-5">
            <TextStyling />
          </div>
        </Panel>
        <Panel key={SIDE_PANEL_COLLAPSE.CONTAINER_STYLING} header={t(translations.containerStyle.title)}>
          <div className="ants-flex ants-flex-col ants-space-y-5">
            <BlockStyling />
          </div>
        </Panel>
      </Collapse>

      <AddDynamicLink
        defaultDynamicIndex={blockSettings.defaultDynamicIndex}
        defaultData={modalInsertLinkDefaultData}
        visible={modalInsertLinkVisible}
        onCancel={handleModalInsertLinkCancelButtonClick}
        onOk={handleModalInsertLinkOkButtonClick}
      />

      <AddDynamicContent
        journeySettings={journeySettings}
        contentSources={contentSources}
        defaultDynamicIndex={blockSettings.defaultDynamicIndex}
        defaultData={modalAddPersonalizationDefaultData}
        visible={modalAddPersonalizationVisible}
        onCancel={handleModalAddPersonalizationCancelButtonClick}
        onOk={handleModalAddPersonalizationOkButtonClick}
        isShowCustomFunction
      />
    </TextWrapper>
  );
};
