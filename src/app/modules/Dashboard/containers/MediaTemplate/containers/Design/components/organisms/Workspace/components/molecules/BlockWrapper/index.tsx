// Libraries
import React, { memo, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createPortal } from 'react-dom';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import mergeWith from 'lodash/mergeWith';
import { useDrag } from 'react-dnd';

// Icons
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Atoms
import { Alert, Button, Input, Text, TextArea } from 'app/components/atoms';

// Molecules
import { Form, Modal } from 'app/components/molecules';
import { SetIndexButton } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SetIndexButton';
import { BlockDropArea } from '../BlockDropArea';

// Styled
import { ContentBlock, HeaderBlock, HeaderButton, StyledBlockWrapper } from './styled';

// Select
import {
  selectCurrentBlockErrors,
  selectCurrentBlocks,
  selectCurrentBlockWarnings,
  selectCurrentTree,
  selectIsDraggingBlock,
  selectIsShowErrorAlert,
  selectIsShowWarningAlert,
  selectNamespace,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Hooks
import { useDebounce } from 'app/hooks';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Constants
import {
  BLOCK_ITEM,
  SIDE_PANEL_TYPE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { FORM_VALIDATE } from 'constants/formValidate';

// Utils
import { handleError } from 'app/utils/handleError';

// Types
import { TSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Translations
import { translations } from 'locales/translations';

// Utils
import { generateBlockContentStyle } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/utils';
import {
  getAllChildrenBlockId,
  getCloneBlocks,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';

// Constants
import { SIDE_PANEL_TABS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/constants';

// Types
import { TSlide } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

// Queries
import { useAddSavedBlock, useGetSavedBlocks, useUpdateSavedBlock } from 'app/queries/SavedBlock';
import {
  CopyDuplicateIcon,
  DeleteRemoveTrashIcon,
  DoubleTreeDotIcon,
  SaveIcon,
  SettingFillIcon,
} from '@antscorp/antsomi-ui/es/components/icons';

const { COLUMN, GROUP, SLIDE_SHOW } = STANDARDS_BLOCKS;

interface BlockProps {
  type: string;
  idx: number;
  parentId?: string;
  id: string;
  draggableId: string;
  isDragDisabled?: boolean;
  isPreviewMode?: boolean;
  savedBlockId?: number;
  settings: TSettings;
  isPortal?: boolean;
  slides?: TSlide[];
  children?: React.ReactNode;
  blockWrapperStyle?: React.CSSProperties;
  displayConditionData?: Record<string, any>;
}

interface SavedBlockModalProps {
  savedBlock: any;
  saveBlockModal: any;
  setSaveBlockModal: any;
  checkSavedBlockModal: any;
  savedBlocks: any;
  setCheckSavedBlockModal: any;
  blockLabel: string;
  parentId: string;
  blockId: string;
  blockType: string;
  blockSettings: TSettings;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/molecules/BlockWrapper/index.tsx';

const HEADER_WIDTH = 159;

export const SavedBlockModal: React.FC<SavedBlockModalProps> = memo(props => {
  // Props
  const {
    blockId,
    blockType,
    blockLabel,
    saveBlockModal,
    setSaveBlockModal,
    setCheckSavedBlockModal,
    checkSavedBlockModal,
    savedBlocks,
    savedBlock,
    blockSettings,
  } = props;

  // Selectors
  const currentBlocks = useSelector(selectCurrentBlocks);
  const currentTree = useSelector(selectCurrentTree);

  // Form
  const [form] = Form.useForm();

  // I18n
  const { t } = useTranslation();

  // use hooks queries
  const { mutate: addSavedBlock } = useAddSavedBlock();
  const { mutate: updateSavedBlock } = useUpdateSavedBlock();

  // Handlers
  const onCancelModal = () => {
    try {
      setSaveBlockModal(state => ({
        ...state,
        isVisible: false,
      }));

      form.resetFields();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancelModal',
        args: {},
      });
    }
  };

  const onCancelCheckSavedModal = () => {
    try {
      setCheckSavedBlockModal(state => ({
        ...state,
        isVisible: false,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancelCheckSavedModal',
        args: {},
      });
    }
  };

  const onClickSaveAsNew = () => {
    try {
      setCheckSavedBlockModal(state => ({
        ...state,
        isSaveAsNew: true,
        isVisible: false,
      }));

      setSaveBlockModal(state => ({
        ...state,
        isVisible: true,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSaveAsNew',
        args: {},
      });
    }
  };

  const onClickReplace = () => {
    try {
      setCheckSavedBlockModal(state => ({
        ...state,
        isVisible: false,
        isSaveAsNew: false,
      }));

      setSaveBlockModal(state => ({
        ...state,
        isVisible: true,
      }));
      setTimeout(() => {
        form.setFieldsValue({
          blockName: savedBlock?.name,
          blockNotes: savedBlock?.notes,
        });
      }, 500);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickReplace',
        args: {},
      });
    }
  };

  const validateBlockName = (_: any, value: any) => {
    const allBlockNames = savedBlocks?.map(({ name }) => name);

    if (checkSavedBlockModal.isSaveAsNew) {
      if (allBlockNames?.some(name => name.trim() === value.trim())) {
        return Promise.reject(new Error(FORM_VALIDATE.DUPLICATED.message()));
      }
    } else {
      if (savedBlock?.name !== value.trim()) {
        if (allBlockNames?.some(name => name.trim() === value.trim())) {
          return Promise.reject(new Error(FORM_VALIDATE.DUPLICATED.message()));
        }
      }
    }
    return Promise.resolve();
  };

  return (
    <>
      <Modal
        visible={saveBlockModal.isVisible}
        loading={saveBlockModal.isLoading}
        destroyOnClose
        title={t(translations[checkSavedBlockModal.isSaveAsNew ? 'saveNewBlock' : 'replaceExistingBlock'].title, {
          name: blockLabel,
        })}
        onCancel={onCancelModal}
        onOk={() => form.submit()}
        // okButtonProps={{disabled:}}
      >
        {checkSavedBlockModal.isSaveAsNew ? (
          <Text>{t(translations.saveNewBlock.description)}</Text>
        ) : (
          <Alert message={t(translations.replaceExistingBlock.warning)} type="warning" showIcon />
        )}
        <Form
          form={form}
          className="!ants-mt-5"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          initialValues={{
            blockName: '',
            blockNotes: '',
          }}
          onFinish={async (values: any) => {
            const { blockName = '', blockNotes = '' } = values;
            const children = {
              blockParentId: blockId,
              blocks: {},
              tree: {},
            };

            if (currentTree[blockId]?.length) {
              currentTree[blockId].forEach(childId => {
                const { childrenBlocks, childrenTree } = getCloneBlocks({
                  parentId: blockId,
                  blockId: childId,
                  blocks: currentBlocks,
                  tree: currentTree,
                });

                Object.assign(children.blocks, childrenBlocks);

                mergeWith(children.tree, childrenTree, (objValue, srcValue) => {
                  if (Array.isArray(objValue)) {
                    return objValue.concat(srcValue);
                  }
                });
              });
            }

            try {
              setSaveBlockModal(state => ({
                ...state,
                isLoading: true,
              }));

              if (checkSavedBlockModal.isSaveAsNew) {
                await addSavedBlock({
                  name: blockName.trim(),
                  type: blockType,
                  notes: blockNotes.trim(),
                  settings: blockSettings,
                  children,
                });
              } else {
                await updateSavedBlock({
                  id: savedBlock?.id,
                  name: blockName.trim(),
                  notes: blockNotes.trim(),
                  settings: blockSettings,
                  children,
                });
              }
            } catch (error) {
              handleError(error, {
                path: PATH,
                name: '',
                args: { values },
              });
            } finally {
              setSaveBlockModal(state => ({
                ...state,
                isLoading: false,
                isVisible: false,
              }));
            }
          }}
        >
          <Form.Item
            label={t(translations.enterNameBlock.title)}
            name="blockName"
            rules={[
              { required: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
              {
                max: 70,
                message: FORM_VALIDATE.MAX.message('Block name', 70),
              },
              { whitespace: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
              {
                validator: validateBlockName,
              },
            ]}
          >
            <Input placeholder={t(translations.enterNameBlock.placeholder)} />
          </Form.Item>
          <Form.Item
            label={t(translations.addNote.title)}
            name="blockNotes"
            rules={[
              {
                max: 500,
                message: FORM_VALIDATE.MAX.message('Block Notes', 500),
              },
            ]}
          >
            <TextArea placeholder={t(translations.addNote.placeholder)} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        visible={checkSavedBlockModal.isVisible}
        destroyOnClose
        centered
        title={t(translations.replaceOrCreateNewBlock.title, { name: blockLabel })}
        onCancel={onCancelCheckSavedModal}
        onOk={() => form.submit()}
        footer={
          <>
            <Button type="primary" onClick={onClickSaveAsNew}>
              {t(translations.saveAsNew.title)}
            </Button>
            <Button type="primary" onClick={onClickReplace}>
              {t(translations.replaceExisting.title)}
            </Button>
            <Button onClick={onCancelCheckSavedModal}>{t(translations.cancel.title)}</Button>
          </>
        }
      >
        <Alert
          message={t(translations.replaceOrCreateNewBlock.warning)}
          type="warning"
          showIcon
          className="ants-mb-2.5"
        />
        <Text>{t(translations.replaceOrCreateNewBlock.description)}</Text>
      </Modal>
    </>
  );
});

export const BlockWrapper: React.FC<BlockProps> = props => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  const contentBlockRef = useRef<HTMLHeadingElement>(null);
  const headerBlockRef = useRef<HTMLHeadingElement>(null);

  // Props
  const {
    type,
    idx,
    id,
    parentId,
    settings,
    isPreviewMode,
    savedBlockId,
    children,
    blockWrapperStyle,
    displayConditionData,
  } = props;

  const isGamification =
    [STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.name, STANDARDS_BLOCKS.SHAKE_AND_WIN.name].includes(settings?.type) ||
    false;

  const { setSidePanel, cloneBlock, removeBlock, setDraggingBlock } = mediaTemplateDesignActions;

  const boundingBlockClientRect = contentBlockRef?.current?.getBoundingClientRect();
  const boundingBemWrapperClientRect = document.querySelector('#bem-tools')?.getBoundingClientRect();

  // Selector
  const isDraggingBlock = useSelector(selectIsDraggingBlock);

  const [{ isDraggingCurrent }, drag, previewDrag] = useDrag(() => ({
    type: BLOCK_ITEM,
    collect: monitor => {
      return {
        isDraggingCurrent: !!monitor.isDragging(),
      };
    },
    canDrag() {
      dispatch(setDraggingBlock(true));

      setTimeout(() => {
        const el = document.querySelector(`#block-wrapper-${id}`);

        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 250);

      return true;
    },
    end() {
      setTimeout(() => {
        dispatch(setDraggingBlock(false));
      }, 500);
    },
    item: {
      isAddBlock: false,
      blockId: id,
      blockType: type,
      index: idx,
    },
  }));

  // Debounce
  // Use debounce for force update block client rect
  useDebounce(boundingBlockClientRect, 500);

  // Selector
  const sidePanel = useSelector(selectSidePanel);
  const namespace = useSelector(selectNamespace);
  const tree = useSelector(selectCurrentTree);
  const currentBlockErrors = useSelector(selectCurrentBlockErrors);
  const currentBlockWarnings = useSelector(selectCurrentBlockWarnings);
  const blockError = currentBlockErrors[id] || [];
  const blockWarning = currentBlockWarnings[id] || [];
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);
  const isShowWarningAlert = useSelector(selectIsShowWarningAlert);

  const { data: savedBlocks } = useGetSavedBlocks();

  // State
  const [isShowHeader, setShowHeader] = useState(false);
  const [checkSavedBlockModal, setCheckSavedBlockModal] = useState({
    isVisible: false,
    isSaveAsNew: true,
  });
  const [saveBlockModal, setSaveBlockModal] = useState({
    isVisible: false,
    blockName: '',
    blockNote: '',
    isLoading: false,
  });

  // Variables
  const isShowError = !!blockError.length && isShowErrorAlert;
  const isShowWarning = !!blockWarning.length && isShowWarningAlert;

  // Memoize
  const positionHeader = useMemo(() => {
    const { top: blockTop = 0, left: blockLeft = 0, width: blockWidth = 0 } = boundingBlockClientRect || {};
    const { top: bemTop = 0, left: bemLeft = 0, width: bemWidth = 0 } = boundingBemWrapperClientRect || {};

    const position: any = {
      top: blockTop - bemTop,
      left: blockLeft - bemLeft,
      right: 'auto',
    };

    // Width of header block is 159
    if (position.left + HEADER_WIDTH > bemWidth) {
      position.right = bemWidth - (position.left + blockWidth);
      position.left = 'auto';
    }

    return position;
  }, [boundingBlockClientRect, boundingBemWrapperClientRect]);

  const savedBlock = useMemo(() => {
    return savedBlocks?.find(({ id }) => id === savedBlockId);
  }, [savedBlockId, savedBlocks]);

  const blockLabel = useMemo(() => {
    const block = Object.values(STANDARDS_BLOCKS).find(({ name }) => type === name);

    return block ? block.label : '';
  }, [type]);

  const isColumnBlock = useMemo(() => {
    return STANDARDS_BLOCKS.COLUMN.name === type;
  }, [type]);

  const isHideHeader = useMemo(() => {
    let isHide = false;

    switch (type) {
      case STANDARDS_BLOCKS.TEXT.name:
        if (sidePanel.blockSelectedId === id) {
          isHide = true;
        }
        break;
      case STANDARDS_BLOCKS.GROUP.name:
      case STANDARDS_BLOCKS.SLIDE_SHOW.name:
        if (sidePanel.blockSelectedId === id) {
          isHide = true;
        }

        const childrenBlockIds = getAllChildrenBlockId({ tree, blockId: id });

        if (childrenBlockIds.some(childId => childId === sidePanel.blockSelectedId)) {
          isHide = true;
        }
        break;
      default:
        break;
    }

    return isHide;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, sidePanel.blockSelectedId, sidePanel.type, type]);

  // Debounce
  // Use debounce for force update block client rect
  useDebounce(positionHeader, 500);

  // Handlers
  const onOpenSettingBlock = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, isClickSetting: boolean) => {
    try {
      e.stopPropagation();

      let sidePanelType = type;

      if (!isClickSetting) {
        switch (type) {
          case SIDE_PANEL_TYPE.COLUMN.name:
            sidePanelType = SIDE_PANEL_TYPE.BLOCKS.name;
            break;

          default:
            sidePanelType = type;
            break;
        }
      }

      dispatch(
        setSidePanel({
          type: sidePanelType,
          blockSelectedId: id,
          activeTab: SIDE_PANEL_TABS.CONTENT.name,
          activePanel: '',
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onOpenSettingBlock',
        args: {},
      });
    }
  };

  const onClickCLoneBlock = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    try {
      e.stopPropagation();

      dispatch(
        cloneBlock({
          parentBlockId: parentId,
          blockId: id,
          blockType: type,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickCLoneBlock',
        args: {},
      });
    }
  };

  const onClickRemoveBlock = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    try {
      e.stopPropagation();

      Modal.confirm({
        title: t(translations.confirmDeletionBlock.title),
        icon: null,
        centered: true,
        content: t(translations.confirmDeletionBlock.description),
        onOk() {
          if (sidePanel.blockSelectedId === id) {
            dispatch(
              setSidePanel({
                blockSelectedId: '',
                type: SIDE_PANEL_TYPE.BLOCKS.name,
              }),
            );
          }

          dispatch(
            removeBlock({
              parentBlockId: parentId,
              blockId: id,
              blockType: type,
            }),
          );
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickRemoveBlock',
        args: {},
      });
    }
  };

  const onClickSaveBlock = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    try {
      e.stopPropagation();

      if (savedBlock) {
        setCheckSavedBlockModal(state => ({
          ...state,
          isVisible: true,
        }));

        return;
      }

      setSaveBlockModal(state => ({
        ...state,
        isVisible: true,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSaveBlock',
        args: {},
      });
    }
  };

  const renderSetIndexButton = () => {
    try {
      switch (type) {
        case STANDARDS_BLOCKS.COLUMN.name:
        case STANDARDS_BLOCKS.SLIDE_SHOW.name:
          return (
            <HeaderButton>
              <SetIndexButton blockId={id} />
            </HeaderButton>
          );

        default:
          return null;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderSetIndexIcon',
        args: {},
      });
    }
  };

  const renderContentBlock = (element: React.ReactNode) => {
    if (isPreviewMode) {
      return element;
    }

    return (
      <BlockDropArea blockType={type} blockId={id} idx={idx}>
        {element}
      </BlockDropArea>
    );
  };

  const renderBlockWrapper = () => {
    try {
      return (
        <StyledBlockWrapper
          id={`block-wrapper-${id}`}
          ref={ref => {
            (contentBlockRef as any).current = ref;
          }}
          className={classNames(isColumnBlock ? '--outside' : '--inside', {
            '--invisible': (isDraggingBlock && !blockError.length) || isPreviewMode,
            '!ants-border-none': isPreviewMode,
            '!ants-z-[120]':
              !isPreviewMode && (sidePanel.blockSelectedId === id || isShowHeader || isShowError || isShowWarning),
            '!ants-pointer-events-none !ants-opacity-50': isDraggingCurrent,
          })}
          isSelected={sidePanel.blockSelectedId === id || isShowHeader}
          isError={isShowError}
          onMouseOver={() => setShowHeader(true)}
          onMouseLeave={() => setShowHeader(false)}
          onClick={e => onOpenSettingBlock(e, false)}
          style={{
            ...(settings?.blockStylesSettings?.hidden && { display: 'none' }),
            ...blockWrapperStyle,
          }}
          {...displayConditionData}
        >
          {!!blockError.length && !isPreviewMode && isShowErrorAlert ? (
            <div className="ants-absolute ants-top-3px ants-right-3px ants-z-50">
              <ErrorIcon
                style={{
                  width: 14,
                  height: 14,
                }}
              />
            </div>
          ) : !!blockWarning.length && !isPreviewMode && isShowWarningAlert ? (
            <div className="ants-absolute ants-top-3px ants-right-3px ants-z-50">
              <WarningIcon
                style={{
                  width: 18,
                  height: 14,
                }}
              />
            </div>
          ) : null}

          {createPortal(
            <HeaderBlock
              ref={headerBlockRef}
              className={classNames(isColumnBlock ? '--outside' : '--inside', {
                'header-block': true,
                '!ants-hidden': isPreviewMode || isHideHeader || !isShowHeader,
                '!ants-opacity-0': isDraggingBlock,
                '!ants-flex-row-reverse': positionHeader.right !== 'auto',
              })}
              style={{
                ...positionHeader,
                ...([GROUP.name, SLIDE_SHOW.name, COLUMN.name].includes(type) && { zIndex: 9999 }),
              }}
            >
              <HeaderButton ref={drag}>
                <DoubleTreeDotIcon size={19} />
              </HeaderButton>
              <HeaderButton onClick={e => onOpenSettingBlock(e, true)}>
                <SettingFillIcon size={19} />
              </HeaderButton>

              {renderSetIndexButton()}

              {!isColumnBlock && (
                <HeaderButton onClick={e => onClickSaveBlock(e)}>
                  <SaveIcon size={18} />
                </HeaderButton>
              )}
              {![STANDARDS_BLOCKS.OPTIN_FIELDS.name].includes(type) && (
                <HeaderButton onClick={onClickCLoneBlock} disabled={isGamification}>
                  <CopyDuplicateIcon size={16} />
                </HeaderButton>
              )}
              <HeaderButton onClick={onClickRemoveBlock}>
                <DeleteRemoveTrashIcon size={20} />
              </HeaderButton>
            </HeaderBlock>,
            document.querySelector('#bem-tools') || document.body,
          )}

          {renderContentBlock(
            <ContentBlock
              ref={previewDrag}
              id={
                !!settings.blockStylesSettings.customId
                  ? settings.blockStylesSettings.customId
                  : `${namespace}-${settings.component || ''}--wrapper--${id}`
              }
              className={classNames(settings.blockStylesSettings.customClass, `content-block content-${id}`, {
                [`${namespace}-${settings.component || ''}--wrapper`]: settings.component,
              })}
              hoverStyle={
                settings.blockHoverStylesSettings?.editHover
                  ? {
                      ...generateBlockContentStyle(settings.blockHoverStyles || {}),
                      zIndex: !!sidePanel.blockSelectedId ? 'unset' : settings.blockHoverStyles?.zIndex,
                    }
                  : {}
              }
              style={{
                ...generateBlockContentStyle(settings.blockStyles || {}),
                zIndex: !!sidePanel.blockSelectedId && !isPreviewMode ? 'unset' : settings.blockStyles?.zIndex,
              }}
            >
              {children}
            </ContentBlock>,
          )}
        </StyledBlockWrapper>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  return (
    <>
      {renderBlockWrapper()}

      <SavedBlockModal
        parentId={id}
        blockId={id}
        blockType={type}
        blockSettings={settings}
        blockLabel={blockLabel}
        saveBlockModal={saveBlockModal}
        checkSavedBlockModal={checkSavedBlockModal}
        savedBlocks={savedBlocks}
        savedBlock={savedBlock}
        setCheckSavedBlockModal={setCheckSavedBlockModal}
        setSaveBlockModal={setSaveBlockModal}
      />
    </>
  );
};

BlockWrapper.defaultProps = {
  isDragDisabled: false,
  isPreviewMode: false,
};

export default BlockWrapper;
