// Libraries
import React, { useRef, useState } from 'react';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';
import get from 'lodash/get';
import { NumberSize } from 're-resizable';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from 'app/hooks';

// Atoms
import { Tooltip } from 'app/components/atoms';

// Molecules
import BlockWrapper from '../../molecules/BlockWrapper';
import { DragBlockHere } from '../../molecules/DragBlockHere';

// Organisms
import {
  GroupBlock,
  TextBlock,
  ImageBlock,
  ButtonBlock,
  CountdownBlock,
  CouponWheelBlock,
  DividerBlock,
  HtmlBlock,
  IconsBlock,
  OptinFieldsBlock,
  SpacerBlock,
  VideoBlock,
  YesNoBlock,
  RatingBlock,
  SlideShowBlock,
  TableBlock,
  ShakeAndWinBlock,
  // Export
  TableBlockExport,
  SurpriseTreasureHuntBlock,
  OTPVerificationBlock,
} from '..';

// Styled
import {
  ColumnBlockContent,
  ColumnBlockSetting,
  ColumnBlockWrapper,
  ColumnsBlockWrapper,
  HandleResize,
} from './styled';

// Types
import { BlockProps, TSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Selectors
import {
  selectBlockById,
  selectChildrenBlockById,
  selectDeviceType,
  selectIsDraggingBlock,
  selectIsMobileMode,
  selectNamespace,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Utils
import { handleError } from 'app/utils/handleError';
import {
  generateBlockContentStyle,
  getDisplayConditionIndex,
  MAP_DISPLAY_CONDITION,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/utils';

// Constants
import {
  PREFIX_EL_NAME,
  SIDE_PANEL_TYPE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { DEVICE_TYPE } from 'app/modules/Dashboard/containers/MediaTemplate/constants';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/index';
import { TDisplayCondition } from '../../../../SidePanel/components/organisms/DisplayCondition/types';
import { buildDisplayConditionData } from '../../../../SidePanel/components/organisms/DisplayCondition/utils';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/molecules/ColumnsBlock/index.tsx';

interface ColumnsBlockProps extends BlockProps {
  isPreviewMode?: boolean;
}

interface ColumnBlockProps {
  parentId: string;
  id: string;
  idx: number;
  settings: TSettings;
  isPreviewMode?: boolean;
  columnsLength?: number;
  maxWidth?: number;
  width: number;
  columnWidths: number[];
  displayCondition?: TDisplayCondition;
  notStackOnMobile?: boolean;
  onResize?: any;
  onResizeStop?: any;
}

export const ColumnBlock: React.FC<ColumnBlockProps> = props => {
  const dispatch = useDispatch();

  // Props
  const {
    parentId,
    idx,
    settings,
    columnsLength,
    columnWidths,
    width,
    id: columnId,
    isPreviewMode,
    displayCondition = {},
    notStackOnMobile,
  } = props;

  // Selectors
  const isMobileMode = useSelector(selectIsMobileMode);
  const sidePanel = useSelector(selectSidePanel);
  const isDraggingBlock = useSelector(selectIsDraggingBlock);
  const namespace = useSelector(selectNamespace);
  const elements = useSelector(selectChildrenBlockById(columnId));
  const isEmpty = !elements.length;

  // Actions
  const { setSidePanel } = mediaTemplateDesignActions;

  // Refs
  const originWidth = useRef(width || 0);
  const nextOriginWidth = useRef(0);
  const columnResizeIdx = useRef(-1);

  // State
  const [isVisibleResizeTooltip, setVisibleResizeTooltip] = useState(false);

  const memoizedDisplayCondition: any = useDeepCompareMemo(() => {
    const displayConditionIndex = getDisplayConditionIndex({
      blocks: elements,
      fieldSelected: displayCondition.attribute?.value,
      isHasNoIndex: true,
    });

    const draftDisplayCondition = {
      ...displayCondition,
      ...(typeof displayConditionIndex === 'boolean' && displayConditionIndex
        ? { isHasNoIndex: displayConditionIndex }
        : { index: displayConditionIndex }),
      condition: MAP_DISPLAY_CONDITION[displayCondition.condition || ''] || '',
    };

    return buildDisplayConditionData(draftDisplayCondition);
  }, [displayCondition, elements]);

  const renderElement = ({ id, parentId, type, settings, savedBlockId, idx }) => {
    try {
      const elementProps = {
        parentId,
        id,
        type,
        settings,
        idx,
        isPreviewMode,
        namespace,
        columnId,
      };

      const settingDC = settings.blockStylesSettings.displayCondition || {};
      const renderContent = () => {
        switch (type) {
          case STANDARDS_BLOCKS.TEXT.name:
            return <TextBlock {...elementProps} />;

          case STANDARDS_BLOCKS.IMAGE.name:
            return <ImageBlock {...elementProps} />;

          case STANDARDS_BLOCKS.BUTTON.name:
            return <ButtonBlock {...elementProps} />;

          case STANDARDS_BLOCKS.COUNT_DOWN.name:
            return <CountdownBlock {...elementProps} />;

          case STANDARDS_BLOCKS.COUPON_WHEEL.name:
            return <CouponWheelBlock {...elementProps} />;

          case STANDARDS_BLOCKS.DIVIDER.name:
            return <DividerBlock {...elementProps} />;

          case STANDARDS_BLOCKS.HTML.name:
            return <HtmlBlock {...elementProps} />;

          case STANDARDS_BLOCKS.ICON.name:
            return <IconsBlock {...elementProps} />;

          case STANDARDS_BLOCKS.OPTIN_FIELDS.name:
            return <OptinFieldsBlock {...elementProps} />;

          case STANDARDS_BLOCKS.SPACER.name:
            return <SpacerBlock {...elementProps} />;

          case STANDARDS_BLOCKS.VIDEO.name:
            return <VideoBlock {...elementProps} />;

          case STANDARDS_BLOCKS.YES_NO.name:
            return <YesNoBlock {...elementProps} />;

          case STANDARDS_BLOCKS.SLIDE_SHOW.name:
            return <SlideShowBlock {...elementProps} />;

          case STANDARDS_BLOCKS.RATING.name:
            return <RatingBlock {...elementProps} />;

          case STANDARDS_BLOCKS.GROUP.name:
            return <GroupBlock {...elementProps} />;

          case STANDARDS_BLOCKS.TABLE.name:
            return isPreviewMode ? <TableBlockExport {...elementProps} /> : <TableBlock {...elementProps} />;
          case STANDARDS_BLOCKS.SHAKE_AND_WIN.name:
            return <ShakeAndWinBlock {...elementProps} />;

          case STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.name:
            return <SurpriseTreasureHuntBlock {...elementProps} />;

          case STANDARDS_BLOCKS.OTP_VERIFICATION.name:
            return <OTPVerificationBlock {...elementProps} />;

          default:
            break;
        }
      };

      return (
        <div
          className={`${namespace}-element ${namespace}-el-${idx + 1} Element ${PREFIX_EL_NAME}-element`}
          {...buildDisplayConditionData(settingDC)}
        >
          <BlockWrapper {...{ ...elementProps, savedBlockId, draggableId: `${type}-${id}` }}>
            {renderContent()}
          </BlockWrapper>
        </div>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  return (
    <ColumnBlockWrapper
      id={`block-wrapper-${columnId}`}
      className={classNames(
        settings.blockStylesSettings.customClass,
        `${namespace}-column ${namespace}-column-${idx + 1} ${PREFIX_EL_NAME}-column Column column__block`,
        {
          'hover:!ants-z-[120]': !isPreviewMode && !isDraggingBlock,
          // '!ants-w-full': isMobileMode && !notStackOnMobile,
          // '--responsive': !notStackOnMobile,
        },
      )}
      data-columnslength={columnsLength}
      handleComponent={{
        right: (
          <Tooltip key={width} title={`${Math.round(width)}%`} placement="left" visible={isVisibleResizeTooltip}>
            <Tooltip
              title={`${Math.round(get(columnWidths, `[${idx + 1}]`, 0))}%`}
              placement="right"
              visible={isVisibleResizeTooltip}
            >
              <HandleResize
                onMouseDown={() => {
                  dispatch(
                    setSidePanel({
                      blockSelectedId: parentId,
                      type: SIDE_PANEL_TYPE.COLUMN.name,
                    }),
                  );
                }}
              />
            </Tooltip>
          </Tooltip>
        ),
      }}
      size={{
        width: `${width}%`,
        height: '100%',
      }}
      // Max with calculate: if have next width => max width equal currentWidth plus nextWidth
      // else max width will equal 100 percent
      maxWidth={`${columnResizeIdx.current === idx ? originWidth.current + nextOriginWidth.current - 8 : 100}%`}
      minWidth={'8%'}
      style={{
        zIndex: isDraggingBlock || !!sidePanel.blockSelectedId ? 'unset' : settings.blockStyles.zIndex,
      }}
      hoverstyle={{
        zIndex:
          isDraggingBlock || !!sidePanel.blockSelectedId || !settings.blockHoverStylesSettings?.editHover
            ? 'unset'
            : settings.blockHoverStyles?.zIndex,
      }}
      enable={{
        left: false,
        right: idx !== (columnsLength || 0) - 1 && !isPreviewMode && (!isMobileMode || notStackOnMobile) ? true : false,
      }}
      onResize={(_even, _direction, _, delta) => props.onResize(delta)}
      onResizeStart={() => {
        originWidth.current = width;
        nextOriginWidth.current = get(columnWidths, `[${idx + 1}]`, 0);
        columnResizeIdx.current = idx;

        setVisibleResizeTooltip(true);
      }}
      onResizeStop={(_even, _direction, _element, delta) => {
        props.onResizeStop(delta);

        nextOriginWidth.current = 0;
        columnResizeIdx.current = -1;

        setVisibleResizeTooltip(false);
      }}
      {...memoizedDisplayCondition}
    >
      <ColumnBlockSetting
        className={`content-block content-${columnId}`}
        style={{
          ...generateBlockContentStyle(settings.blockStyles),
          height: `100%`,
          zIndex: 'unset',
        }}
        hoverStyle={
          settings.blockHoverStylesSettings?.editHover
            ? {
                ...generateBlockContentStyle(settings.blockHoverStyles || {}),
                height: `100%`,
                zIndex: 'unset',
              }
            : {}
        }
      >
        <ColumnBlockContent
          isEmpty={isEmpty}
          className={classNames(`${namespace}-col-content Column__content`, {
            '!ants-border-none': isPreviewMode,
          })}
        >
          {isEmpty
            ? !isPreviewMode && <DragBlockHere blockType={'col'} blockId={columnId} />
            : elements.map((element, idx) => {
                const { id, type, settings, savedBlockId } = element;
                return (
                  <React.Fragment key={id + idx}>
                    {renderElement({ parentId: columnId, id, type, settings, savedBlockId, idx })}
                  </React.Fragment>
                );
              })}
        </ColumnBlockContent>
      </ColumnBlockSetting>
    </ColumnBlockWrapper>
  );
};

export const ColumnsBlock: React.FC<ColumnsBlockProps> = props => {
  const dispatch = useDispatch();

  // Props
  const { id, settings, type, idx, isPreviewMode } = props;
  const { responsiveSettings } = settings;
  const { notStackOnMobile } = responsiveSettings || {};
  // const columns: any = [];

  // Selectors
  const columns = useSelector(selectChildrenBlockById(id));
  const columnsBlock = useSelector(selectBlockById(id));
  const isMobileMode = useSelector(selectIsMobileMode);
  const namespace = useSelector(selectNamespace);

  // // Actions
  const { updateBlockById } = mediaTemplateDesignActions;

  const [columnWidths, setColumnWidths] = useState<number[]>([]);

  useDeepCompareEffect(() => {
    setColumnWidths(columns.map(column => column.settings.width || 0));
  }, [columns]);

  const onResizeColumnBlock = (index: number, delta: NumberSize) => {
    try {
      const viewWidth = document.querySelector('.row__block')?.clientWidth || 1;
      const deltaPercent = (delta.width * 100) / viewWidth;

      const draftColumnWidths = [...columnWidths];

      draftColumnWidths[index] = (columns[index].settings.width || 0) + deltaPercent;

      if (draftColumnWidths[index + 1] !== undefined) {
        if ((columns[index + 1].settings.width || 0) - deltaPercent >= 0) {
          draftColumnWidths[index + 1] = (columns[index + 1].settings.width || 0) - deltaPercent;
        }
      }

      setColumnWidths(draftColumnWidths);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onResizeColumnBlock',
        args: {},
      });
    }
  };

  const onResizeStopColumnBlock = (index: number) => {
    try {
      if (columnsBlock) {
        const currentColumn = columns[index];
        const nextColumn = columns[index + 1];

        dispatch(
          updateBlockById({
            blockId: currentColumn.id,
            dataUpdate: produce(currentColumn, draft => {
              draft.settings.width = columnWidths[index];
            }),
          }),
        );

        if (nextColumn) {
          dispatch(
            updateBlockById({
              blockId: nextColumn.id,
              dataUpdate: produce(nextColumn, draft => {
                draft.settings.width = columnWidths[index + 1];
              }),
            }),
          );
        }
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onResizeStopColumnBlock',
        args: {},
      });
    }
  };

  return (
    <BlockWrapper
      type={type}
      id={id}
      parentId={'root'}
      idx={idx}
      settings={settings}
      isPreviewMode={isPreviewMode}
      draggableId={`row-${id}`}
      isDragDisabled
      displayConditionData={buildDisplayConditionData(settings.blockStylesSettings.displayCondition)}
    >
      <ColumnsBlockWrapper
        className={classNames(`${namespace}-row ${namespace}-row-${idx + 1} ${PREFIX_EL_NAME}-row Row row__block`, {
          'ants-justify-between': settings.blockStylesSettings.columnGap,
          // '--wrap': isMobileMode && !notStackOnMobile,
          // '--no-wrap': notStackOnMobile,
        })}
        style={{
          alignItems: settings?.blockStyles.alignItems,
        }}
      >
        {columns && columns.length
          ? columns.map((column, index) => {
              const { id: columnId, settings: columnSettings } = column;

              if (columnSettings?.blockStylesSettings.hidden) {
                return null;
              }

              return (
                <ColumnBlock
                  key={columnId}
                  idx={index}
                  parentId={id}
                  id={columnId}
                  settings={columnSettings}
                  isPreviewMode={isPreviewMode}
                  width={columnWidths[index]}
                  columnWidths={columnWidths}
                  columnsLength={columns.length}
                  notStackOnMobile={notStackOnMobile}
                  displayCondition={settings.blockStylesSettings.displayCondition || {}}
                  onResize={delta => onResizeColumnBlock(index, delta)}
                  onResizeStop={() => onResizeStopColumnBlock(index)}
                />
              );
            })
          : null}
      </ColumnsBlockWrapper>
    </BlockWrapper>
  );
};

export default ColumnsBlock;
