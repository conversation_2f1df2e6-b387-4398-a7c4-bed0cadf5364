import { createSelector } from '@reduxjs/toolkit';
import { StateWithHistory } from 'app/libs/redux-undo/typings';

import get from 'lodash/get';

import { RootState } from 'types';
import { TBlock } from './types';
import { initialState } from '.';

// Utils
import { getCurrentPageIdx, getBlockSelectedV2, getBlockByIdV2, getBlockResponsiveSettings } from './utils';

// Constants
import { BUSINESS_OBJECT_SETTINGS_DEFAULT, JOURNEY_SETTINGS_DEFAULT } from '../config';
import { DEVICE_TYPE } from '../../../constants';
import { merge, omit, pick, values } from 'lodash';
import { MAP_DEVICE_TYPE_TO_BREAKPOINT } from 'constants/variables';
import { TSettings } from '../types';
import { DESIGN_TEMPLATE_MODE } from '../constants';

const selectSlice = (state: RootState) =>
  (state.mediaTemplateDesign || initialState) as any as StateWithHistory<typeof initialState>;

export const selectMediaTemplateDesign = createSelector([selectSlice], state => state);

export const selectDesignTemplateMode = createSelector([selectSlice], state => state.present.workspace.mode);

export const selectCodeModeSettings = createSelector([selectSlice], state => {
  const currentPageIdx = getCurrentPageIdx(state.present);
  if (state.present.workspace.mode === DESIGN_TEMPLATE_MODE.CODE && currentPageIdx !== -1) {
    return state.present.workspace?.viewPages[currentPageIdx]?.codeModeSettings;
  }

  return {};
});

export const selectToolbar = createSelector([selectSlice], state => state.present.toolbar);

export const selectSidePanel = createSelector([selectSlice], state => state.present.sidePanel);

export const selectLeftSidePanel = createSelector([selectSlice], state => state.present.leftSidePanel);

export const selectSidePanelSettings = createSelector([selectSlice], state => state.present.sidePanel.settings);

export const selectWorkspace = createSelector([selectSlice], state => state.present.workspace);

export const selectBreakpoint = createSelector(
  [selectWorkspace],
  state => MAP_DEVICE_TYPE_TO_BREAKPOINT[state.deviceType],
);

export const selectBreakpointSetting = (blockSettings: Partial<TSettings>) =>
  createSelector([selectBreakpoint], breakpoint => getBlockResponsiveSettings({ breakpoint, settings: blockSettings }));
export const selectIsEmbed = createSelector([selectWorkspace], state => state.isEmbed);

export const selectTemplateId = createSelector([selectSlice], state => state.present.workspace.id);

export const selectDeviceType = createSelector([selectSlice], state => state.present.workspace.deviceType);

export const selectIsMobileMode = createSelector(
  [selectSlice],
  state => state.present.workspace.deviceType === DEVICE_TYPE.MOBILE.value,
);

export const selectIsLoadingWorkspace = createSelector([selectSlice], state => state.present.isLoadingWorkspace);

export const selectIsSavingTemplate = createSelector([selectSlice], state => state.present.isSavingTemplate);

export const selectNamespace = createSelector([selectSlice], state => state.present.workspace.namespace);

export const selectTemplate = createSelector([selectSlice], state => state.present.workspace.template);

export const selectBoSettings = createSelector([selectSlice], state => state.present.workspace.boSettings);

export const selectUtmTracking = createSelector([selectSlice], state => state.present.workspace.utmSettings);

export const selectTrackingModule = createSelector([selectSlice], state => state.present.workspace.trackingModule);

export const selectCategoriesGoal = createSelector([selectSlice], state => state.present.workspace.categories.goal);

export const selectViewPageSelected = createSelector([selectSlice], state => state.present.toolbar.viewPageSelected);

export const selectViewPages = createSelector([selectSlice], state => state.present.workspace.viewPages);

export const selectCurrentViewPage = createSelector([selectSlice], state =>
  state.present.workspace.viewPages.find(viewPage => state.present.toolbar.viewPageSelected === viewPage.id),
);

export const selectViewPage = (viewPageId: string) => {
  return createSelector([selectSlice], state =>
    state.present.workspace.viewPages.find(viewPage => viewPageId === viewPage.id),
  );
};

export const selectIsDraggingBlock = createSelector([selectSlice], state => state.present.isDraggingBlock);

export const selectDraggingBlockId = createSelector([selectSlice], state => state.present.draggingBlockId);

export const selectIsShowErrorAlert = createSelector([selectSlice], state => state.present.isShowErrorAlert);

export const selectIsShowWarningAlert = createSelector([selectSlice], state => state.present.isShowWarningAlert);

export const selectUndoableIndex = createSelector([selectSlice], state => state.index);

export const selectUndoableLimit = createSelector([selectSlice], state => state.limit);

export const selectGlobalSettings = createSelector([selectSlice], state =>
  get(state, 'present.workspace.settings', {}),
);

export const selectBusinessObjectSettings = createSelector([selectSlice], state =>
  get(state, 'present.workspace.boSettings', BUSINESS_OBJECT_SETTINGS_DEFAULT),
);

export const selectListSavedImages = createSelector([selectSlice], state => state.present.data.listSavedImages);

export const selectMediaTemplateTypes = createSelector([selectSlice], state => state.present.data.mediaTemplateTypes);

export const selectListFallbackBO = createSelector([selectSlice], state => state.present.data.listFallbackBO);

export const selectTrackingData = createSelector([selectSlice], state => state.present.data.dataTracking);

export const selectExportInfo = createSelector([selectSlice], state => state.present.exportInfo);

export const selectSaveData = createSelector([selectSlice], state => state.present.saveData);

export const selectContentSources = createSelector([selectSlice], state => state.present.workspace.contentSources);

export const selectContentSourcesGroups = createSelector(
  [selectSlice],
  state => state.present.workspace.contentSources.groups,
);

export const selectBoIdByGroupFromCS = createSelector([selectContentSourcesGroups], state => {
  const result: Record<string, number> = {};

  state.forEach(g => {
    if (g.itemTypeId !== null) {
      result[g.groupId] = g.itemTypeId;
    }
  });

  return result;
});

export const selectCSFallbackByGroupId = createSelector([selectSlice], state => {
  const result: { [key: string]: string } = {};
  const groups = state.present.workspace.contentSources.groups;

  if (groups.length) groups.forEach(g => (result[g.groupId] = g.fallback));

  return result;
});

export const selectCSDataOfGroup = createSelector([selectContentSources], state => {
  const result = new Map<string, Record<string, any>[]>();

  state.groups.forEach(g => result.set(g.groupId, g.data ?? []));

  return {
    data: result,
    isLoading: state.isLoadingDataBO,
  };
});

export const selectPromotionPool = createSelector([selectSlice], state => state.present.data.promotionPool);

export const selectSource = createSelector([selectSlice], state => state.present.data.sources);

export const selectEvent = createSelector([selectSlice], state => state.present.data.events);

export const selectAttribute = createSelector([selectSlice], state => state.present.data.attributes);

// New Version
export const selectAllBlocks = createSelector([selectViewPages], viewPages => {
  type TResult = TBlock & { viewPageId: string };

  const result: TResult[] = [];

  for (const viewPage of viewPages) {
    const { id: viewPageId, blocks } = viewPage || {};

    values(blocks).forEach(block => {
      result.push({
        ...block,
        viewPageId,
      });
    });
  }

  return result;
});
export const selectBlockById = (blockId: string, blockType?: string) =>
  createSelector([selectSlice], state => getBlockByIdV2(blockId, state.present));

export const selectBlockSelected = createSelector([selectSlice], state => getBlockSelectedV2(state.present));

export const selectChildrenBlockById = (blockParentId: string) =>
  createSelector([selectSlice], state => {
    const currentPageIdx = getCurrentPageIdx(state.present);
    const { blocks = {}, tree = {} } = state.present.workspace.viewPages[currentPageIdx];

    return tree[blockParentId]?.map(blockId => blocks[blockId]) || [];
  });

export const selectParentBlockById = (blockId: string) => {
  return createSelector([selectSlice], state => {
    const currentPageIdx = getCurrentPageIdx(state.present);
    const { blocks = {}, tree = {} } = state.present.workspace.viewPages[currentPageIdx];

    const parentBlockId = Object.keys(tree).find(key => tree[key].includes(blockId.toString()));

    return blocks[parentBlockId || -1] || {};
  });
};

export const selectParentBlockSelected = createSelector([selectSlice], state => {
  const currentPageIdx = getCurrentPageIdx(state.present);
  const { blocks = {}, tree = {} } = state.present.workspace.viewPages[currentPageIdx];
  const blockId = state.present.sidePanel.blockSelectedId;

  const parentBlockId = Object.keys(tree).find(key => tree[key].includes(blockId.toString()));

  return blocks[parentBlockId || -1] || {};
});

export const selectJourneySettings = createSelector(
  [selectSlice],
  state => state.present.workspace.journeySettings || JOURNEY_SETTINGS_DEFAULT,
);

export const selectChildrenBlockSelected = createSelector([selectSlice], state => {
  const currentPageIdx = getCurrentPageIdx(state.present);
  const { blocks = {}, tree = {} } = state.present.workspace.viewPages[currentPageIdx];

  return tree[state.present.sidePanel.blockSelectedId]?.map(blockId => blocks[blockId]) || [];
});

export const selectCurrentTree = createSelector(
  [selectSlice],
  state =>
    state.present.workspace.viewPages.find(viewPage => state.present.toolbar.viewPageSelected === viewPage.id)?.tree ||
    {},
);

export const selectCurrentBlocks = createSelector(
  [selectSlice],
  state =>
    state.present.workspace.viewPages.find(viewPage => state.present.toolbar.viewPageSelected === viewPage.id)
      ?.blocks || {},
);

export const selectCurrentBlockErrors = createSelector(
  [selectSlice],
  state =>
    get(state, `present.workspace.errors.viewPages[${state.present.toolbar.viewPageSelected}]`, {}) as Record<
      string,
      any
    >,
);

export const selectCurrentBlockWarnings = createSelector(
  [selectSlice],
  state =>
    get(state, `present.workspace.warnings.viewPages[${state.present.toolbar.viewPageSelected}]`, {}) as Record<
      string,
      any
    >,
);

export const selectWorkspaceErrors = createSelector([selectSlice], state => state.present.workspace.errors);

export const selectWorkspaceWarnings = createSelector([selectSlice], state => state.present.workspace.warnings);
