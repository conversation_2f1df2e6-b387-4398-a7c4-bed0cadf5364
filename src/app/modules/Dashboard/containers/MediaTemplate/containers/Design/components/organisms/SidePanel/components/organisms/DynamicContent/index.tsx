// Libraries
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Tooltip } from 'antd';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Components
import { Button, Icon, Space, Switch } from 'app/components/atoms';
import { Modal } from 'app/components/molecules';

import { SettingWrapper } from '../../molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Selectors
import {
  selectBlockSelected,
  selectBusinessObjectSettings,
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectWorkspaceErrors,
  selectWorkspaceWarnings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Constants
import {
  DYNAMIC_LINK_SETTING_KEY,
  DYNAMIC_LINK_TYPE,
  DYNAMIC_CONTENT_SETTING_KEY,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Icons
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Queries
import { useGetListBO } from 'app/queries/BusinessObject';
import { isCheckAttrArchiveDelete } from '../../molecules/DynamicSetting/constants';

interface DynamicContentProps {}

export const DynamicContent: React.FC<DynamicContentProps> = props => {
  //* Hooks
  const dispatch = useDispatch();
  const { t } = useTranslation();

  //* Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const boSettings = useSelector(selectBusinessObjectSettings) || {};
  const workspaceErrors = useSelector(selectWorkspaceErrors);
  const workspaceWarnings = useSelector(selectWorkspaceWarnings);
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);

  const blockSettings = blockSelected?.settings;
  const blockId = blockSelected?.id || '';

  const { data: dataDynamic = {}, highlight = true } = blockSettings?.dynamic || {};
  const { data: dataLink = {} } = blockSettings?.link || {};
  //* Actions
  const { updateBlockText } = mediaTemplateDesignActions;

  // Queries
  const { data: listBO } = useGetListBO();

  //* States
  const [listData, setListData] = useState<Array<[string, string, string]>>([]);
  const [listLink, setListLink] = useState<Array<[string, string, string]>>([]);

  const errorMessage = isCheckAttrArchiveDelete(listBO, boSettings.itemTypeId);
  //* Effects

  // Format list dynamic data
  useDeepCompareEffect(() => {
    setListData(
      Object.keys({ ...dataDynamic })
        .filter(key => dataDynamic[key] && Object.keys(dataDynamic[key])?.length)
        .filter(key => !dataDynamic[key].hide && dataDynamic[key][DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE])
        .map(key => [
          key,
          dataDynamic[key][DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE].label,
          dataDynamic[key][DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL],
        ]),
    );
  }, [dataDynamic]);

  // Format list dynamic link
  useDeepCompareEffect(() => {
    setListLink(
      Object.keys({ ...dataLink })
        .filter(key => dataLink[key] && Object.keys(dataLink[key])?.length)
        .filter(key => dataLink[key][DYNAMIC_LINK_SETTING_KEY.LINK_TYPE] === DYNAMIC_LINK_TYPE.DYNAMIC)
        .filter(key => !dataLink[key].hide && dataLink[key][DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE])
        .map(key => [
          key,
          dataLink[key][DYNAMIC_LINK_SETTING_KEY.ATTRIBUTE].label,
          dataLink[key][DYNAMIC_LINK_SETTING_KEY.PROMOTION_POOL],
        ]),
    );
  }, [dataLink]);

  //* Handlers

  const handleDeleteDynamicContentVariable = (key: string) => {
    if (blockSelected && key) {
      Modal.confirm({
        centered: true,
        content: t(translations.dynamicContent.modal.message.confirmDeleteVariable),
        title: t(translations.dynamicContent.modal.title.deleteVariable),
        onOk: () => {
          dispatch(
            updateBlockText({
              blockId,
              dataUpdate: [
                {
                  fieldPath: 'settings.dynamic.selectedId',
                  data: `${key}:delete`,
                },
              ],
              ignoreUndoAction: true,
            }),
          );
        },
      });
    }
  };
  const handleEditDynamicContentVariable = (key: string) => {
    if (blockSelected && key) {
      dispatch(
        updateBlockText({
          blockId,
          dataUpdate: [
            {
              fieldPath: 'settings.dynamic.selectedId',
              data: `${key}:edit`,
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }
  };

  const handleDeleteDynamicLink = (key: string) => {
    if (blockSelected && key) {
      Modal.confirm({
        centered: true,
        content: t(translations.dynamicLink.modal.message.confirmDeleteLink),
        title: t(translations.dynamicLink.modal.title.deleteLink),
        onOk: () => {
          dispatch(
            updateBlockText({
              blockId,
              dataUpdate: [
                {
                  fieldPath: 'settings.link.selectedId',
                  data: `${key}:delete`,
                },
              ],
              ignoreUndoAction: true,
            }),
          );
        },
      });
    }
  };
  const handleEditDynamicLink = (key: string) => {
    if (blockSelected && key) {
      dispatch(
        updateBlockText({
          blockId,
          dataUpdate: [
            {
              fieldPath: 'settings.link.selectedId',
              data: `${key}:edit`,
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }
  };

  const handleChangeHighlight = (checked: boolean) => {
    if (blockSelected) {
      dispatch(
        updateBlockText({
          blockId,
          dataUpdate: [
            {
              fieldPath: 'settings.dynamic.highlight',
              data: checked,
            },
          ],
        }),
      );
    }
  };
  return (
    <Space size={20} direction="vertical">
      {listData.map(([key, attribute, pool]) => (
        <Row key={key}>
          {errorMessage && (
            <Col span={3}>
              <Tooltip title={errorMessage}>
                <WarningIcon
                  style={{
                    width: 15,
                    height: 25,
                  }}
                />
              </Tooltip>
            </Col>
          )}

          <Col span={errorMessage ? 21 : 24}>
            <SettingWrapper key={key} label={attribute} labelClassName="ants-max-w-[50%] ants-truncate">
              <Space size={10} direction="horizontal">
                {/* Show Icon Error or Warning */}
                {errors[blockSelected.id] && workspaceErrors?.promotionPool?.[pool] ? (
                  <Tooltip title={workspaceErrors?.promotionPool?.[pool]}>
                    <ErrorIcon />
                  </Tooltip>
                ) : warnings[blockSelected.id] && workspaceWarnings?.promotionPool?.[pool] ? (
                  <Tooltip title={workspaceWarnings?.promotionPool?.[pool]}>
                    <WarningIcon />
                  </Tooltip>
                ) : null}

                <Button
                  className="ants-border-none ants-bg-white hover:ants-bg-button-primary-hover"
                  icon={<Icon type="icon-ants-edit-2" size={20} />}
                  title={t(translations.edit.title)}
                  onClick={_ => handleEditDynamicContentVariable(key)}
                />
                <Button
                  className="ants-border-none ants-bg-white hover:ants-bg-button-primary-hover"
                  icon={<Icon type="icon-ants-remove-trash" size={20} />}
                  title={t(translations.remove.title)}
                  onClick={_ => handleDeleteDynamicContentVariable(key)}
                />
              </Space>
            </SettingWrapper>
          </Col>
        </Row>
      ))}

      {listLink.map(([key, attribute, pool]) => (
        <Row key={key}>
          {errorMessage && (
            <Col span={3}>
              <Tooltip title={errorMessage}>
                <WarningIcon
                  style={{
                    width: 15,
                    height: 25,
                  }}
                />
              </Tooltip>
            </Col>
          )}
          <Col span={errorMessage ? 21 : 24}>
            <SettingWrapper key={key} label={attribute} labelClassName="ants-max-w-[50%] ants-truncate">
              <Space size={10} direction="horizontal">
                {/* Show Icon Error or Warning */}
                {errors[blockSelected.id] && workspaceErrors?.promotionPool?.[pool] ? (
                  <Tooltip title={workspaceErrors?.promotionPool?.[pool]}>
                    <ErrorIcon />
                  </Tooltip>
                ) : warnings[blockSelected.id] && workspaceWarnings?.promotionPool?.[pool] ? (
                  <Tooltip title={workspaceWarnings?.promotionPool?.[pool]}>
                    <WarningIcon />
                  </Tooltip>
                ) : null}

                <Button
                  className="ants-border-none ants-bg-white hover:ants-bg-button-primary-hover"
                  icon={<Icon type="icon-ants-edit-2" size={20} />}
                  title={t(translations.edit.title)}
                  onClick={_ => handleEditDynamicLink(key)}
                />
                <Button
                  className="ants-border-none ants-bg-white hover:ants-bg-button-primary-hover"
                  icon={<Icon type="icon-ants-remove-trash" size={20} />}
                  title={t(translations.remove.title)}
                  onClick={_ => handleDeleteDynamicLink(key)}
                />
              </Space>
            </SettingWrapper>
          </Col>
        </Row>
      ))}

      <SettingWrapper label={t(translations.dynamicContent.sidePanel.highlightCheckbox)}>
        <Switch checked={highlight} onChange={handleChangeHighlight} />
      </SettingWrapper>
    </Space>
  );
};
