// Libraries
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Hook
import { useUserInfo } from 'app/hooks';

// Assets
import PlaceholderImage from 'assets/images/placeholder-image.png';

// Atoms
import { Icon, Text, Skeleton } from 'app/components/atoms';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Styled
import { ImageBlockWrapper, ImagesContainer, ImageBlockEmptyWrapper } from './styled';

// Constants
import { PREFIX_EL_NAME } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Config
import { DATA_MIGRATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';
import { APP_CONFIG } from 'constants/appConfig';

// Utils
import { random } from 'app/utils/common';
import { getRawDynamicData } from '../../../utils';
import { buildMergeTag } from '../../../../../../slice/utils';
import { getDataBOfromDM } from '../../../../SidePanel/components/organisms/AddDynamicContent/utils';
import { useSelector } from 'react-redux';
import { selectCSDataOfGroup } from '../../../../../../slice/selectors';

interface ImageBlockProps extends BlockProps {}

export const ImageBlock: React.FC<ImageBlockProps> = props => {
  // Props
  const { namespace, settings, isPreviewMode, type } = props;
  const { dynamic = DATA_MIGRATE[type].dynamic } = settings;
  const { previewUrl = {}, altText = {}, linkedUrl = {} } = dynamic;
  const { token, user_id, account_id } = useUserInfo();

  // i18n
  const { t } = useTranslation();

  // Selectors
  const contentSourcesData = useSelector(selectCSDataOfGroup);

  // Memoized
  const [dynamicData, dynamicDataPreview, isDynamic, dynamicIndexData] = useMemo(() => {
    const draftData = {
      previewUrl: settings.uploadedImage.previewUrl.toString(),
      altText: settings.altText,
      linkedUrl: settings.linkedUrl,
    };
    const draftDataPreview = {
      previewUrl: settings.uploadedImage.previewUrl.toString(),
      altText: settings.altText,
      linkedUrl: settings.linkedUrl,
    };

    let isDynamic = false;
    const dynamicIndexData: number[] = [];

    // Preview url
    if (previewUrl.isDynamic) {
      draftDataPreview.previewUrl = getRawDynamicData({
        dataTableBO: getDataBOfromDM(previewUrl, contentSourcesData.data),
        dynamicItem: previewUrl,
      });

      draftData.previewUrl = isPreviewMode ? buildMergeTag(previewUrl) : draftDataPreview.previewUrl;

      isDynamic = true;
      dynamicIndexData.push(previewUrl.index);
    }

    // Alt text
    if (altText.isDynamic) {
      draftDataPreview.altText = getRawDynamicData({
        dataTableBO: getDataBOfromDM(altText, contentSourcesData.data),
        dynamicItem: altText,
      });

      draftData.altText = isPreviewMode ? buildMergeTag(altText) : draftDataPreview.altText;

      isDynamic = true;
      dynamicIndexData.push(altText.index);
    }

    // Linked url
    if (linkedUrl.isDynamic) {
      draftDataPreview.linkedUrl = getRawDynamicData({
        dataTableBO: getDataBOfromDM(linkedUrl, contentSourcesData.data),
        dynamicItem: linkedUrl,
      });

      draftData.linkedUrl = isPreviewMode ? buildMergeTag(linkedUrl) : draftDataPreview.linkedUrl;

      isDynamic = true;
      dynamicIndexData.push(linkedUrl.index);
    }

    return [draftData, draftDataPreview, isDynamic, dynamicIndexData];
  }, [previewUrl, altText, contentSourcesData, linkedUrl, settings, isPreviewMode]);

  // Handlers
  const renderImageDynamic = (renderData, { classLinked, relLinked }, isPreview = false) => {
    const proxyImageUrl = isPreview
      ? `${APP_CONFIG.API_HOST}/api/v1/saved-image/external-url/${random(
          8,
        )}?_token=${token}&_user_id=${user_id}&_account_id=${account_id}&imageUrl=${renderData.previewUrl}`
      : '';

    return settings.linkedImage || renderData.linkedUrl ? (
      <a
        className={classLinked}
        href={renderData.linkedUrl}
        {...(settings.linkedTarget === '' ? {} : { target: settings.linkedTarget })}
        {...(relLinked === '' ? {} : { rel: relLinked })}
        target={settings.linkedTarget}
        rel={relLinked}
        style={{
          display: !isPreview ? 'inline' : 'none',
        }}
        {...(isPreview ? { 'data-dynamic-preview': 1 } : { 'data-dynamic-display': 1 })}
      >
        <img
          className={`block-style`}
          src={renderData.previewUrl || 'invalid'}
          alt={renderData.altText}
          style={{ ...settings.styles, display: 'inline' }}
          {...(isPreview && { 'data-proxy-image-url': proxyImageUrl })}
          onError={(e: any) => {
            if (!isPreviewMode) {
              e.target.src = PlaceholderImage;
              e.target.dataset.src = renderData.previewUrl || 'invalid';
            }
          }}
        />
      </a>
    ) : (
      <img
        className={`block-style`}
        src={renderData.previewUrl || 'invalid'}
        alt={renderData.altText}
        style={{ ...settings.styles, display: !isPreview ? 'inline' : 'none' }}
        // width={settings.dimensions.width}
        // height={settings.dimensions.height}
        {...(isPreview
          ? { 'data-dynamic-preview': 1, 'data-proxy-image-url': proxyImageUrl }
          : { 'data-dynamic-display': 1 })}
        onError={(e: any) => {
          if (!isPreviewMode) {
            e.target.src = PlaceholderImage;
            e.target.dataset.src = renderData.previewUrl || 'invalid';
          }
        }}
      />
    );
  };

  const renderImage = () => {
    //
    const relLinked = settings.linkedNoFollow ? 'nofollow' : '';
    const classLinked = settings.linkedTracking
      ? `${PREFIX_EL_NAME}-trigger-conversion`
      : `${PREFIX_EL_NAME}-no-conversion`;
    //

    if (settings.uploadedImage.previewUrl || isDynamic) {
      return (
        <>
          {renderImageDynamic(dynamicData, { classLinked, relLinked })}
          {isDynamic && isPreviewMode && renderImageDynamic(dynamicDataPreview, { classLinked, relLinked }, true)}
        </>
      );
    }

    return (
      <ImageBlockEmptyWrapper>
        <Icon type="icon-ants-plus-circle" className="ants-text-primary" size={50} />
        <Text className="ants-mt-2.5">{t(translations.clickToAddImage.title)}</Text>
      </ImageBlockEmptyWrapper>
    );
  };

  return (
    <ImageBlockWrapper
      className={`${namespace}-imge-content ${namespace}-${settings.component}--content outer-container`}
      style={{
        ...settings.outerContainerStyles,
        ...(!isPreviewMode && { pointerEvents: 'none' }),
      }}
      {...(isDynamic && {
        [APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_IS_DYNAMIC]: 1,
        [APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_DYNAMIC_INDEX]: dynamicIndexData.join(','),
      })}
    >
      {previewUrl.isDynamic && contentSourcesData.isLoading ? (
        <>
          <Skeleton.Image className="!ants-w-full" style={{ height: 200 }} />
          <Skeleton.Input className="ants-absolute ants-top-1 ants-left-1 !ants-w-1/2" size="small" active />
        </>
      ) : (
        <ImagesContainer className="animate__animated animate__fadeIn">{renderImage()}</ImagesContainer>
      )}
    </ImageBlockWrapper>
  );
};

export default ImageBlock;
