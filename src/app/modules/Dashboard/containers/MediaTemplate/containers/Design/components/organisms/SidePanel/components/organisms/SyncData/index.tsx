// Libraries
import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { TreeNode } from 'rc-tree-select';
import { DownOutlined } from '@ant-design/icons';
import set from 'lodash/set';
import get from 'lodash/get';
import cloneDeep from 'lodash/cloneDeep';
import classNames from 'classnames';

// Locales
import { translations } from 'locales/translations';

// Atoms
import { Space, Spin, Icon, Button, Text, Input, Switch } from 'app/components/atoms';

//Action
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Molecules
import { Dropdown, Menu, Select } from 'app/components/molecules';
import { SettingWrapper } from '../../../../SidePanel/components/molecules/SettingWrapper';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

//Util
import { handleError } from 'app/utils/handleError';
import { getEventAttributesBOQuery } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';

//Selector
import {
  selectEvent,
  selectAttribute,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { buildOptionAttrArchive, checkStatusAttr } from '../../../utils';

// Constants
import {
  mapFieldToFieldName,
  serialDatatoOptionAddField as serialDataToOptionAddField,
  optionSourceSerial,
  optionEventSerial,
  optionTreeAttribute,
  getSourceDefault,
  getEventDefault,
  getAttrDefault,
  HASH,
  mapOptionAttributeNameExist,
  mapOptionAttributeChildNameExist,
  checkHashOptionExist,
  FIELD_TYPE,
  BO_SYNC_DATA,
  CUSTOMER_SYNC_METHOD,
  VISITOR_SYNC_METHOD,
  BO_SYNC,
} from './constants';

// Styles
import {
  FieldNameDropdownBtn,
  OptionLibary,
  SelectLibrary,
  TableSyncData,
  // TextFieldName,
  TreeSelectBO,
} from './styled';

// Queries
import { useGetListSourceBO } from 'app/queries/BusinessObject';

// Services
import { getListEvents, getListSources, getListAttributes } from 'app/services/MediaTemplateDesign/BusinessObject';

// Types
import { ItemType } from 'antd/lib/menu/hooks/useItems';
import { flattenTree } from 'app/utils/common';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/ButtonGroupSetting/index.tsx';
interface SyncDataProps {
  fields;
  syncData;
  onChangeColumnMapping: (dataSource: any, isAction: any) => void;
  onChangeBO: (BO: any) => void;
  onChangeSyncData: (data: any) => void;
  onChangeSyncDataToBO?: (value: boolean) => void;
  onChangeBOColumnMapping?: (dataSource: any) => void;
}

export const SyncData: React.FC<SyncDataProps> = props => {
  const dispatch = useDispatch();

  // Actions
  const { setData } = mediaTemplateDesignActions;

  // Props
  const { syncData, fields, onChangeColumnMapping, onChangeBO, onChangeSyncData } = props;
  const { insightPropertyId, insightPropertyName } = syncData?.source || {};
  const { eventTrackingName, translateLabel } = syncData?.event || {};

  const { t } = useTranslation();

  // Queries
  const { data: sourceBO = [], isLoading: isLoadingSourceBO } = useGetListSourceBO();

  // Selector
  const eventBO = useSelector(selectEvent);
  const attributeBO = useSelector(selectAttribute);
  let attributeBOS = optionTreeAttribute(attributeBO);

  // State
  const [loading, setLoading] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState<any>([]);
  const [isLoadingAttr, setIsLoadingAttr] = useState(false);
  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const [isLoadingSyncBO, setLoadingSyncBO] = useState(false);
  const [isOpenDrop, setIsOpenDrop] = useState(false);
  const [state, setState] = useState<any>({
    dataSource: [],
    isAction: false,
    isActionBO: false,
    optionAddField: {
      couponCode: {
        value: 'couponCode',
        label: 'Coupon Code',
      },
    },
    isLoadingEvent: false,
    isStatusButtonAddField: false,
    valueAddField: '',
    // All field from form field
    dataSourceAll: [],
    optionSource: {},
    boAttributes: [],
  });

  const mappingSyncBO = (columnMappingBO, attribute) => {
    if (attribute) {
      const newColumnMappingBO = cloneDeep(columnMappingBO || syncData.columnMappingBO);
      const idx = newColumnMappingBO.findIndex(
        item => !item.attribute || item.attribute.itemPropertyName === attribute.itemPropertyName,
      );

      if (idx >= 0) {
        newColumnMappingBO[idx].attribute = attribute;
        newColumnMappingBO[idx].isDefault = true;
      } else {
        newColumnMappingBO.push({ name: 'Select a field', id: 'add', attribute, hash: null, isDefault: true });
      }
      return newColumnMappingBO;
    }

    return columnMappingBO;
  };

  useEffect(() => {
    if (syncData?.syncDataToBO) {
      const getBoAttr = async () => {
        setLoadingSyncBO(true);
        let boAttributes = await getEventAttributesBOQuery({
          itemTypeIds: [syncData?.syncBO === BO_SYNC.VISITOR ? -1007 : -1003],
        });

        if (syncData?.syncBO === BO_SYNC.VISITOR) {
          boAttributes = boAttributes.filter(item => item.itemPropertyName !== 'user_id');
        }

        boAttributes = boAttributes.map(item => ({
          ...item,
          value: item.itemPropertyName,
          label: item.itemPropertyDisplay,
        }));

        if (boAttributes) {
          setState(prev => ({
            ...prev,
            boAttributes,
          }));
        }

        setLoadingSyncBO(false);

        return boAttributes;
      };

      getBoAttr().then(res => {
        const isActionBO =
          syncData?.columnMappingBO?.length &&
          syncData?.columnMappingBO.every(
            item => !item.attribute || res.some(attr => attr.itemPropertyName === item.attribute.itemPropertyName),
          );
        let {
          dataSourceOut: newColumnMappingBO,
          // allDataSourceOut: allColumnMappngBO,
          // isCouponCode: isCouponCode
        } = mapFieldToFieldName(fields, syncData.columnMappingBO, isActionBO);

        let nameField, idField;
        if (syncData?.syncBO === BO_SYNC.VISITOR) {
          // nameField = res.find(item => item.itemPropertyName === 'name');
          // idField = res.find(item => item.itemPropertyName === 'user_id');
        } else {
          if (syncData?.syncBOMethod !== CUSTOMER_SYNC_METHOD[1].value) {
            nameField = res.find(item => item.itemPropertyName === 'name');
          }
          idField = res.find(item => item.itemPropertyName === 'customer_id');
        }

        newColumnMappingBO = mappingSyncBO(newColumnMappingBO, nameField);
        newColumnMappingBO = mappingSyncBO(newColumnMappingBO, idField);

        onChangeColumnMappingBO(newColumnMappingBO);
      });
    }
  }, [syncData?.syncDataToBO, syncData?.syncBO]);

  useEffect(() => {
    async function getData() {
      if (!sourceBO?.length) return;
      if (syncData.source.insightPropertyId && !eventBO?.length) {
        await getListEvent(syncData);
      }
      if (syncData.event.eventCategoryId && !attributeBO?.length) {
        const init = true;
        await getListAttribute(syncData, init);
      }
      const { dataSourceOut, allDataSourceOut, isCouponCode } = mapFieldToFieldName(
        fields,
        syncData.columnMapping,
        syncData.isAction,
      );

      // onChangeColumnMappings({ dataSource: dataSourceOut, isAction: null });

      const compareData = serialDataToOptionAddField(syncData.columnMapping, allDataSourceOut);
      setState(state => ({
        ...state,
        dataSource: dataSourceOut,
        isStatusButtonAddField: Object.keys(compareData).length > 0 || !isCouponCode ? false : true,
        dataSourceAll: allDataSourceOut,
        optionAddField: compareData,
      }));

      if (dataSourceOut && !syncData.isAction && !syncData?.columnMapping?.length) {
        const sourceDefault = getSourceDefault(sourceBO);
        if (eventBO && Object.keys(sourceDefault).length > 0) {
          const events = await getListEvents({ source_id: sourceDefault.insightPropertyId });
          if (events) {
            const eventDefault = getEventDefault(events);
            dispatch(
              setData({
                events,
              }),
            );
            const params = {
              eventActionId: eventDefault.eventActionId,
              eventCategoryId: eventDefault.eventCategoryId,
              sourceId: eventDefault.insightPropertyId,
              getAll: true,
            };

            const attributes = await getListAttributes(params);
            if (attributes) {
              dispatch(
                setData({
                  attributes,
                }),
              );
              const dataAttrDefault = getAttrDefault(dataSourceOut, attributes);
              onChangeDefaultData({
                BO: {
                  source: { ...sourceDefault },
                  columnMapping: [...dataAttrDefault],
                  event: { ...eventDefault },
                  isAction: true,
                },
              });
            }
          }
        }
      } else {
        onChangeColumnMappings({ dataSource: dataSourceOut, isAction: null });
      }
      setLoading(false);
    }

    getData();
  }, [fields, sourceBO]);

  // Memoized
  const fieldOptions = useMemo(() => {
    const options: ItemType[] = [];
    Object.values(state.optionAddField)?.forEach((item: any) => {
      options.push({
        key: item.value,
        label: item.label,
      });
    });

    const restOfOptions: ItemType[] = [
      {
        key: 'divider-1',
        type: 'divider',
      },
      {
        key: FIELD_TYPE.SET_VALUES.value,
        label: (
          <div className="ants-flex ants-items-center ants-space-x-2.5">
            <Icon type="icon-ants-edit-2" className="ants-text-primary ants-w-5" size={20} />
            <Text>{t(translations.setValues.title)}</Text>
          </div>
        ),
      },
      {
        key: 'divider-2',
        type: 'divider',
      },
      {
        key: 'remove',
        label: (
          <div className="ants-flex ants-items-center ants-space-x-2.5">
            <Icon type="icon-ants-remove-trash" className="ants-text-primary ants-w-5" size={18} />
            <Text>{t(translations.remove.title)}</Text>
          </div>
        ),
      },
    ];

    options.push(...restOfOptions);

    return options;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.optionAddField]);

  const fieldOptionsBO = useCallback(
    (field: any) => {
      const options: ItemType[] = [];
      Object.values(state.optionAddField)?.forEach((item: any) => {
        options.push({
          key: item.value,
          label: item.label,
        });
      });

      const restOfOptions: ItemType[] = [
        {
          key: 'divider-1',
          type: 'divider',
        },
        {
          key: FIELD_TYPE.SET_VALUES.value,
          label: (
            <div className="ants-flex ants-items-center ants-space-x-2.5">
              <Icon type="icon-ants-edit-2" className="ants-text-primary ants-w-5" size={20} />
              <Text>{t(translations.setValues.title)}</Text>
            </div>
          ),
        },
      ];

      if (field && !field.isDefault) {
        restOfOptions.push({
          key: 'divider-2',
          type: 'divider',
        });
        restOfOptions.push({
          key: 'remove',
          label: (
            <div className="ants-flex ants-items-center ants-space-x-2.5">
              <Icon type="icon-ants-remove-trash" className="ants-text-primary ants-w-5" size={18} />
              <Text>{t(translations.remove.title)}</Text>
            </div>
          ),
        });
      }

      options.push(...restOfOptions);

      return options;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [state.optionAddField],
  );

  // Memo
  const flatEventAttrs = useMemo(() => {
    return flattenTree(attributeBOS, 'items');
  }, [attributeBOS]);

  const getListSource = async dataSourceOut => {
    const sources = await getListSources();
    if (sources) {
      dispatch(
        setData({
          sources,
        }),
      );
      if (!syncData.isAction && dataSourceOut) {
        setIsLoadingTable(true);
        // const { dataSourceOut } = mapFieldtoFieldName(fields, syncData.columnMapping, syncData.isAction);
        const sourceDefault = getSourceDefault(sources);
        if (eventBO && Object.keys(sourceDefault).length > 0) {
          const events = await getListEvents({ source_id: sourceDefault.insightPropertyId });
          if (events) {
            const eventDefault = getEventDefault(events);
            dispatch(
              setData({
                events,
              }),
            );
            const params = {
              eventActionId: eventDefault.eventActionId,
              eventCategoryId: eventDefault.eventCategoryId,
              sourceId: eventDefault.insightPropertyId,
              getAll: true,
            };

            const attributes = await getListAttributes(params);
            if (attributes) {
              dispatch(
                setData({
                  attributes,
                }),
              );
              const dataAttrDefault = getAttrDefault(dataSourceOut, attributes);
              onChangeDefaultData({
                BO: { source: { ...sourceDefault }, columnMapping: [...dataAttrDefault], event: { ...eventDefault } },
              });
              setIsLoadingTable(false);
            }
          }
        } else {
          setIsLoadingTable(false);
        }
      }
    }
  };

  const getListEvent = async data => {
    setState(state => ({
      ...state,
      isLoadingEvent: true,
    }));
    const events = await getListEvents({ source_id: data.source.insightPropertyId });
    if (events) {
      dispatch(
        setData({
          events,
        }),
      );
      setState(state => ({
        ...state,
        isLoadingEvent: false,
      }));
    }
  };

  const getListAttribute = async (data, init) => {
    setIsLoadingAttr(true);
    if (init) {
      setIsLoadingTable(true);
    }
    const params = {
      eventActionId: data.event.eventActionId,
      eventCategoryId: data.event.eventCategoryId,
      sourceId: syncData?.source.insightPropertyId,
      getAll: true,
    };
    const attributes = await getListAttributes(params);
    if (attributes) {
      dispatch(
        setData({
          attributes,
        }),
      );
      setIsLoadingAttr(false);
      if (init) {
        setIsLoadingTable(false);
      }
    }
  };

  const onSelectParent = (item, data, isCheckAttrBr, index) => {
    if (!isCheckAttrBr) {
      if (item.isLeaf) {
        onSelectChildren('', data, item, false, index);
      } else {
        if (expandedKeys.includes(item.value)) {
          // close expand
          const newExpandedKeys = expandedKeys.filter(key => key !== item.value);
          onTreeExpand(newExpandedKeys);
        } else {
          // open expand
          onTreeExpand(prev => [...prev, item.value]);
        }
      }
    }
  };

  const onChangeHash = (value, data, index) => {
    let dataSourceAdd = [...syncData.columnMapping] as any;
    const dataOut = dataSourceAdd.map((each, key) => {
      const items = { ...each };
      if (each.id === data.id && key === index) {
        items.hash = value;
      }
      return { ...items };
    });
    onChangeColumnMappings({
      dataSource: dataOut,
      isAction: true,
    });
  };

  const onSelectChildren = (item, data, attr, isCheckAttrChild, index) => {
    if (!isCheckAttrChild) {
      let dataSourceAdd = [...syncData.columnMapping] as any;
      const dataOut = dataSourceAdd.map((each, key) => {
        const itemAttribute = { ...each };
        if (each.id === data.id && index === key) {
          itemAttribute.attribute = { ...attr, attributeOpin: item };
        }
        return { ...itemAttribute };
      });
      onChangeColumnMappings({
        dataSource: dataOut,
        isAction: true,
      });
    }

    // setExpandedKeys([]);
  };

  const onTreeExpand = v => {
    setExpandedKeys(v);
  };

  const onSearch = val => {
    setExpandedKeys(attributeBOS.map(attr => attr.value));
  };

  const onDropdownVisibleChange = value => {
    if (isOpenDrop) {
      setIsOpenDrop(false);
    } else {
      setIsOpenDrop(true);
    }
    setExpandedKeys([value.data]);
  };

  const onClickFieldsMenu = ({ rowData, index, field }) => {
    try {
      const { key } = field;

      switch (key) {
        case FIELD_TYPE.SET_VALUES.value:
          setValues({
            rowIndex: index,
          });

          break;
        case 'remove':
          removeField(index);
          break;

        default:
          addField({
            rowIndex: index,
            fieldKey: key,
          });
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickFieldsMenu',
        args: { rowData, index, field },
      });
    }
  };

  const fieldsMenu = (item: Record<string, any> = {}, index: number) => {
    return (
      <Menu
        selectedKeys={item.id}
        items={fieldOptions}
        onClick={field =>
          onClickFieldsMenu({
            rowData: item,
            index,
            field,
          })
        }
      />
    );
  };

  const fieldsMenuBO = (item: Record<string, any> = {}, index: number) => {
    return (
      <Menu
        selectedKeys={item.id}
        items={fieldOptionsBO(item)}
        onClick={field => updateColumnMappingBO(index, 'field', field)}
      />
    );
  };

  const columns = [
    {
      title: 'Field name',
      dataIndex: 'name',
      key: 'field',
      textWrap: 'word-break',
      ellipsis: true,
      width: 95,
      render: (value: any, item: Record<string, any>, index: number) => {
        return (
          <Dropdown overlay={() => fieldsMenu(item, index)} trigger={['click']}>
            <FieldNameDropdownBtn>
              {item.type === FIELD_TYPE.SET_VALUES.value ? (
                <Input
                  noborder={'true'}
                  value={item.value}
                  placeholder={t(translations.inputYourValue.title)}
                  onChange={e => onChangeFieldNameInput({ rowIndex: index, value: e.target.value })}
                  onClick={e => e.stopPropagation()}
                />
              ) : (
                <Text
                  className={classNames('ants-overflow-hidden ants-text-ellipsis', {
                    'ants-opacity-25': item.id === 'add',
                  })}
                >
                  {value}
                </Text>
              )}

              <DownOutlined />
            </FieldNameDropdownBtn>
          </Dropdown>
        );
      },
    },
    {
      title: 'HASH',
      dataIndex: 'hash',
      key: 'hash',
      width: 80,
      render: (value, data, index) => {
        return (
          // <div style={{ marginRight: '50px' }}>
          <SelectLibrary
            // options={sourceBO?.map(item => ({
            //   label: item.source_display_name,
            //   value: item.source_id,
            // }))}
            style={{ width: '86px', fontSize: '12px', color: '#222222', marginLeft: '-10px' }}
            // showArrow
            bordered={false}
            disabled={data.name === 'Select a field'}
            // options={Object.values(HASH)}
            onChange={value => onChangeHash(value, data, index)}
            placeholder="Select an hash"
            value={value}
          >
            {HASH.map(item => {
              const isCheck = checkHashOptionExist(item, data, syncData);
              return (
                <OptionLibary disabled={isCheck} value={item.value}>
                  {item.label}
                </OptionLibary>
              );
            })}
          </SelectLibrary>
          // </div>
        );
      },
    },
    {
      title: 'Attribute name',
      dataIndex: 'attribute',
      key: 'attribute',
      render: (value, data, index) => {
        const { errorMessage } = checkStatusAttr({
          listAttribute:
            flatEventAttrs.map(({ itemTypeId, itemPropertyName, status }) => ({
              value: `${itemTypeId}.${itemPropertyName}`,
              status,
            })) || [],
          field: get(value, 'attributeOpin.value', '') || undefined,
        });

        return (
          <>
            <TreeSelectBO
              virtual={false}
              bordered={false}
              showArrow
              showSearch
              style={{ width: '115%', fontSize: '12px', color: '#222222', marginLeft: '-10px' }}
              placeholder="Select an item"
              placement={'bottomRight'}
              dropdownStyle={{ maxHeight: 300, overflow: 'auto !important', minWidth: 200 }}
              value={value.attributeOpin ? value.attributeOpin.value : value.value}
              onDropdownVisibleChange={() =>
                onDropdownVisibleChange({
                  data: value.value,
                })
              }
              treeExpandedKeys={expandedKeys}
              onTreeExpand={onTreeExpand}
              onSearch={onSearch}
              filterTreeNode={(val, option) => {
                return option.props?.title?.props?.children?.toLowerCase().indexOf(val.toLowerCase()) >= 0;
              }}
              // tagRender={v => <Tag {...v} />}
              dropdownRender={menu =>
                isLoadingAttr ? (
                  <div className="ants-flex ants-justify-center ants-items-center">
                    <Spin spinning={isLoadingAttr} />
                  </div>
                ) : (
                  <>{menu}</>
                )
              }
            >
              {attributeBOS.map(attr => {
                const isCheckAttrBr = mapOptionAttributeNameExist(attr, syncData);
                return (
                  <TreeNode
                    key={attr.value}
                    value={attr.value}
                    disabled={attr.children.length > 0 ? true : false || isCheckAttrBr}
                    title={
                      <div
                        style={{
                          color: isCheckAttrBr && isOpenDrop ? '#00000040' : '#222222',
                          fontWeight: 'normal',
                          width: '115px',
                        }}
                        onClick={() => onSelectParent(attr, data, isCheckAttrBr, index)}
                      >
                        {attr.title}
                      </div>
                    }
                  >
                    {buildOptionAttrArchive(
                      attr.children,
                      value.attributeOpin ? value.attributeOpin.value : value.value,
                    ).map(item => {
                      const isCheckAttrChild = mapOptionAttributeChildNameExist(item, syncData, attr);

                      return (
                        <TreeNode
                          key={`${item.value}`}
                          value={item.value}
                          disabled={isCheckAttrChild}
                          title={
                            <div
                              style={{
                                color: isCheckAttrChild && isOpenDrop ? '#00000040' : '#222222',
                                width: '115px',
                                fontWeight: 'normal',
                              }}
                              onClick={() => onSelectChildren(item, data, attr, isCheckAttrChild, index)}
                            >
                              {item.title}
                            </div>
                          }
                        />
                      );
                    })}
                  </TreeNode>
                );
              })}
            </TreeSelectBO>
            {errorMessage ? (
              <Text color="#ff4d4f" className="ants-mt-5px">
                {errorMessage}
              </Text>
            ) : null}
          </>
        );
      },
    },
  ];

  const updateColumnMappingBO = (idx, key, value, others = {}) => {
    const newMapping = cloneDeep(syncData.columnMappingBO);
    switch (key) {
      case 'field':
        const { key } = value;

        switch (key) {
          case FIELD_TYPE.SET_VALUES.value:
            set(newMapping, `[${idx}]`, {
              ...newMapping[idx],
              type: FIELD_TYPE.SET_VALUES.value,
              id: FIELD_TYPE.SET_VALUES.value,
              name: '',
              value: '',
            });
            break;
          case 'remove':
            newMapping.splice(idx, 1);
            break;

          default:
            const field = state.optionAddField[key] || {};

            newMapping[idx] = {
              ...newMapping[idx],
              name: field.label,
              id: field.value,
              type: fields[key]?.type || 'text',
              inputName: fields[key]?.inputName,
              value: '',
            };
            break;
        }
        // if (typeof value === 'string') {
        //   newMapping[idx].type = 'set_values'
        //   newMapping[idx].id = 'set_values'
        // } else {
        //   newMapping[idx].inputName = value.inputName
        //   newMapping[idx].id = value.
        //   newMapping[idx].name = value
        // }
        break;
      case 'setValue':
        set(newMapping, `[${idx}]`, {
          ...newMapping[idx],
          value,
        });
        break;
      case 'hash':
        set(newMapping, `[${idx}]`, {
          ...newMapping[idx],
          hash: value,
        });
        break;
      case 'boAttribute':
        set(newMapping, `[${idx}]`, {
          ...newMapping[idx],
          attribute: value,
        });
        break;
      case 'addField':
        newMapping.push({ name: 'Select a field', id: 'add', attribute: value, hash: null, ...others });
        break;
    }

    onChangeColumnMappingBO(newMapping);
  };

  const columnsBO = syncData?.syncDataToBO
    ? [
        {
          title: 'Field name',
          dataIndex: 'name',
          key: 'field',
          textWrap: 'word-break',
          ellipsis: true,
          width: 95,
          render: (value: any, item: Record<string, any>, index: number) => {
            return (
              <Dropdown overlay={() => fieldsMenuBO(item, index)} trigger={['click']}>
                <FieldNameDropdownBtn>
                  {item.type === FIELD_TYPE.SET_VALUES.value ? (
                    <Input
                      noborder={'true'}
                      value={item.value}
                      placeholder={t(translations.inputYourValue.title)}
                      onChange={e => updateColumnMappingBO(index, 'setValue', e.target.value)}
                      onClick={e => e.stopPropagation()}
                    />
                  ) : (
                    <Text
                      className={classNames('ants-overflow-hidden ants-text-ellipsis', {
                        'ants-opacity-25': item.id === 'add',
                      })}
                    >
                      {value}
                    </Text>
                  )}

                  <DownOutlined />
                </FieldNameDropdownBtn>
              </Dropdown>
            );
          },
        },
        {
          title: 'HASH',
          dataIndex: 'hash',
          key: 'hash',
          width: 80,
          render: (value, data, index) => {
            return (
              // <div style={{ marginRight: '50px' }}>
              <SelectLibrary
                // options={sourceBO?.map(item => ({
                //   label: item.source_display_name,
                //   value: item.source_id,
                // }))}
                style={{ width: '86px', fontSize: '12px', color: '#222222', marginLeft: '-10px' }}
                // showArrow
                bordered={false}
                disabled={data.name === 'Select a field'}
                // options={Object.values(HASH)}
                onChange={value => updateColumnMappingBO(index, 'hash', value)}
                placeholder="Select an hash"
                value={value}
              >
                {HASH.map(item => {
                  const isCheck = checkHashOptionExist(item, data, syncData);
                  return (
                    <OptionLibary disabled={isCheck} value={item.value}>
                      {item.label}
                    </OptionLibary>
                  );
                })}
              </SelectLibrary>
              // </div>
            );
          },
        },
        {
          title: 'Attribute name',
          dataIndex: 'attribute',
          key: 'attribute',
          render: (value, data, index) => {
            const { errorMessage } = checkStatusAttr({
              listAttribute:
                flatEventAttrs.map(({ itemTypeId, itemPropertyName, status }) => ({
                  value: `${itemTypeId}.${itemPropertyName}`,
                  status,
                })) || [],
              field: get(value, 'attributeOpin.value', '') || undefined,
            });

            return (
              <>
                <Select
                  // label={t('Source')}
                  // options={sourceBO?.map(item => ({
                  //   label: item.source_display_name,
                  //   value: item.source_id,
                  // }))}
                  showSearch
                  options={state.boAttributes}
                  placeholder="Select an attribute"
                  value={value}
                  filterOption={(val, option: any) => {
                    return option.props?.label.toLowerCase().indexOf(val.toLowerCase()) >= 0;
                  }}
                  onChange={(value, option: any) => updateColumnMappingBO(index, 'boAttribute', option)}
                  className={data.isDefault ? 'disabled-select' : ''}
                  disabled={data.isDefault}
                />
                {errorMessage ? (
                  <Text color="#ff4d4f" className="ants-mt-5px">
                    {errorMessage}
                  </Text>
                ) : null}
              </>
            );
          },
        },
      ]
    : null;

  const onChangeFieldNameInput = ({ rowIndex, value }) => {
    try {
      let dataSourceAdd = cloneDeep(syncData.columnMapping);

      dataSourceAdd[rowIndex].value = value;

      setState(state => ({
        ...state,
        dataSource: dataSourceAdd,
      }));

      onChangeColumnMappings({
        dataSource: dataSourceAdd,
        isAction: true,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeInputFieldName',
        args: {},
      });
    }
  };

  const addField = ({ rowIndex, fieldKey }) => {
    let dataSourceAdd = [...syncData.columnMapping] as any;
    const field = state.optionAddField[fieldKey] || {};

    dataSourceAdd[rowIndex] = {
      ...dataSourceAdd[rowIndex],
      name: field.label,
      id: field.value,
      type: fields[fieldKey]?.type || 'text',
      value: '',
    };

    onChangeColumnMappings({
      dataSource: dataSourceAdd,
      isAction: true,
    });

    setState(state => ({
      ...state,
      dataSource: dataSourceAdd,
      isStatusButtonAddField: dataSourceAdd.some((item: any) => item.id === 'add'),
      isAction: true,
    }));
  };

  const setValues = ({ rowIndex }) => {
    try {
      let dataSourceAdd = cloneDeep(syncData.columnMapping);

      set(dataSourceAdd, `[${rowIndex}]`, {
        ...dataSourceAdd[rowIndex],
        type: FIELD_TYPE.SET_VALUES.value,
        id: FIELD_TYPE.SET_VALUES.value,
        name: '',
        value: '',
      });

      onChangeColumnMappings({
        dataSource: dataSourceAdd,
        isAction: true,
      });

      setState(state => ({
        ...state,
        dataSource: dataSourceAdd,
        isStatusButtonAddField: dataSourceAdd.some((item: any) => item.id === 'add'),
        isAction: true,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'setValues',
        args: {},
      });
    }
  };

  const removeField = rowIndex => {
    let dataSourceAdd = [...syncData.columnMapping] as any;

    dataSourceAdd.splice(rowIndex, 1);

    onChangeColumnMappings({
      // dataSource: syncData.columnMapping.filter((data, key) => data.id !== valueRemove && key !== index),
      dataSource: dataSourceAdd,
      isAction: true,
    });

    setState(state => ({
      ...state,
      dataSource: dataSourceAdd,
      isStatusButtonAddField: false,
      isAction: true,
    }));
  };

  // On Add Field
  const onAddField = () => {
    const dataSourceAdd = [...syncData.columnMapping] as any;
    dataSourceAdd.push({ name: 'Select a field', id: 'add', attribute: '', hash: null });
    onChangeColumnMappings({
      dataSource: dataSourceAdd,
      isAction: true,
    });
    setState(state => ({
      ...state,
      dataSource: dataSourceAdd,
      isStatusButtonAddField: true,
      optionAddField: serialDataToOptionAddField(syncData.columnMapping, state.dataSourceAll),
    }));
  };

  // OnChange Column Mapping
  const onChangeColumnMappings = ({ dataSource = [], isAction }) => {
    try {
      // Callback onChange
      onChangeColumnMapping([...dataSource], isAction);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeColumnMappings',
        args: {},
      });
    }
  };

  const onChangeColumnMappingBO = mapping => {
    try {
      // Callback onChange
      handleChangeSyncDataToBO('columnMappingBO', mapping);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeColumnMappings',
        args: {},
      });
    }
  };

  // OnChange
  const onChangeEventSource = ({ BO }) => {
    let dataSourceAdd = [...syncData.columnMapping] as any;
    const dataOut = dataSourceAdd.map(each => {
      const itemAttribute = { ...each };
      itemAttribute.attribute = '';
      return { ...itemAttribute };
    });

    if (BO.source) {
      getListEvent(BO);
      dispatch(
        setData({
          attributes: [],
          // events: [],
        }),
      );
    }
    if (BO.event.value) {
      getListAttribute(BO, false);
    }
    const test = { ...BO, columnMapping: dataOut };
    try {
      // Callback onChange
      onChangeBO({
        ...syncData,
        ...test,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeColumnMappings',
        args: {},
      });
    }
  };

  // OnChange
  const onChangeDefaultData = ({ BO }) => {
    const data = { ...BO };
    try {
      // Callback onChange
      onChangeSyncData({
        ...data,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeColumnMappings',
        args: {},
      });
    }
  };

  const updateDefaultBOMapping = (mapping, syncBO, syncBOMethod) => {
    let newMapping = cloneDeep(mapping);
    const idx = newMapping.findIndex(item => item.attribute && item.attribute.itemPropertyName === 'name');

    const isRequiredName = syncBO === BO_SYNC.CUSTOMER && syncBOMethod !== CUSTOMER_SYNC_METHOD[1].value;

    if (idx >= 0) {
      newMapping[idx].isDefault = isRequiredName;
      return newMapping;
    } else if (isRequiredName) {
      const nameField = state.boAttributes.find(item => item.itemPropertyName === 'name');

      if (nameField) {
        newMapping = mappingSyncBO(newMapping, nameField);
        return newMapping;
      }
    }

    return null;
  };

  const handleChangeSyncDataToBO = (key, value) => {
    try {
      // Callback onChange
      if (typeof onChangeSyncData === 'function') {
        const changedSyncBO: Record<string, any> = { [key]: value };

        if (key === 'syncDataToBO' && !syncData.syncBO) {
          changedSyncBO.syncBO = BO_SYNC.CUSTOMER;
          changedSyncBO.syncBOMethod = CUSTOMER_SYNC_METHOD[0].value;
        } else if (key === 'syncBO') {
          changedSyncBO.syncBOMethod =
            value === BO_SYNC.VISITOR ? VISITOR_SYNC_METHOD[0].value : CUSTOMER_SYNC_METHOD[0].value;

          const newMapping = updateDefaultBOMapping(syncData.columnMappingBO, value, changedSyncBO.syncBOMethod);

          if (newMapping) {
            changedSyncBO.columnMappingBO = newMapping;
          }
        } else if (key === 'syncBOMethod') {
          const newMapping = updateDefaultBOMapping(syncData.columnMappingBO, syncData.syncBO, value);

          if (newMapping) {
            changedSyncBO.columnMappingBO = newMapping;
          }
        }

        onChangeSyncData(changedSyncBO);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeColumnMappings',
        args: {},
      });
    }
  };

  return (
    <>
      <Space size={20} direction="vertical">
        <Select
          label={t('Source')}
          // options={sourceBO?.map(item => ({
          //   label: item.source_display_name,
          //   value: item.source_id,
          // }))}
          showSearch
          options={Object.values(optionSourceSerial(sourceBO))}
          placeholder="Select an source"
          value={!isLoadingSourceBO && (insightPropertyId || insightPropertyName)}
          loading={isLoadingSourceBO || loading}
          filterOption={(val, option: any) => {
            return option.props?.label.toLowerCase().indexOf(val.toLowerCase()) >= 0;
          }}
          onChange={(value, option: any) =>
            onChangeEventSource({
              BO: { source: { ...option, insightPropertyId: value, insightPropertyName: option.label }, event: {} },
            })
          }
        />
        <Select
          showSearch
          loading={isLoadingSourceBO || isLoadingTable || loading || state.isLoadingEvent}
          label={t('Event')}
          options={Object.values(optionEventSerial(eventBO))}
          placeholder="Select an event"
          disabled={state.isLoadingEvent}
          // notFoundContent={state.isLoadingEvent ? <Spin size="small" /> : null}
          value={!isLoadingSourceBO && !isLoadingTable && (eventTrackingName || translateLabel)}
          onChange={(value, option: any) =>
            onChangeEventSource({
              BO: { event: { ...option } },
            })
          }
        />
        {isLoadingSourceBO || isLoadingTable || loading || state.isLoadingEvent ? (
          <div className="ants-flex ants-justify-center ants-items-center">
            <Spin spinning={isLoadingSourceBO || isLoadingTable || loading || state.isLoadingEvent} />
          </div>
        ) : (
          <TableSyncData
            className="ants-w-full "
            bordered
            rowClassName={() => 'ants-align-baseline'}
            pagination={false}
            dataSource={(syncData.columnMapping || []).map((item, index) => ({ ...item, key: index }))}
            columns={columns}
          />
        )}

        <Button disabled={state.isStatusButtonAddField} onClick={onAddField} type="text">
          + Add field
        </Button>

        <SettingWrapper label={t(translations.syncDataToBO.title)}>
          <Switch
            loading={loading}
            checked={syncData?.syncDataToBO}
            onChange={checked => handleChangeSyncDataToBO('syncDataToBO', checked)}
          />
        </SettingWrapper>

        {!!syncData?.syncDataToBO && columnsBO && (
          <>
            <Select
              label={t('Business Object')}
              // options={sourceBO?.map(item => ({
              //   label: item.source_display_name,
              //   value: item.source_id,
              // }))}
              // showSearch
              options={BO_SYNC_DATA}
              placeholder="Select a BO"
              loading={loading}
              value={syncData?.syncBO || BO_SYNC.CUSTOMER}
              onChange={value => handleChangeSyncDataToBO('syncBO', value)}
            />
            <Select
              // showSearch
              label={t('Method')}
              options={syncData?.syncBO === BO_SYNC.VISITOR ? VISITOR_SYNC_METHOD : CUSTOMER_SYNC_METHOD}
              placeholder="Select a method"
              loading={loading}
              disabled={state.isLoadingEvent}
              // notFoundContent={state.isLoadingEvent ? <Spin size="small" /> : null}
              value={syncData?.syncBOMethod || CUSTOMER_SYNC_METHOD[0].value}
              onChange={value => handleChangeSyncDataToBO('syncBOMethod', value)}
            />
            {loading || isLoadingSyncBO ? (
              <div className="ants-flex ants-justify-center ants-items-center">
                <Spin spinning={loading || isLoadingSyncBO} />
              </div>
            ) : (
              <TableSyncData
                className="ants-w-full "
                bordered
                rowClassName={() => 'ants-align-baseline'}
                pagination={false}
                dataSource={(syncData.columnMappingBO || []).map((item, index) => ({ ...item, key: index }))}
                columns={columnsBO}
              />
            )}

            <Button
              // disabled={state.isStatusButtonAddField}
              onClick={() => updateColumnMappingBO(0, 'addField', null)}
              type="text"
            >
              + Add field
            </Button>
          </>
        )}
      </Space>
    </>
  );
};
