// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

// Atoms
import { Button, Icon } from 'app/components/atoms';

export const ComponentsWrapper = styled.div`
  ${tw`ants-flex ants-flex-col ants-pt-15px ants-px-2.5`}
`;

export const ElementItem = styled.div`
  ${tw`ants-flex ants-items-center ants-justify-between ants-py-2.5 ants-px-5px ants-space-x-5px ants-cursor-pointer ants-transition-colors ants-duration-300`}

  &.--active {
    ${tw`ants-bg-button-primary-active`}
  }

  /* &:hover { */
  /*   ${tw`ants-bg-accent-1`} */
  /* } */
`;

export const OptionIcon = styled(Icon)`
  ${tw`ants-transition-colors ants-duration-300 ants-text-cus-second`}

  &:hover {
    ${tw`ants-text-primary`}
  }
`;

export const AddComponent = styled(But<PERSON>)`
  ${tw`ants-flex-col ants-space-y-5px ants-mt-2.5 ants-mx-auto`}
  width: 167px !important;
  height: 83px !important;
`;
