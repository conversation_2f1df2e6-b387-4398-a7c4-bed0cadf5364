// Libraries
import React from 'react';

// Locales
import { translations } from 'locales/translations';

// Types
import {
  TBusinessObjectSettings,
  TFilters,
  TRanking,
  TTrackingModuleData,
  TColumnTableBlock,
  TContentSourceSettings,
} from './types';
import { TTrackingModule } from './slice/types';

// Utils
import { getTranslateMessage } from 'utils/messages';
import { TUtmTrackingSettings } from './types';
import { initialsErrorMessage, random } from 'app/utils/common';

//constants
import { FALLBACK_SELECTION } from './components/organisms/SidePanel/components/organisms/BlockEditing/Settings/Basic/constants';
import { LAYOUT_TEMPLATE, STANDARDS_BLOCKS } from './constants';
import { BREAK_POINT_KEYS, HASH, OBJECTIVE_VALUES, OPERATORS_OPTION } from 'constants/variables';
import { DISPLAY_LAYOUT } from './components/organisms/Workspace/components/organisms/OTPVerification/constants';
import { VALUE_STYLE_INPUT_FIELD } from './components/organisms/SidePanel/components/organisms/BlockEditing/OTPVerification/constants';

const DEFAULT_CONDITION = {
  field: null,
  hash: HASH.NONE.value,
  operator: OPERATORS_OPTION.MATCHES.value,
  valueType: 'normal',
  value: null,
  filters: { OR: [{ AND: [] }] },
};

export const DATA_MIGRATE = {
  [STANDARDS_BLOCKS.IMAGE.name]: {
    dynamic: {
      previewUrl: {
        isDynamic: false,
      },
      altText: {
        isDynamic: false,
      },
      linkedUrl: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
  },
  [STANDARDS_BLOCKS.BUTTON.name]: {
    dynamic: {
      url: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
  },
  [STANDARDS_BLOCKS.OPTIN_FIELDS.name]: {
    dynamic: {
      url: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
    fields: {
      firstNameInput: {
        id: 'firstNameInput',
        inputName: 'firstName',
        name: getTranslateMessage(translations.optinFields.fields.firstNameField.title),
        type: 'text',
        order: -1,
        placeholder: getTranslateMessage(translations.optinFields.fields.firstNameField.placeholder),
        inputWidth: '80%',
        fieldWidth: '100%',
        required: true,
        label: getTranslateMessage(translations.optinFields.fields.firstNameField.title),
        displayLabel: true,
        alignField: 'horizontal',
        nameField: getTranslateMessage(translations.optinFields.fields.firstNameField.title),
        errorText: initialsErrorMessage('first name'),
        requireErrorMessage: '',
        invalidText: '',
        isShowField: true,
      },
      lastNameInput: {
        id: 'lastNameInput',
        inputName: 'lastName',
        name: getTranslateMessage(translations.optinFields.fields.lastNameField.title),
        type: 'text',
        order: -1,
        placeholder: getTranslateMessage(translations.optinFields.fields.lastNameField.placeholder),
        inputWidth: '80%',
        fieldWidth: '100%',
        required: true,
        label: getTranslateMessage(translations.optinFields.fields.lastNameField.title),
        displayLabel: true,
        alignField: 'horizontal',
        nameField: getTranslateMessage(translations.optinFields.fields.lastNameField.title),
        requireErrorMessage: '',
        errorText: initialsErrorMessage('last name'),
        invalidText: '',
        isShowField: true,
      },
    },
    stylesSettings: {
      gapX: '0px',
      gapY: '0px',
      gapSuffix: 'px',
      linkedGapInput: true,
    },
    limitedSubmit: {
      typeFrequency: 'unlimited',
      conditions: [],
      messagePosition: 'top',
      cappingLevel: 'variant',
    },
    verifiedSubmit: {
      conditions: { OR: [{ AND: [DEFAULT_CONDITION] }] },
      errorMessage: 'Invalid information. Please check again',
    },
    settings: {
      labelStyles: {
        textAlign: 'left',
        color: '#000000',
        fontFamily: 'Montserrat',
        fontWeight: 400,
        fontSize: '16px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0',
        fontStyle: 'normal',
        paddingLeft: '0',
      },
      labelStylesSettings: {
        gapX: '0px',
        gapY: '0px',
        gapSuffix: 'px',
        linkedGapInput: true,
        textAlign: 'left',
        indentation: '0',
      },
      radioCheckboxStyles: {
        color: '#000000',
        fontFamily: 'Montserrat',
        fontWeight: 400,
        fontSize: '16px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0',
        fontStyle: 'normal',
      },
      radioCheckboxStylesSettings: {
        gapSuffix: 'px',
        position: 'left',
        optionLabelGap: '0',
        lineHeightGap: '0',
        columnSpacing: '0',
      },
      errorStyles: {
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        borderTopLeftRadius: '3px',
        borderTopRightRadius: '3px',
        borderBottomRightRadius: '3px',
        borderBottomLeftRadius: '3px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '10px',
        marginLeft: '0px',
        paddingTop: '8px',
        paddingRight: '12px',
        paddingBottom: '8px',
        paddingLeft: '12px',
        color: '#aa6463',
        fontFamily: 'Roboto',
        fontWeight: 400,
        fontSize: '14px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0',
        fontStyle: 'normal',
        background: '#ffefef',
        boxShadow: 'none',
        borderStyle: 'solid',
        borderColor: '#ffefef',
        display: 'block',
        maxWidth: '100%',
      },
      errorStylesSettings: {
        defaultErrorStyles: true,
        errorPosition: 'top',
        backgroundColor: '#ffffff',
        backgroundColorStyle: 'color',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
        borderRadiusStyle: 'custom',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: false,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: 'rgba(0, 0, 0, 0.5)',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        inputSize: 'small-rounded',
        placeholderColor: '#b1bacb',
        marginAlign: '',
      },
    },
    defaultFieldsProperties: {
      isShowField: true,
      alignField: 'vertical',
    },
  },
  [STANDARDS_BLOCKS.YES_NO.name]: {
    yesDynamic: {
      url: {
        isDynamic: false,
      },
    },
    noDynamic: {
      url: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
  },
  [STANDARDS_BLOCKS.VIDEO.name]: {
    dynamic: {
      videoSrc: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
  },
  [STANDARDS_BLOCKS.TEXT.name]: {
    dynamic: {
      data: {},
      highlight: true,
      selectedId: '',
    },
    link: {
      data: {},
      selectedId: '',
    },
    defaultDynamicIndex: 1,
  },
  [STANDARDS_BLOCKS.RATING.name]: {
    dynamic: {
      value: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
  },
  [LAYOUT_TEMPLATE.SLIDE_IN.name]: {
    positionSettings: {
      positionSuffix: 'px',
      linkedPositionInput: false,
      top: '0',
      right: '0',
      bottom: '0',
      left: '0',
    },
  },
  [LAYOUT_TEMPLATE.FLOATING_BAR.name]: {
    positionSettings: {
      positionSuffix: 'px',
      linkedPositionInput: false,
      top: 'auto',
      right: 'auto',
      bottom: '0',
      left: '0',
    },
  },
  [STANDARDS_BLOCKS.COUNT_DOWN.name]: {
    settings: {
      countdownStyle: 'grouping',
      layout: 'horizontal',
      separator: 'none',
    },
    separatorStyles: {
      color: '#000000',
      fontFamily: 'Arial',
      fontWeight: 500,
      fontSize: '12px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
    },
  },
  [STANDARDS_BLOCKS.OTP_VERIFICATION.name]: {
    dynamic: {
      value: {
        isDynamic: false,
      },
    },
    defaultDynamicIndex: 1,
  },
  settings: {
    delayShowing: {
      enable: false,
      conditions: [{ type: 'percented_scroll', value: 20 }],
    },
    autoCloseTemplate: {
      enable: false,
      at: 'template',
      after: 10,
    },
  },
};

export const VIEW_SETTING_DEFAULT = {
  isActive: true,
  global: true,
  container: {
    linkedMarginInput: true,
    marginSuffix: 'px',
    linkedPaddingInput: false,
    paddingSuffix: 'px',
    borderRadiusStyle: 'none',
    borderRadiusSuffix: 'px',
    linkedBorderRadiusInput: false,
    gradientType: 'linear-gradient',
    gradients: [
      {
        gradientColor: '#d11a66',
        gradientColorLocation: 0,
      },
      {
        gradientColor: '#ffbd64',
        gradientColorLocation: 72,
      },
    ],
    radialPosition: 'left top',
    linearAngle: 331,
    boxShadowStyle: 'none',
    boxShadowColor: 'rgba(0, 0, 0, 0.5)',
    boxShadowBlur: '0px',
    boxShadowSpread: '0px',
    boxShadowHorizontal: '0px',
    boxShadowVertical: '0px',
    boxShadowInset: false,
    overflowHidden: false,
    overlayColor: 'rgba(0, 0, 0, 0)',
    containerWidth: 700,
    containerWidthSuffix: 'px',
    imageLayerFirst: true,
    linkedBorderWidthInput: true,
    backgroundColor: 'rgba(0, 0, 0, 0)',
    backgroundColorStyle: 'color',
    backgroundPosition: 'center bottom',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'contain',
    styles: {
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '20px',
      paddingRight: '0px',
      paddingLeft: '0px',
      paddingBottom: '20px',
      background: 'rgba(0, 0, 0, 0)',
      borderColor: '#ffffff',
      borderStyle: 'none',
      boxShadow: 'none',
    },
    backgroundImageObj: {
      name: '',
      previewUrl: '',
    },
    containerSize: '',
  },
  closeButton: {
    paddingTop: '5px',
    paddingRight: '5px',
    paddingLeft: '5px',
    paddingBottom: '5px',
    marginTop: '10px',
    marginRight: '10px',
    marginLeft: '10px',
    marginBottom: '10px',
    borderRadiusStyle: 'none',
    borderRadiusSuffix: 'px',
    linkedBorderRadiusInput: true,
    linkedPaddingInput: true,
    paddingSuffix: 'px',
    linkedMarginInput: false,
    marginSuffix: 'px',
    boxShadowStyle: 'custom',
    boxShadowColor: '#333333',
    boxShadowBlur: '0px',
    boxShadowSpread: '0px',
    boxShadowHorizontal: '0px',
    boxShadowVertical: '0px',
    boxShadowInset: false,
    closeButtonActive: false,
    buttonTitle: 'Close',
    buttonStyle: 'circle',
    buttonSize: '14px',
    buttonWeight: 'regular',
    buttonPosition: 'top-right',
    iconColor: '#ffffff',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    hoverIconColor: '#ffffff',
    hoverBackgroundColor: 'rgba(0, 0, 0, 0.2)',
    boxShadow: '0px 0px 0px 0px #33333',
  },
  openButton: {
    paddingTop: '0px',
    paddingRight: '0px',
    paddingLeft: '0px',
    paddingBottom: '0px',
    marginTop: '0px',
    marginRight: '0px',
    marginLeft: '0px',
    marginBottom: '0px',
    linkedPaddingInput: true,
    paddingSuffix: 'px',
    linkedMarginInput: true,
    marginSuffix: 'px',
    openButtonActive: true,
    buttonSize: '22px',
    buttonWeight: 'regular',
    iconColor: '#60656f',
  },
  customCSS: {
    applyCssPrefix: false,
    rawEditorOutput: '',
  },
  customJS: {
    rawEditorOutput: '',
  },
  slideClosedContainer: {
    linkedPaddingInput: true,
    linkedMarginInput: true,
    position: 'right',
    slideDirection: 'replace',
    displayStyle: 'text',
    paddingSuffix: 'px',
    marginSuffix: 'px',
    borderRadiusStyle: 'none',
    borderRadiusSuffix: 'px',
    linkedBorderRadiusInput: true,
    gradientType: 'linear-gradient',
    gradients: [
      {
        gradientColor: '#ffffff',
        gradientColorLocation: 35,
      },
      {
        gradientColor: '#e8e8e8',
        gradientColorLocation: 100,
      },
    ],
    radialPosition: 'center center',
    linearAngle: 180,
    boxShadowStyle: 'none',
    boxShadowColor: '#ffffff',
    boxShadowBlur: '3px',
    boxShadowSpread: '0px',
    boxShadowHorizontal: '0px',
    boxShadowVertical: '0px',
    boxShadowInset: false,
    toggleClosedText: 'Learn more about us!',
    linkedBorderWidthInput: true,
    backgroundColor: '#0D82DF',
    backgroundColorStyle: 'color',
    icon: 'fas bell',
    iconColor: '#ffffff',
    iconSize: 60,
    iconSpacing: 3,
    positionSettings: {
      positionSuffix: 'px',
      linkedPositionInput: false,
      top: '0',
      right: '0',
      bottom: '0',
      left: '0',
    },
    contentAnimation: {
      animationType: 'none',
      animationDelay: 0,
      animationDuration: 1000,
      animationIterationStyle: 'infinite',
      animationIterationCount: 1,
    },
    toggleAnimation: {
      animationType: 'none',
      animationDelay: 0,
      animationDuration: 1000,
      animationIterationStyle: 'infinite',
      animationIterationCount: 1,
    },
    styles: {
      borderTopWidth: '1px',
      borderRightWidth: '1px',
      borderBottomWidth: '1px',
      borderLeftWidth: '1px',
      borderTopLeftRadius: '3px',
      borderTopRightRadius: '3px',
      borderBottomRightRadius: '3px',
      borderBottomLeftRadius: '3px',
      paddingTop: '5px',
      paddingRight: '5px',
      paddingLeft: '5px',
      paddingBottom: '5px',
      marginTop: '0px',
      marginRight: '0px',
      marginLeft: '0px',
      marginBottom: '0px',
      background: '#0D82DF',
      borderColor: '#b1bacb',
      borderStyle: 'solid',
      cursor: 'pointer',
      userSelect: 'none',
      fontSize: '16px',
      fontStyle: 'normal',
      fontWeight: 400,
      fontFamily: 'Open Sans',
      lineHeight: '1.4',
      letterSpacing: '0',
      textTransform: 'none',
      textDecoration: 'none',
      color: '#ffffff',
    },
    viewMinimized: false,
  },
  fullscreenContainer: {
    gradientType: 'linear-gradient',
    gradients: [
      {
        gradientColor: '#d11a66',
        gradientColorLocation: 0,
      },
      {
        gradientColor: '#ffbd64',
        gradientColorLocation: 72,
      },
    ],
    radialPosition: 'center center',
    linearAngle: 331,
    imageLayerFirst: true,
    linkedBorderWidthInput: true,
    backgroundColor: 'rgba(0, 0, 0, 0)',
    backgroundColorStyle: 'color',
    backgroundPosition: 'left top',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    styles: {
      borderTopWidth: '0',
      borderRightWidth: '0',
      borderBottomWidth: '0',
      borderLeftWidth: '0',
      background: 'rgba(0, 0, 0, 0)',
      borderColor: '#b1bacb',
      borderStyle: 'solid',
    },
    backgroundImageObj: {
      name: '',
      previewUrl: '',
    },
  },
  sidebarRootActive: false,
};

export const GLOBAL_SETTINGS_DEFAULT = {
  mobile: 0,
  thumbnailCapture: true,
  interactionCookieType: 'after_x_days',
  interactionCookieDuration: 30,
  successCookieType: 'after_x_days',
  successCookieDuration: 365,
  seenCookieType: 'after_x_days',
  seenCookieDuration: 30,
  crossSubdomainCookies: 0,
  showAffiliateLink: 1,
  floatingBarPosition: 'bottom',
  slideOpen: false,
  autoToggle: 60,
  pageSlide: false,
  enableLock: false,
  lockMethod: 'obfuscate',
  countdown: 0,
  gamified: 1,
  smartSuccess: false,
  chatbot: [],
  analyticsAccountId: 0,
  gaId: '',
  uaId: '',
  attentionActivation: false,
  customColors: [
    '#f7da64',
    '#8912dd',
    '#ed1515',
    '#230439',
    '#d11a66',
    '#ffbd64',
    '#f1ab96',
    '#824ccd',
    '#5858e9',
    '#57b8c2',
  ],
  slideToggleState: false,
  bgClose: false,
  mcTitle: '',
  mcMetaDescription: '',
  mcImageUrl: '',
  enableWebFonts: 1,
  lazyLoadImage: false,
  ...DATA_MIGRATE.settings,
};

export const RESPONSIVE_SETTINGS_DEFAULT = {
  notStackOnMobile: false,
  hideOnDesktop: false,
  hideOnMobile: false,
};

export const VIEW_YES_NO_SETTING_DEFAULT = {
  ...VIEW_SETTING_DEFAULT,
  isActive: false,
};

export const BLOCK_STYLES_DEFAULT: React.CSSProperties = {
  borderTopLeftRadius: '0px',
  borderTopRightRadius: '0px',
  borderBottomRightRadius: '0px',
  borderBottomLeftRadius: '0px',
  borderTopWidth: '0px',
  borderRightWidth: '0px',
  borderBottomWidth: '0px',
  borderLeftWidth: '0px',
  paddingTop: '0px',
  paddingRight: '0px',
  paddingBottom: '0px',
  paddingLeft: '0px',
  marginTop: '0px',
  marginRight: '0px',
  marginBottom: '0px',
  marginLeft: '0px',
  background: 'transparent',
  borderStyle: 'none',
  borderColor: '#000000',
  boxShadow: 'none',
  width: 'auto',
  height: 'auto',
  maxWidth: '100%',
  position: 'relative',
  top: '0px',
  left: '0px',
  right: '0px',
  bottom: '0px',
  zIndex: 0,
};

export const BLOCK_STYLES_SETTINGS_DEFAULT = {
  borderRadiusStyle: 'none',
  borderRadiusSuffix: 'px',
  linkedBorderRadiusInput: true,
  linkedBorderWidthInput: true,
  linkedPaddingInput: false,
  paddingSuffix: 'px',
  linkedMarginInput: true,
  marginSuffix: 'px',
  boxShadowStyle: 'none',
  boxShadowColor: '#ffffff',
  boxShadowBlur: '0px',
  boxShadowSpread: '0px',
  boxShadowHorizontal: '0px',
  boxShadowVertical: '0px',
  boxShadowInset: false,
  backgroundColor: 'transparent',
  backgroundColorStyle: 'color',
  backgroundPosition: 'left top',
  backgroundRepeat: 'no-repeat',
  backgroundSize: 'cover',
  gradientType: 'linear-gradient',
  radialPosition: 'center center',
  linearAngle: 45,
  gradients: [
    {
      gradientColor: '#ffffff',
      gradientColorLocation: 0,
    },
    {
      gradientColor: '#000000',
      gradientColorLocation: 50,
    },
  ],
  customId: '',
  customClass: '',
  heightSuffix: 'auto',
  hidden: false,
  displayCondition: {
    condition: '',
    operator: '',
    value: '',
    type: '',
    attribute: {},
  },
};

export const BLOCK_HOVER_STYLES_SETTINGS_DEFAULT = {
  ...BLOCK_STYLES_SETTINGS_DEFAULT,
  editHover: false,
};

export const BUTTON_STYLES_DEFAULT = {
  borderTopLeftRadius: '100px',
  borderTopRightRadius: '100px',
  borderBottomRightRadius: '100px',
  borderBottomLeftRadius: '100px',
  borderTopWidth: '1px',
  borderRightWidth: '1px',
  borderBottomWidth: '1px',
  borderLeftWidth: '1px',
  paddingTop: '7px',
  paddingRight: '14px',
  paddingBottom: '7px',
  paddingLeft: '14px',
  marginTop: '0px',
  marginRight: '0px',
  marginBottom: '0px',
  marginLeft: '0px',
  color: '#230439',
  fontFamily: 'Quicksand',
  fontWeight: 900,
  fontSize: '18px',
  textTransform: 'none',
  textDecoration: 'none',
  lineHeight: '1',
  letterSpacing: '0px',
  fontStyle: 'normal',
  background: '#ffffff',
  borderStyle: 'none',
  borderColor: 'rgba(0,0,0,.35)',
  boxShadow: 'none',
  maxWidth: '100%',
  height: '100%',
  width: '50%',
  textAlign: 'center',
};

export const INPUT_FIELD_STYLE_DEFAULT = {
  borderTopLeftRadius: '8px',
  borderTopRightRadius: '8px',
  borderBottomRightRadius: '8px',
  borderBottomLeftRadius: '8px',
  borderTopWidth: '1px',
  borderRightWidth: '1px',
  borderBottomWidth: '1px',
  borderLeftWidth: '1px',
  paddingTop: '0px',
  paddingRight: '0px',
  paddingBottom: '0px',
  paddingLeft: '0px',
  marginTop: '0px',
  marginRight: '0px',
  marginBottom: '15px',
  marginLeft: '0px',
  color: '#230439',
  fontFamily: 'Quicksand',
  fontWeight: 900,
  fontSize: '18px',
  textTransform: 'none',
  textDecoration: 'none',
  lineHeight: '1',
  letterSpacing: '0px',
  fontStyle: 'normal',
  background: 'transparent',
  borderStyle: 'solid',
  borderColor: '#005FB8',
  boxShadow: 'none',
  maxWidth: '100%',
  height: '80px',
  width: '80px',
  textAlign: 'center',
};

export const BUTTON_SETTINGS_DEFAULT = {
  borderRadiusStyle: 'capsule_round',
  borderRadiusSuffix: 'px',
  linkedBorderRadiusInput: true,
  linkedBorderWidthInput: true,
  linkedPaddingInput: true,
  paddingSuffix: 'px',
  widthSuffix: '%',
  linkedMarginInput: false,
  marginSuffix: 'px',
  boxShadowStyle: 'none',
  boxShadowColor: 'rgba(0, 0, 0, 0.5)',
  boxShadowBlur: '0px',
  boxShadowSpread: '0px',
  boxShadowHorizontal: '0px',
  boxShadowVertical: '0px',
  boxShadowInset: false,
  backgroundColor: '#ffffff',
  backgroundColorStyle: 'color',
  gradientType: 'linear-gradient',
  radialPosition: 'center center',
  linearAngle: 45,
  gradients: [
    {
      gradientColor: '#ffffff',
      gradientColorLocation: 0,
    },
    {
      gradientColor: '#000000',
      gradientColorLocation: 50,
    },
  ],
  buttonValue: 'Start Now',
  buttonSize: 'medium',
  editIcon: false,
  icon: '',
  iconColor: 'rgba(0, 0, 0)',
  iconSize: 28,
  iconAfter: false,
  iconSpacing: '3',
};

export const BUTTON_HOVER_STYLES_DEFAULT = {
  borderTopLeftRadius: '100px',
  borderTopRightRadius: '100px',
  borderBottomRightRadius: '100px',
  borderBottomLeftRadius: '100px',
  borderTopWidth: '1px',
  borderRightWidth: '1px',
  borderBottomWidth: '1px',
  borderLeftWidth: '1px',
  color: '#ffffff',
  fontFamily: 'Quicksand',
  fontWeight: 900,
  fontSize: '24px',
  textTransform: 'none',
  textDecoration: 'none',
  lineHeight: '1',
  letterSpacing: '0px',
  fontStyle: 'normal',
  background: '#ffffff',
  borderStyle: 'none',
  borderColor: 'rgba(0,0,0,.35)',
  boxShadow: '0px 0px 0px 0px rgba(0, 0, 0, 0.3)',
  maxWidth: '100%',
  paddingTop: '16px',
  paddingRight: '16px',
  paddingBottom: '16px',
  paddingLeft: '16px',
  marginTop: '0px',
  marginRight: '0px',
  marginBottom: '0px',
  marginLeft: '0px',
  width: '88%',
  textAlign: 'center',
};

export const BUTTON_HOVER_SETTINGS_DEFAULT = {
  boxShadowStyle: 'custom',
  boxShadowColor: 'rgba(0, 0, 0, 0.3)',
  boxShadowBlur: '0px',
  boxShadowSpread: '0px',
  boxShadowHorizontal: '0px',
  boxShadowVertical: '0px',
  boxShadowInset: false,
  backgroundColor: '#ffffff',
  backgroundColorStyle: 'color',
  gradientType: 'linear-gradient',
  radialPosition: 'center center',
  linearAngle: 45,
  gradients: [
    {
      gradientColor: '#ffffff',
      gradientColorLocation: 0,
    },
    {
      gradientColor: '#000000',
      gradientColorLocation: 50,
    },
  ],
  editHover: false,
  editIcon: false,
  icon: '',
  iconColor: '#ffffff',
  iconSize: 28,
  iconAfter: false,
  iconSpacing: '3',
  borderRadiusStyle: 'capsule_round',
  borderRadiusSuffix: 'px',
  linkedBorderRadiusInput: true,
  linkedBorderWidthInput: true,
  linkedPaddingInput: true,
  paddingSuffix: 'px',
  linkedMarginInput: false,
  marginSuffix: 'px',
  customId: '',
  customClass: '',
  buttonSize: 'small',
};

export const SURPRISE_TREASURE_HUNT_STYLE_DEFAULT = {
  borderTopLeftRadius: '0px',
  borderTopRightRadius: '0px',
  borderBottomRightRadius: '0px',
  borderBottomLeftRadius: '0px',
  borderTopWidth: '0px',
  borderRightWidth: '0px',
  borderBottomWidth: '0px',
  borderLeftWidth: '0px',
  paddingTop: '0px',
  paddingRight: '0px',
  paddingBottom: '0px',
  paddingLeft: '0px',
  marginTop: '0px',
  marginRight: '0px',
  marginBottom: '0px',
  marginLeft: '0px',
  background: 'transparent',
  borderStyle: 'none',
  borderColor: 'rgba(0,0,0,.35)',
  boxShadow: 'none',
  maxWidth: '100%',
  height: '100%',
};

export const SURPRISE_TREASURE_HUNT_SETTINGS_DEFAULT = {
  borderRadiusStyle: 'none',
  borderRadiusSuffix: 'px',
  linkedBorderRadiusInput: true,
  linkedBorderWidthInput: true,
  linkedPaddingInput: true,
  paddingSuffix: 'px',
  widthSuffix: '%',
  linkedMarginInput: false,
  marginSuffix: 'px',
  boxShadowStyle: 'none',
  boxShadowColor: 'rgba(0, 0, 0, 0.5)',
  boxShadowBlur: '0px',
  boxShadowSpread: '0px',
  boxShadowHorizontal: '0px',
  boxShadowVertical: '0px',
  boxShadowInset: false,
  backgroundColor: 'transparent',
  backgroundColorStyle: 'color',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center center',
  backgroundSize: 'cover',
  gradientType: 'linear-gradient',
  radialPosition: 'center center',
  linearAngle: 45,
  gradients: [
    {
      gradientColor: '#ffffff',
      gradientColorLocation: 0,
    },
    {
      gradientColor: '#000000',
      gradientColorLocation: 50,
    },
  ],
};

export const BREAK_POINTS_DEFAULT = {
  [BREAK_POINT_KEYS.SM]: {
    blockStyles: {},
    blockStylesSettings: {},
    blockHoverStyles: {},
    blockHoverStylesSettings: {},
  },
};

export const BLOCK_SETTING_DEFAULT = {
  ROW: {
    type: 'one',
    widths: [100],
    name: '',
    fromIndex: 1,
    blockStyles: { ...BLOCK_STYLES_DEFAULT, alignItems: 'flex-start' },
    blockStylesSettings: {
      ...BLOCK_STYLES_SETTINGS_DEFAULT,
      columnGap: 0,
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
    blockHoverStyles: { ...BLOCK_STYLES_DEFAULT, alignItems: 'flex-start' },
    blockHoverStylesSettings: {
      ...BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
      columnGap: 0,
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
    responsiveSettings: RESPONSIVE_SETTINGS_DEFAULT,
  },
  COLUMN: {
    width: 100,
    defaultIndex: 1,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: {
      ...BLOCK_STYLES_SETTINGS_DEFAULT,
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: {
      ...BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
  },
  TEXT: {
    type: 'text',
    name: 'Text',
    altText: '',
    component: 'TextElement',
    editor: 'TextElementEditor',
    rawHTML: `<p>${getTranslateMessage(translations.textBlock.defaultValue)}</p>`,
    styles: {
      color: '#000000',
      fontFamily: 'Montserrat',
      fontWeight: 400,
      fontSize: '16px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
      textAlign: 'left',
    },
    linkStyles: {
      color: '#1e5494',
      textDecoration: 'underline',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    textStyling: {
      selectLineDisplay: '',
      isEllipsisText: true,
    },
    ...DATA_MIGRATE[STANDARDS_BLOCKS.TEXT.name],
  },
  IMAGE: {
    type: 'image',
    name: 'Image',
    component: 'ImageElement',
    editor: 'ImageElementEditor',
    altText: '',
    outerContainerStyles: {
      textAlign: 'left',
    },
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      borderStyle: 'solid',
      borderColor: '#ffffff',
      boxShadow: 'none',
      opacity: 1,
      width: '100%',
      height: 'auto',
      objectPosition: 'center center',
      objectFit: 'unset',
    },
    stylesSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      widthSuffix: '%',
      heightSuffix: 'auto',
    },
    uploadedImage: {
      previewUrl: false,
    },
    linkedImage: false,
    linkedUrl: '',
    linkedTarget: '_blank',
    linkedTracking: false,
    linkedNoFollow: false,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    dimensions: {
      width: 0,
      height: 0,
    },
    ...DATA_MIGRATE[STANDARDS_BLOCKS.IMAGE.name],
  },
  HTML: {
    type: 'html',
    name: 'HTML',
    component: 'HtmlElement',
    editor: 'HtmlElementEditor',
    showModal: false,
    htmlAreaValue: {
      value: '<div>\n\t<p>Add HTML here.</p>\n</div>',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  HTML_CODE_MODE: {
    type: 'html',
    name: 'HTML',
    component: 'HtmlElement',
    editor: 'HtmlElementEditor',
    showModal: false,
    htmlAreaValue: {
      value: '<div>\n\t<p>Add HTML here.</p>\n</div>',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
  },
  ICONS: {
    type: 'icons',
    name: 'Icons',
    component: 'IconsElement',
    editor: 'IconsElementEditor',
    icon: 'fab angellist',
    outerContainerStyles: {
      textAlign: 'center',
    },
    iconContainerStyles: {
      paddingTop: '5px',
      paddingRight: '5px',
      paddingBottom: '5px',
      paddingLeft: '5px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      backgroundColor: '#0d82df',
      borderTopLeftRadius: '200px',
      borderTopRightRadius: '200px',
      borderBottomRightRadius: '200px',
      borderBottomLeftRadius: '200px',
      lineHeight: '44px',
      width: '48px',
      height: '48px',
      fontSize: 24,
      borderColor: '#000000',
      borderStyle: 'solid',
    },
    iconContainerStylesSetting: {
      borderRadiusStyle: 'custom',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      linkedPaddingInput: false,
      paddingSuffix: 'px',
      linkedMarginInput: true,
      marginSuffix: 'px',
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      backgroundColor: 'transparent',
      backgroundColorStyle: 'color',
      backgroundPosition: 'right top',
      backgroundRepeat: 'repeat',
      backgroundSize: 'contain',
      gradientType: 'linear-gradient',
      radialPosition: 'center center',
      linearAngle: 45,
      customId: '',
      customClass: '',
    },
    iconStyles: {
      color: '#ffffff',
    },
    linkedIcon: false,
    linkedUrl: '',
    linkedTarget: '_blank',
    linkedTracking: false,
    linkedNoFollow: false,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  SPACER: {
    type: 'spacer',
    name: 'Spacer',
    component: 'SpacerElement',
    editor: 'SpacerElementEditor',
    styles: {
      height: '30px',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  DIVIDER: {
    type: 'divider',
    name: 'Divider',
    component: 'DividerElement',
    editor: 'DividerElementEditor',
    widthSuffix: '%',
    styles: {
      borderTopWidth: '1px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      borderStyle: 'solid',
      borderColor: '#000000',
      width: '85%',
      display: 'inline-block',
    },
    outerContainerStyles: {
      textAlign: 'center',
      paddingTop: '0px',
      paddingRight: '0px',
      paddingBottom: '0px',
      paddingLeft: '0px',
    },
    blockStyles: { ...BLOCK_STYLES_DEFAULT, paddingTop: '10px', paddingBottom: '10px' },
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: { ...BLOCK_STYLES_DEFAULT, paddingTop: '10px', paddingBottom: '10px' },
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  COUPON_WHEEL: {
    type: 'coupon_wheel',
    name: 'Coupon Wheel',
    component: 'CouponWheelElement',
    editor: 'CouponWheelElementEditor',
    premium: true,
    actions: {
      CouponWheelElement: {
        event: 'wheel',
        scripts: '',
        type: '',
        options: {
          name: '',
          track: false,
          url: '',
          pass: false,
          close: false,
          phone: '',
          copy: '',
          spinDuration: 1e4,
        },
      },
    },
    sections: [
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        canWin: false,
        winChance: 0,
        pool: true,
        couponCode: '',
        couponCodeAttr: '',
        limitSpinning: {
          type: 'out_of_code',
        },
        label: 'Not quite',
        saved: false,
        internalCode: 'not_quite',
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        canWin: true,
        winChance: 12.5,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off',
        saved: false,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        canWin: true,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: 'Free shipping',
        internalCode: 'free_shipping_1',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        canWin: true,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_1',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        canWin: false,
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'no_luck_today',
        saved: false,
        label: 'No luck today',
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        canWin: true,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_2',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        canWin: false,
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'spin_again',
        saved: false,
        label: 'Spin again',
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        canWin: true,
        pool: false,
        limitSpinning: {
          type: 'unlimited',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_3',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        canWin: true,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '25% Off',
        internalCode: '25_percent_off',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        canWin: true,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: 'Free shipping',
        internalCode: 'free_shipping_2',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        canWin: false,
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'almost',
        saved: false,
        label: 'Almost',
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        canWin: true,
        pool: false,
        limitSpinning: {
          type: 'unlimited',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '25% Off',
        internalCode: '25_percent_off_3',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
      },
    ],
    sectionColors: ['#b5d08d', '#79c5df', '#2e404f', '#c1e3f5'],
    outerWheelColor: '#fff',
    innerWheelColor: '#fff',
    flipperColor: '#fff',
    wheelHeight: 50,
    pullDirection: 0,
    labelStyles: {
      color: '#fff',
      fontFamily: 'Arial',
      fontWeight: 500,
      fontSize: '20px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '3',
      letterSpacing: '0',
      fontStyle: 'normal',
    },
    innerWheelImage: {
      previewUrl: '',
    },
    innerWheelIcon: {
      icon: '',
      iconColor: '#000',
    },
    errorMessage: {
      position: 'bottom',
      message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  BUTTON: {
    type: 'button',
    name: 'Button',
    component: 'ButtonElement',
    editor: 'ButtonElementEditor',
    actions: {
      ButtonElement: {
        event: 'click',
        scripts: '',
        type: 'view',
        options: {
          name: '',
          track: true,
          url: '',
          pass: false,
          close: false,
          phone: '',
          copy: '',
        },
      },
    },
    buttonStyles: BUTTON_STYLES_DEFAULT,
    buttonSettings: BUTTON_SETTINGS_DEFAULT,
    buttonHoverStyles: BUTTON_HOVER_STYLES_DEFAULT,
    buttonHoverSettings: BUTTON_HOVER_SETTINGS_DEFAULT,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    ...DATA_MIGRATE[STANDARDS_BLOCKS.BUTTON.name],
  },
  VIDEO: {
    type: 'video',
    name: 'Video',
    component: 'VideoElement',
    editor: 'VideoElementEditor',
    rawURL: 'https://www.youtube.com/watch?v=SuSxN05neGo',
    videoSrc: 'https://www.youtube.com/watch?v=SuSxN05neGo',
    aspectRatio: '56.25',
    styles: {
      textAlign: 'center',
      width: '100%',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    ...DATA_MIGRATE[STANDARDS_BLOCKS.VIDEO.name],
  },
  COUNT_DOWN: {
    type: 'countdown',
    name: 'Countdown',
    component: 'CountdownElement',
    editor: 'CountdownElementEditor',
    premium: true,
    settings: {
      type: 'dynamic',
      timeZone: '',
      static: {
        endDate: '2022-03-01',
        endTime: '08:30 AM',
        epoch: 1646901445000,
      },
      dynamic: {
        days: 7,
        hours: 0,
        minutes: 0,
        seconds: 0,
        timeInSeconds: 604800,
      },
      display: {
        type: 'long',
        position: 'bottom',
        days: true,
        hours: true,
        minutes: true,
        seconds: true,
        customLabels: {
          days: 'Days',
          hours: 'Hours',
          minutes: 'Minutes',
          seconds: 'Seconds',
        },
      },
      ...DATA_MIGRATE[STANDARDS_BLOCKS.COUNT_DOWN.name].settings,
    },
    actions: {
      CountdownElement: {
        event: 'countdown',
        scripts: '',
        type: 'noaction',
        options: {
          name: 'success',
          track: false,
          url: '',
          pass: false,
          close: true,
        },
      },
    },
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      paddingTop: '5px',
      paddingRight: '5px',
      paddingBottom: '5px',
      paddingLeft: '5px',
      marginTop: '0px',
      marginRight: '0px',
      marginBottom: '0px',
      marginLeft: '0px',
      background: 'transparent',
      borderStyle: 'solid',
      borderColor: '#000000',
      boxShadow: 'none',
      width: 'auto',
      maxWidth: '100%',
      minWidth: '40px',
      display: 'inline-block',
      textAlign: 'center',
      zIndex: 0,
    },
    stylesSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      linkedPaddingInput: true,
      paddingSuffix: 'px',
      linkedMarginInput: true,
      marginSuffix: 'px',
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      backgroundColor: 'transparent',
      backgroundColorStyle: 'color',
      gradientType: 'linear-gradient',
      radialPosition: 'center center',
      linearAngle: 45,
      gradients: [
        {
          gradientColor: '#ffffff',
          gradientColorLocation: 0,
        },
        {
          gradientColor: '#000000',
          gradientColorLocation: 50,
        },
      ],
      backgroundImageObj: {
        lastModified: '',
        name: '',
        previewUrl: '',
        type: '',
        provider: '',
      },
      backgroundPosition: 'left top',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
    },
    numberStyles: {
      color: '#000000',
      fontFamily: 'Arial',
      fontWeight: 500,
      fontSize: '30px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
    },
    unitStyles: {
      color: '#000000',
      fontFamily: 'Arial',
      fontWeight: 500,
      fontSize: '12px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
    },
    separatorStyles: DATA_MIGRATE[STANDARDS_BLOCKS.COUNT_DOWN.name].separatorStyles,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  YES_NO: {
    type: 'yes_no',
    name: 'Yes/No',
    component: 'YesNoElement',
    editor: 'YesNoElementEditor',
    premium: true,
    displayInline: false,
    swapButtons: false,
    actions: {
      YesButtonElement: {
        event: 'click',
        scripts: '',
        type: 'view',
        options: {
          name: '',
          track: false,
          url: '',
          pass: false,
          close: false,
        },
      },
      NoButtonElement: {
        event: 'click',
        scripts: '',
        type: 'close',
        options: {
          name: '',
          track: false,
          url: '',
          pass: false,
          close: false,
        },
      },
    },
    yesButtonStyles: BUTTON_STYLES_DEFAULT,
    yesButtonSettings: {
      ...BUTTON_SETTINGS_DEFAULT,
      buttonValue: 'Yes',
    },
    yesButtonHoverStyles: BUTTON_HOVER_STYLES_DEFAULT,
    yesButtonHoverSettings: BUTTON_HOVER_SETTINGS_DEFAULT,
    noButtonStyles: {
      ...BUTTON_STYLES_DEFAULT,
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      paddingTop: '0px',
      paddingRight: '12px',
      paddingBottom: '8px',
      paddingLeft: '12px',
      marginTop: '10px',
      background: 'transparent',
      borderStyle: 'solid',
      boxShadow: '0px 0px 0px 0px #ffffff',
    },
    noButtonSettings: {
      ...BUTTON_SETTINGS_DEFAULT,
      borderRadiusStyle: 'none',
      linkedPaddingInput: false,
      boxShadowStyle: 'custom',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      backgroundColor: 'transparent',
      buttonValue: 'No',
      iconColor: '',
      iconSize: '',
      iconSpacing: '',
    },
    noButtonHoverStyles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      color: '#ffffff',
      fontFamily: 'Dosis',
      fontWeight: 600,
      fontSize: '16px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
      background: 'transparent',
      borderStyle: 'solid',
      borderColor: 'rgba(0,0,0,.35)',
      boxShadow: '0px 0px 0px 0px #ffffff',
      maxWidth: '100%',
      paddingTop: '0px',
      paddingRight: '12px',
      paddingBottom: '8px',
      paddingLeft: '12px',
    },
    noButtonHoverSettings: {
      ...BUTTON_HOVER_SETTINGS_DEFAULT,
      boxShadowColor: '#ffffff',
      backgroundColor: 'transparent',
      iconColor: '',
      iconSize: '',
      iconSpacing: '',
      borderRadiusStyle: 'none',
      linkedPaddingInput: false,
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    ...DATA_MIGRATE[STANDARDS_BLOCKS.YES_NO.name],
  },
  OPTIN_FIELDS: {
    type: 'optinfields',
    name: 'Optin Fields',
    component: 'FieldsElement',
    editor: 'FieldsElementEditor',
    customHtmlEnabled: false,
    syncData: {
      columnMapping: [],
      isAction: false,
      event: {},
      source: {},
    },
    customHtmlString: '',
    displayInline: false,
    privacyTextLast: true,
    privacyStyles: {
      color: '#000000',
      fontFamily: 'Arial',
      fontWeight: 400,
      fontSize: '16px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
      textAlign: 'left',
      maxWidth: '100%',
    },
    privacyStyleSettings: {
      linkedMarginInput: true,
      marginSuffix: 'px',
    },
    buttonStyles: BUTTON_STYLES_DEFAULT,
    buttonSettings: {
      ...BUTTON_SETTINGS_DEFAULT,
      buttonValue: 'Sign up',
      marginAlign: 'center',
    },
    buttonHoverStyles: BUTTON_HOVER_STYLES_DEFAULT,
    buttonHoverSettings: BUTTON_HOVER_SETTINGS_DEFAULT,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    fields: {
      ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].fields,
      nameInput: {
        id: 'nameInput',
        inputName: 'name',
        name: getTranslateMessage(translations.optinFields.fields.nameField.title),
        type: 'text',
        order: 0,
        placeholder: getTranslateMessage(translations.optinFields.fields.nameField.placeholder),
        inputWidth: '80%',
        fieldWidth: '100%',
        required: true,
        label: getTranslateMessage(translations.optinFields.fields.nameField.title),
        displayLabel: true,
        alignField: 'horizontal',
        nameField: getTranslateMessage(translations.optinFields.fields.nameField.title),
        requireErrorMessage: '',
        errorText: initialsErrorMessage('name'),
        invalidText: '',
        isShowField: true,
      },
      emailInput: {
        id: 'emailInput',
        inputName: 'email',
        name: getTranslateMessage(translations.optinFields.fields.emailField.title),
        type: 'email',
        order: 1,
        placeholder: getTranslateMessage(translations.optinFields.fields.emailField.placeholder),
        inputWidth: '80%',
        fieldWidth: '100%',
        required: true,
        label: getTranslateMessage(translations.optinFields.fields.emailField.title),
        displayLabel: true,
        alignField: 'horizontal',
        nameField: getTranslateMessage(translations.optinFields.fields.emailField.title),
        requireErrorMessage: '',
        errorText: initialsErrorMessage('email'),
        invalidText: 'Invalid email format',
        isShowField: true,
      },
      phoneInput: {
        id: 'phoneInput',
        inputName: 'phone',
        name: getTranslateMessage(translations.optinFields.fields.phoneField.title),
        type: 'tel',
        order: -1,
        placeholder: getTranslateMessage(translations.optinFields.fields.phoneField.placeholder),
        inputWidth: '80%',
        fieldWidth: '100%',
        phoneValidationEnabled: false,
        required: true,
        label: getTranslateMessage(translations.optinFields.fields.phoneField.title),
        displayLabel: true,
        alignField: 'horizontal',
        nameField: getTranslateMessage(translations.optinFields.fields.phoneField.title),
        useCountryCode: true,
        countryCodeDefault: {
          value: 'VN',
          code: '84',
        },
        invalidText: getTranslateMessage(translations.invalidMessagePhone.title),
        errorText: initialsErrorMessage('phone'),
        isShowField: true,
      },
      privacyText: {
        id: 'privacyText',
        inputName: 'accept_privacy',
        name: getTranslateMessage(translations.optinFields.fields.privacyNotice.title),
        order: -1,
        rawText: '<span style="font-family: Arial;">We promise not to use your email for spam.</span>',
        darkMode: false,
        inputWidth: '80%',
        fieldWidth: '100%',
        showCheckbox: false,
        privacyError: 'You must check the box to agree to the terms and conditions.',
        errorText: 'You must check the box to agree to the terms and conditions.',
        label: getTranslateMessage(translations.optinFields.fields.privacyNotice.title),
        displayLabel: true,
        alignField: 'horizontal',
        nameField: getTranslateMessage(translations.optinFields.fields.privacyNotice.title),
        required: true,
        isShowField: true,
      },
      submitButton: {
        id: 'submitButton',
        name: getTranslateMessage(translations.submitButton.title),
        order: 2,
        buttonValue: 'Sign Up',
        inputWidth: '100%',
      },
    },
    actions: {
      FieldsElementButton: {
        event: 'submit',
        scripts: '',
        type: 'view',
        options: {
          name: 'success',
          track: true,
          url: '',
          pass: false,
        },
      },
    },
    styles: {
      borderTopWidth: '1px',
      borderRightWidth: '1px',
      borderBottomWidth: '1px',
      borderLeftWidth: '1px',
      borderTopLeftRadius: '3px',
      borderTopRightRadius: '3px',
      borderBottomRightRadius: '3px',
      borderBottomLeftRadius: '3px',
      marginTop: '0px',
      marginRight: '0px',
      marginBottom: '10px',
      marginLeft: '0px',
      paddingTop: '8px',
      paddingRight: '12px',
      paddingBottom: '8px',
      paddingLeft: '12px',
      color: '#60656f',
      fontFamily: 'Arial',
      fontWeight: 400,
      fontSize: '16px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
      background: '#ffffff',
      boxShadow: 'none',
      borderStyle: 'solid',
      borderColor: 'rgba(0,0,0,.35)',
      display: 'block',
      maxWidth: '100%',
    },
    stylesSettings: {
      backgroundColor: '#ffffff',
      backgroundColorStyle: 'color',
      gradientType: 'linear-gradient',
      radialPosition: 'center center',
      linearAngle: 45,
      displayLabels: false,
      gradients: [
        {
          gradientColor: '#ffffff',
          gradientColorLocation: 0,
        },
        {
          gradientColor: '#000000',
          gradientColorLocation: 50,
        },
      ],
      borderRadiusStyle: 'custom',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      linkedPaddingInput: false,
      paddingSuffix: 'px',
      linkedMarginInput: false,
      marginSuffix: 'px',
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      inputSize: 'small-rounded',
      placeholderColor: '#b1bacb',
      marginAlign: '',
      ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].stylesSettings,
    },
    recaptcha: true,
    dynamic: DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].dynamic,
    limitedSubmit: DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].limitedSubmit,
    verifiedSubmit: DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].verifiedSubmit,
    ...DATA_MIGRATE[STANDARDS_BLOCKS.OPTIN_FIELDS.name].settings,
  },
  SLIDE_SHOW: {
    type: 'slide-show',
    name: 'Slide show',
    component: 'SlideShowElement',
    editor: 'SlideShowElementEditor',
    fromIndex: 1,
    totalItems: 5,
    displayItems: 3,
    displayStyle: 'full',
    columnGap: 20,
    skipItems: 1,
    autoSlide: false,
    slideDelay: 5,
    slideDuration: 1,
    slideLoop: true,
    slideDirection: 'horizontal',
    slideTransition: 'slide',
    actions: {
      SlideShowElement: {
        event: 'slide',
        scripts: '',
        type: '',
        options: {
          name: '',
          track: true,
          url: '',
          pass: false,
        },
      },
    },
    navigationSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedPaddingInput: true,
      paddingSuffix: 'px',
      linkedMarginInput: false,
      marginSuffix: 'px',
      boxShadowStyle: 'custom',
      boxShadowColor: '#333333',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      buttonStyle: 'square',
      buttonPosition: 'middle',
      hoverIconColor: '#ffffff',
      hoverBackgroundColor: '#DFA95EB3',
    },
    navigationStyles: {
      paddingTop: '5px',
      paddingRight: '5px',
      paddingLeft: '5px',
      paddingBottom: '5px',
      marginTop: '10px',
      marginRight: '10px',
      marginLeft: '10px',
      marginBottom: '10px',
      boxShadow: '0px 0px 0px 0px #33333',
      fontSize: '20px',
      color: '#ffffff',
      backgroundColor: '#00000080',
    },
    slideStyles: { ...BLOCK_STYLES_DEFAULT, background: '#ffffff' },
    slideStylesSettings: { ...BLOCK_STYLES_SETTINGS_DEFAULT, backgroundColor: '#ffffff' },
    slideHoverStyles: { ...BLOCK_STYLES_DEFAULT, background: '#ffffff' },
    slideHoverStylesSettings: { ...BLOCK_HOVER_STYLES_SETTINGS_DEFAULT, backgroundColor: '#ffffff' },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  RATING: {
    type: 'rating',
    name: 'Rating',
    component: 'RatingElement',
    editor: 'RatingElementEditor',
    totalItems: 5,
    totalFillItems: 3,
    ratingTypes: 'star',
    size: 24,
    iconStyles: {
      borderColor: '#F7DA64',
    },
    BGBeforeRating: '#FFFFFF',
    BGAfterRating: '#F7DA64',
    outerContainerStyles: {
      textAlign: 'center',
      justify: 'center',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    ...DATA_MIGRATE[STANDARDS_BLOCKS.RATING.name],
  },
  SLIDE: {
    type: 'slide',
    name: 'Slide',
    component: 'SlideElement',
    editor: 'SlideElementEditor',
    defaultIndex: 1,
    blockStylesSettings: {
      hidden: false,
    },
  },
  GROUP: {
    type: 'group',
    name: 'Group',
    component: 'GroupElement',
    editor: 'GroupElementEditor',
    groupSettings: {
      block: {},
      flex: {
        direction: 'row',
        align: 'stretch',
        justify: 'flex-start',
        alignContent: 'stretch',
        gapX: '0px',
        gapY: '0px',
        gapSuffix: 'px',
        linkedGapInput: true,
        wrap: 'nowrap',
      },
    },
    groupStyles: {
      display: 'block',
    },
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
  },
  TABLE: {
    boId: '',
    type: 'table',
    name: 'Table',
    component: 'TableElement',
    editor: 'TableElementEditor',
    columns: [],
    filters: {
      OR: [],
    },
    table: {
      settings: {
        cellPadding: 10,
        cellAlignItems: 'center',
        headerBackground: '#4285f4',
        cellBorderColor: '#cccccc',
        oddRowColor: '#ffffff',
        evenRowColor: '#ffffff',
        showTop: 10,
      },
      styles: {
        margin: 0,
        padding: 0,
        boxSizing: 'border-box',
        borderCollapse: 'collapse',
        width: '100%',
      } as React.CSSProperties,
    },
    sortBy: {
      columnId: '',
      order: 'desc',
    },
    tableHeader: {
      settings: {
        showHeader: true,
        wrapText: false,
      },
      styles: {
        color: '#efefef',
        fontFamily: 'Roboto',
        fontWeight: 400,
        fontSize: '14px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0',
        fontStyle: 'normal',
        textAlign: 'left',
      } as React.CSSProperties,
    },
    tableBody: {
      settings: {
        showMissingDataType: 'blank',
        showRowNumbers: true,
        wrapText: false,
      },
      styles: {
        color: '#616161',
        fontFamily: 'Roboto',
        fontWeight: 400,
        fontSize: '12px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0',
        fontStyle: 'normal',
        textAlign: 'left',
      } as React.CSSProperties,
    },
    tableFooter: {
      settings: {
        linkedBorderWidthInput: true,
      },
      styles: {
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        paddingTop: '20px',
        paddingRight: '0px',
        paddingLeft: '0px',
        paddingBottom: '20px',
        background: 'rgba(0, 0, 0, 0)',
        borderColor: '#ffffff',
        borderStyle: 'none',
        boxShadow: 'none',
      } as React.CSSProperties,
    },
    blockStyles: {
      ...BLOCK_STYLES_DEFAULT,
      alignItems: 'flex-start',
      overflow: 'hidden',
    },
    blockStylesSettings: {
      ...BLOCK_STYLES_SETTINGS_DEFAULT,
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
    blockHoverStyles: {
      ...BLOCK_STYLES_DEFAULT,
      alignItems: 'flex-start',
      overflow: 'hidden',
    },
    blockHoverStylesSettings: {
      ...BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
  },
  SHAKE_AND_WIN: {
    type: 'shake-and-win',
    name: 'Shake and show',
    component: 'ShakeAndWinElement',
    editor: 'ShakeAndWinElementEditor',
    altText: '',
    outerContainerStyles: {
      textAlign: 'left',
    },
    actions: {
      ShakeAndWinElement: {
        event: 'shake-and-win',
        scripts: '',
        type: '',
        options: {
          name: '',
          track: false,
          url: '',
          pass: false,
          close: false,
          phone: '',
          copy: '',
        },
      },
    },
    imageToShake: {
      type: 'use-image-before',
      otherUrl: '',
    },
    timeToShake: 10,
    translationTime: 10,
    shakeType: 'vertical_shake',
    sections: [
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        winChance: 0,
        pool: true,
        couponCode: '',
        couponCodeAttr: '',
        limitSpinning: {
          type: 'out_of_code',
        },
        label: 'Not quite',
        saved: false,
        internalCode: 'not_quite',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        winChance: 12.5,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off',
        saved: false,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: 'Free shipping',
        internalCode: 'free_shipping_1',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_1',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'no_luck_today',
        saved: false,
        label: 'No luck today',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_2',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'spin_again',
        saved: false,
        label: 'Spin again',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        pool: false,
        limitSpinning: {
          type: 'unlimited',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_3',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '25% Off',
        internalCode: '25_percent_off',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: 'Free shipping',
        internalCode: 'free_shipping_2',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'almost',
        saved: false,
        label: 'Almost',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        pool: false,
        limitSpinning: {
          type: 'unlimited',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '25% Off',
        internalCode: '25_percent_off_3',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
    ],
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      borderStyle: 'solid',
      borderColor: '#ffffff',
      boxShadow: 'none',
      opacity: 1,
      width: '100%',
      height: 'auto',
      objectPosition: 'center center',
      objectFit: 'unset',
    },
    triggerSettings: {
      shakeTrigger: 'system',
      by: 'click_on_this_block',
      timeToDelay: 0,
      referral: '',
      reminderNotification: 'Let shake your phone',
    },
    stylesSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      widthSuffix: '%',
      heightSuffix: 'auto',
      marginSuffix: 'px',
      paddingSuffix: 'px',
    },
    uploadedImage: {
      previewUrlBefore: false,
      previewUrlAfter: false,
    },
    linkedImage: false,
    linkedUrl: '',
    linkedTarget: '_blank',
    linkedTracking: false,
    linkedNoFollow: false,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    notificationStyles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      paddingTop: '0px',
      paddingRight: '0px',
      paddingBottom: '0px',
      paddingLeft: '0px',
      marginTop: '0px',
      marginRight: '0px',
      marginBottom: '0px',
      marginLeft: '0px',
      background: 'transparent',
      borderStyle: 'none',
      borderColor: '#000000',
      boxShadow: 'none',
      width: 'max-content',
      maxWidth: '100%',
      zIndex: 1,
      color: '#000000',
      fontSize: '16px',
      fontStyle: 'normal',
      fontFamily: 'Montserrat',
      fontWeight: 400,
      lineHeight: '1',
      letterSpacing: '0',
      textTransform: 'none',
      textDecoration: 'none',
    },
    notificationStylesSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      isDefaultNotification: true,
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      linkedPaddingInput: false,
      paddingSuffix: 'px',
      linkedMarginInput: true,
      marginSuffix: 'px',
      boxShadowStyle: 'none',
      boxShadowColor: '#ffffff',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      backgroundColor: 'transparent',
      backgroundColorStyle: 'color',
      backgroundPosition: 'left top',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
      gradientType: 'linear-gradient',
      radialPosition: 'center center',
      position: 'center bottom',
      linearAngle: 45,
      customId: '',
      customClass: '',
      hidden: false,
      gradients: [
        {
          gradientColor: '#ffffff',
          gradientColorLocation: 0,
        },
        {
          gradientColor: '#000000',
          gradientColorLocation: 50,
        },
      ],
    },
    dimensions: {
      width: 0,
      height: 0,
    },
    dynamic: {
      previewUrl: {
        isDynamic: false,
      },
      altText: {
        isDynamic: false,
      },
      linkedUrl: {
        isDynamic: false,
      },
    },
    errorMessage: {
      position: 'bottom',
      message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
    },
    defaultDynamicIndex: 1,
    // ...DATA_MIGRATE[STANDARDS_BLOCKS.SHAKE_AND_WIN.name],
  },
  SURPRISE_TREASURE_HUNT: {
    type: 'surprise-treasure-hunt',
    name: 'Lucky Gift Box',
    component: 'SurpriseTreasureHuntElement',
    editor: 'SurpriseTreasureHuntElementEditor',
    altText: '',
    outerContainerStyles: {
      justify: 'center',
    },
    cellContainerStyles: {
      justify: 'left',
    },
    imageGiftFirstRender:
      'https://st-media-template.antsomi.com/upload/2023/06/27/75d09e35-d3c7-4604-b069-cff2fa8a20da.png',
    actions: {
      SurpriseTreasureHuntElement: {
        event: 'surprise-treasure-hunt',
        scripts: '',
        type: '',
        options: {
          name: '',
          track: false,
          url: '',
          pass: false,
          close: false,
          phone: '',
          copy: '',
        },
      },
    },
    sections: [
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        winChance: 0,
        pool: true,
        couponCode: '',
        couponCodeAttr: '',
        limitSpinning: {
          type: 'out_of_code',
        },
        label: 'Not quite',
        saved: false,
        internalCode: 'not_quite',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        winChance: 12.5,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off',
        saved: false,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: 'Free shipping',
        internalCode: 'free_shipping_1',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_1',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'no_luck_today',
        saved: false,
        label: 'No luck today',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_2',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'spin_again',
        saved: false,
        label: 'Spin again',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        pool: false,
        limitSpinning: {
          type: 'unlimited',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '10% Off',
        internalCode: '10_percent_off_3',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#b5d08d',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '25% Off',
        internalCode: '25_percent_off',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#79c5df',
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: 'Free shipping',
        internalCode: 'free_shipping_2',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#2e404f',
        winChance: 0,
        pool: true,
        limitSpinning: {
          type: 'out_of_code',
        },
        couponCode: '',
        couponCodeAttr: '',
        internalCode: 'almost',
        saved: false,
        label: 'Almost',
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
      {
        sectionId: random(5),
        backgroundColor: '#c1e3f5',
        pool: false,
        limitSpinning: {
          type: 'unlimited',
        },
        couponCode: '',
        couponCodeAttr: '',
        label: '25% Off',
        internalCode: '25_percent_off_3',
        saved: false,
        winChance: 12.5,
        cappingLevel: 'journey',
        frequency: 'lifetime',
        goToView: 'success',
        canWin: true,
      },
    ],
    styles: SURPRISE_TREASURE_HUNT_STYLE_DEFAULT,
    stylesSettings: SURPRISE_TREASURE_HUNT_SETTINGS_DEFAULT,
    cellStyles: SURPRISE_TREASURE_HUNT_STYLE_DEFAULT,
    cellStylesSettings: SURPRISE_TREASURE_HUNT_SETTINGS_DEFAULT,
    blockStyles: BLOCK_STYLES_DEFAULT,
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    backgroundImageSettings: {
      giftBoxUrl: '',
      cellUrl: '',
    },
    translationTime: 10,
    animationClicked: {
      type: 'shake',
      imagePreviewUrl: '',
    },
    animationHover: 'highlight',
    dimensions: {
      boxes: {
        width: 100,
      },
      cells: {
        gap: 0,
      },
    },
    metrics: {
      rows: 3,
      columns: 3,
    },
    isChangedCellImageFirstTime: false,
    cellImages: {
      applyImageAllBefore: true,
      applyImageAllAfter: true,
      imageAllUrlBefore: '',
      imageAllUrlAfter: '',
      imageDynamic: [],
    },
    errorMessage: {
      position: 'bottom',
      message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
    },
    // ...DATA_MIGRATE[STANDARDS_BLOCKS.SHAKE_AND_WIN.name],
  },
  OTP_VERIFICATION: {
    type: 'otp-verification',
    name: 'OTP Verification',
    altText: '',
    component: 'OTPVerificationElement',
    editor: 'OTPVerificationEditor',
    displayMessage: false,
    layout: DISPLAY_LAYOUT.POPULAR,
    otpForm: {
      width: 100,
      suffix: '%',
      timerLabel: 'Expiration time',
      labelPosition: 'left',
      expiredMessage: 'OTP has expired',
      blockStyles: {
        ...BLOCK_STYLES_DEFAULT,
        color: '#000000',
        fontSize: '14px',
        fontFamily: 'Roboto',
        countDownColor: '#3060B8',
      },
      blockSettings: {
        ...BLOCK_STYLES_SETTINGS_DEFAULT,
        countDownColor: '#3060B8',
        color: '#000000',
        fontSize: '14px',
        fontFamily: 'Roboto',
      },
      backgroundImageObj: {
        name: '',
        previewUrl: '',
      },
    },
    resendButton: {
      description: "Didn't receive OTP?",
      width: 80,
      suffix: 'px',
      align: 'left',
      buttonSize: 'x-small',
      buttonStyles: {
        ...BUTTON_STYLES_DEFAULT,
        background: 'transparent',
        fontWeight: 700,
        fontSize: '14px',
        color: '#3A72B6',
        descriptionColor: '#000000',
        width: '80px',
        fontFamily: 'Roboto',
      },
      buttonSettings: {
        ...BUTTON_SETTINGS_DEFAULT,
        buttonValue: 'Resend',
        backgroundColor: 'transparent',
        fontWeight: 700,
        fontSize: '14px',
        color: '#3A72B6',
        descriptionColor: '#000000',
        width: '80px',
        fontFamily: 'Roboto',
      },
      buttonHoverStyles: BUTTON_HOVER_STYLES_DEFAULT,
      buttonHoverSettings: BUTTON_HOVER_SETTINGS_DEFAULT,
    },
    verifyButton: {
      widthSuffix: 'px',
      buttonSize: 'medium',
      textAlign: 'center',
      buttonStyles: {
        ...BUTTON_STYLES_DEFAULT,
        width: '350px',
        background: '#005fb8',
        fontFamily: 'Roboto',
        fontWeight: 500,
        color: '#fff',
        borderTopLeftRadius: '5px',
        borderTopRightRadius: '5px',
        borderBottomLeftRadius: '5px',
        borderBottomRightRadius: '5px',
      },
      buttonSettings: {
        ...BUTTON_SETTINGS_DEFAULT,
        buttonValue: 'Verify',
        width: '350px',
        backgroundColor: '#005fb8',
        fontFamily: 'Roboto',
        fontWeight: 500,
        color: '#fff',
        borderTopLeftRadius: '5px',
        borderTopRightRadius: '5px',
        borderBottomLeftRadius: '5px',
        borderBottomRightRadius: '5px',
        widthSuffix: 'px',
      },
      buttonHoverStyles: BUTTON_HOVER_STYLES_DEFAULT,
      buttonHoverSettings: BUTTON_HOVER_SETTINGS_DEFAULT,
    },
    messages: {
      failedVerification: {
        displayIn: 5,
        content: 'Invalid verificaction code',
        position: 'top',
        blockStyles: {
          ...BLOCK_STYLES_DEFAULT,
          display: 'inline-block',
          paddingTop: '10px',
          paddingRight: '20px',
          paddingBottom: '10px',
          paddingLeft: '20px',
          borderTopLeftRadius: '5px',
          borderTopRightRadius: '5px',
          borderBottomLeftRadius: '5px',
          borderBottomRightRadius: '5px',
          background: '#FCEFEE',
          color: '#B47372',
          boxShadow: '0px 2px 5px 0px #888',
          fontFamily: 'Roboto',
          textTransform: 'none',
          textDecoration: 'none',
          lineHeight: '1',
          letterSpacing: '0px',
          fontStyle: 'normal',
          fontWeight: 700,
          fontSize: '14px',
        },
        blockSettings: {
          ...BLOCK_STYLES_SETTINGS_DEFAULT,
          paddingTop: '10px',
          paddingRight: '20px',
          paddingBottom: '10px',
          paddingLeft: '20px',
          borderTopLeftRadius: '5px',
          borderTopRightRadius: '5px',
          borderBottomLeftRadius: '5px',
          borderBottomRightRadius: '5px',
          backgroundColor: '#FCEFEE',
          color: '#B47372',
          boxShadow: '0px 2px 5px 0px #888',
          fontFamily: 'Roboto',
          fontWeight: 700,
          fontSize: '14px',
        },
      },
      resentOTP: {
        displayIn: 5,
        content: 'The code has been sent again',
        position: 'bottom',
        blockStyles: {
          ...BLOCK_STYLES_DEFAULT,
          display: 'inline-block',
          paddingTop: '10px',
          paddingRight: '20px',
          paddingBottom: '10px',
          paddingLeft: '20px',
          borderTopLeftRadius: '5px',
          borderTopRightRadius: '5px',
          borderBottomLeftRadius: '5px',
          borderBottomRightRadius: '5px',
          boxShadow: '0px 2px 5px 0px #888',
          fontFamily: 'Roboto',
          textTransform: 'none',
          textDecoration: 'none',
          lineHeight: '1',
          letterSpacing: '0px',
          fontStyle: 'normal',
          fontWeight: 500,
          fontSize: '14px',
        },
        blockSettings: {
          ...BLOCK_STYLES_SETTINGS_DEFAULT,
          paddingTop: '10px',
          paddingRight: '20px',
          paddingBottom: '10px',
          paddingLeft: '20px',
          borderTopLeftRadius: '5px',
          borderTopRightRadius: '5px',
          borderBottomLeftRadius: '5px',
          borderBottomRightRadius: '5px',
          boxShadow: '0px 2px 5px 0px #888',
          fontFamily: 'Roboto',
          fontWeight: 500,
          fontSize: '14px',
        },
      },
    },
    eventTrigger: {
      isAction: false,
      event: {},
      source: {},
    },
    actions: {
      OTPElementButton: {
        event: 'otp',
        scripts: '',
        type: 'view',
        options: {
          name: '',
          track: true,
          url: '',
          pass: false,
        },
      },
    },
    inputFieldStyle: {
      styleValue: VALUE_STYLE_INPUT_FIELD.SEPARATE_BOX,
      widthSuffix: 'px',
      heightSuffix: 'px',
      inputStyles: INPUT_FIELD_STYLE_DEFAULT,
      inputSettings: { ...BLOCK_STYLES_SETTINGS_DEFAULT, backgroundColor: 'transparent' },
    },
    styles: {
      color: '#000000',
      fontFamily: 'Montserrat',
      fontWeight: 400,
      fontSize: '16px',
      textTransform: 'none',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0',
      fontStyle: 'normal',
      textAlign: 'left',
    },
    linkStyles: {
      color: '#1e5494',
      textDecoration: 'underline',
    },
    blockStyles: { ...BLOCK_STYLES_DEFAULT, display: 'flex', justifyContent: 'center' },
    blockStylesSettings: BLOCK_STYLES_SETTINGS_DEFAULT,
    blockHoverStyles: BLOCK_STYLES_DEFAULT,
    blockHoverStylesSettings: BLOCK_HOVER_STYLES_SETTINGS_DEFAULT,
    textStyling: {
      selectLineDisplay: '',
      isEllipsisText: true,
    },
    ...DATA_MIGRATE[STANDARDS_BLOCKS.OTP_VERIFICATION.name],
  },
};

export const TEMPLATE_STYLE_DEFAULT = {
  float: 'none',
  letterSpacing: 'normal',
  outline: 'none',
  textDecoration: 'none',
  textIndent: '0px',
  textShadow: 'none',
  textTransform: 'none',
  visibility: 'visible',
  lineHeight: 1,
  fontFamily: 'Roboto',
  boxShadow: 'none',
  appearance: 'none',
  zIndex: 300,
};

export const TEMPLATE_STYLES = {
  [LAYOUT_TEMPLATE.GAMIFIED.name]: {
    ...TEMPLATE_STYLE_DEFAULT,
    position: 'absolute',
    inset: '0px',
    zoom: 1,
    width: 'auto',
    height: '100%',
    margin: '0px',
    padding: '0px',
    overflow: 'auto',
  },
  [LAYOUT_TEMPLATE.FULL_SCREEN.name]: {
    ...TEMPLATE_STYLE_DEFAULT,
    position: 'absolute',
    inset: '0px',
    zoom: 1,
    width: 'auto',
    height: '100%',
    margin: '0px',
    padding: '0px',
    overflow: 'auto',
  },
  [LAYOUT_TEMPLATE.POP_UP.name]: {
    ...TEMPLATE_STYLE_DEFAULT,
    position: 'absolute',
    inset: '0px',
    zoom: 1,
    width: 'auto',
    height: '100%',
    margin: '0px',
    padding: '0px',
    overflow: 'auto',
  },
  [LAYOUT_TEMPLATE.SLIDE_IN.name]: {
    ...TEMPLATE_STYLE_DEFAULT,
    overflow: 'visible',
    maxWidth: '800px',
    width: '100%',
    background: 'transparent',
    position: 'absolute',
    right: '20px',
    // justifyContent: 'end',
    height: 'auto',
    bottom: '2px',
    // transition: 'bottom 0.3s ease 0s',
    maxHeight: '100%',
  },
  [LAYOUT_TEMPLATE.FLOATING_BAR.name]: {
    ...TEMPLATE_STYLE_DEFAULT,
    position: 'absolute',
    left: '0px',
    width: '100%',
    overflow: 'auto',
    paddingBottom: '10px',
    minHeight: '1px',
    // transition: 'bottom 0.3s ease 0s',
    top: 'auto',
    bottom: '2px',
    maxHeight: '100%',
  },
  [LAYOUT_TEMPLATE.INLINE.name]: {
    ...TEMPLATE_STYLE_DEFAULT,
    display: 'block',
    background: 'none',
    maxHeight: '100%',
    overflow: 'auto',
    paddingBottom: '2px',
  },
};

export const SLIDE_ELEMENTS_DEFAULT = [
  {
    id: random(20),
    type: 'image',
    settings: {
      name: 'Image',
      type: 'image',
      editor: 'ImageElementEditor',
      styles: {
        width: '100%',
        opacity: 1,
        boxShadow: 'none',
        borderColor: '#ffffff',
        borderStyle: 'solid',
        borderTopWidth: '0px',
        borderLeftWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderBottomRightRadius: '0px',
      },
      altText: '',
      component: 'ImageElement',
      linkedUrl: '',
      dimensions: {
        width: 0,
        height: 0,
      },
      blockStyles: {
        width: 'auto',
        zIndex: 0,
        maxWidth: '100%',
        position: 'relative',
        boxShadow: 'none',
        marginTop: '0px',
        background: 'transparent',
        marginLeft: '0px',
        paddingTop: '0px',
        borderColor: '#000000',
        borderStyle: 'none',
        marginRight: '0px',
        paddingLeft: '0px',
        marginBottom: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        borderTopWidth: '0px',
        borderLeftWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderBottomRightRadius: '0px',
      },
      linkedImage: false,
      linkedTarget: '_blank',
      uploadedImage: {
        previewUrl: '',
      },
      linkedNoFollow: false,
      linkedTracking: false,
      stylesSettings: {
        boxShadowBlur: '0px',
        boxShadowColor: 'rgba(0, 0, 0, 0.5)',
        boxShadowInset: false,
        boxShadowStyle: 'none',
        boxShadowSpread: '0px',
        borderRadiusStyle: 'none',
        boxShadowVertical: '0px',
        borderRadiusSuffix: 'px',
        boxShadowHorizontal: '0px',
        linkedBorderWidthInput: true,
        linkedBorderRadiusInput: true,
      },
      blockStylesSettings: {
        hidden: false,
        customId: '',
        customClass: '',
        linearAngle: 45,
        gradientType: 'linear-gradient',
        marginSuffix: 'px',
        boxShadowBlur: '0px',
        paddingSuffix: 'px',
        backgroundSize: 'cover',
        boxShadowColor: '#ffffff',
        boxShadowInset: false,
        boxShadowStyle: 'none',
        radialPosition: 'center center',
        backgroundColor: 'transparent',
        boxShadowSpread: '0px',
        backgroundRepeat: 'no-repeat',
        borderRadiusStyle: 'none',
        boxShadowVertical: '0px',
        linkedMarginInput: true,
        backgroundPosition: 'left top',
        borderRadiusSuffix: 'px',
        linkedPaddingInput: false,
        boxShadowHorizontal: '0px',
        backgroundColorStyle: 'color',
        linkedBorderWidthInput: true,
        linkedBorderRadiusInput: true,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      outerContainerStyles: {
        textAlign: 'left',
      },
      ...DATA_MIGRATE[STANDARDS_BLOCKS.IMAGE.name],
    },
    createdAt: '09/05/2022 - 10:05:37',
  },
  {
    id: random(20),
    type: 'text',
    settings: {
      name: 'Text',
      type: 'text',
      editor: 'TextElementEditor',
      styles: {
        color: '#000000',
        fontSize: '16px',
        fontStyle: 'normal',
        textAlign: 'left',
        fontFamily: 'Montserrat',
        fontWeight: 400,
        lineHeight: '1',
        letterSpacing: '0',
        textTransform: 'none',
        textDecoration: 'none',
      },
      altText: '',
      rawHTML:
        '<p style="text-align: center;"><span style="font-family: Poppins; font-size: 16px; letter-spacing: 0px; color: rgb(0, 0, 0);">Product Name Here</span></p>',
      component: 'TextElement',
      linkStyles: {
        color: '#1e5494',
        textDecoration: 'underline',
      },
      blockStyles: {
        width: 'auto',
        zIndex: 0,
        maxWidth: '100%',
        position: 'relative',
        boxShadow: 'none',
        marginTop: '0px',
        background: 'transparent',
        marginLeft: '0px',
        paddingTop: '5px',
        borderColor: '#000000',
        borderStyle: 'none',
        marginRight: '0px',
        paddingLeft: '0px',
        marginBottom: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        borderTopWidth: '0px',
        borderLeftWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderBottomRightRadius: '0px',
      },
      blockStylesSettings: {
        hidden: false,
        customId: '',
        customClass: '',
        linearAngle: 45,
        gradientType: 'linear-gradient',
        marginSuffix: 'px',
        boxShadowBlur: '0px',
        paddingSuffix: 'px',
        backgroundSize: 'cover',
        boxShadowColor: '#ffffff',
        boxShadowInset: false,
        boxShadowStyle: 'none',
        radialPosition: 'center center',
        backgroundColor: 'transparent',
        boxShadowSpread: '0px',
        backgroundRepeat: 'no-repeat',
        borderRadiusStyle: 'none',
        boxShadowVertical: '0px',
        linkedMarginInput: true,
        backgroundPosition: 'left top',
        borderRadiusSuffix: 'px',
        linkedPaddingInput: false,
        boxShadowHorizontal: '0px',
        backgroundColorStyle: 'color',
        linkedBorderWidthInput: true,
        linkedBorderRadiusInput: true,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      ...DATA_MIGRATE[STANDARDS_BLOCKS.TEXT.name],
    },
    createdAt: '09/05/2022 - 10:08:48',
  },
  {
    id: random(20),
    type: 'text',
    settings: {
      name: 'Text',
      type: 'text',
      editor: 'TextElementEditor',
      styles: {
        color: '#000000',
        fontSize: '16px',
        fontStyle: 'normal',
        textAlign: 'left',
        fontFamily: 'Montserrat',
        fontWeight: 400,
        lineHeight: '1',
        letterSpacing: '0',
        textTransform: 'none',
        textDecoration: 'none',
      },
      altText: '',
      rawHTML:
        '<p style="text-align: center;"><span style="font-family: Poppins; font-size: 16px; letter-spacing: 0px; color: rgb(0, 0, 0);">$<s>29</s> </span><span style="font-family: Poppins; font-size: 16px; letter-spacing: 0px; color: rgb(243, 121, 52);">$19</span></p>',
      component: 'TextElement',
      linkStyles: {
        color: '#1e5494',
        textDecoration: 'underline',
      },
      blockStyles: {
        width: 'auto',
        zIndex: 0,
        maxWidth: '100%',
        position: 'relative',
        boxShadow: 'none',
        marginTop: '5px',
        background: 'transparent',
        marginLeft: '0px',
        paddingTop: '5px',
        borderColor: '#000000',
        borderStyle: 'none',
        marginRight: '0px',
        paddingLeft: '0px',
        marginBottom: '5px',
        paddingRight: '0px',
        paddingBottom: '0px',
        borderTopWidth: '0px',
        borderLeftWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderBottomRightRadius: '0px',
      },
      blockStylesSettings: {
        hidden: false,
        customId: '',
        customClass: '',
        linearAngle: 45,
        gradientType: 'linear-gradient',
        marginSuffix: 'px',
        boxShadowBlur: '0px',
        paddingSuffix: 'px',
        backgroundSize: 'cover',
        boxShadowColor: '#ffffff',
        boxShadowInset: false,
        boxShadowStyle: 'none',
        radialPosition: 'center center',
        backgroundColor: 'transparent',
        boxShadowSpread: '0px',
        backgroundRepeat: 'no-repeat',
        borderRadiusStyle: 'none',
        boxShadowVertical: '0px',
        linkedMarginInput: false,
        backgroundPosition: 'left top',
        borderRadiusSuffix: 'px',
        linkedPaddingInput: false,
        boxShadowHorizontal: '0px',
        backgroundColorStyle: 'color',
        linkedBorderWidthInput: true,
        linkedBorderRadiusInput: true,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      ...DATA_MIGRATE[STANDARDS_BLOCKS.TEXT.name],
    },
    createdAt: '09/05/2022 - 10:08:48',
  },
  {
    id: random(20),
    type: 'button',
    settings: {
      type: 'button',
      name: 'Button',
      component: 'ButtonElement',
      editor: 'ButtonElementEditor',
      actions: {
        ButtonElement: {
          event: 'click',
          scripts: '',
          type: 'redirect',
          options: {
            name: 'success',
            track: true,
            url: 'https://antsomi.com/',
            pass: false,
            close: false,
            phone: '',
            copy: '',
          },
        },
      },
      buttonStyles: {
        borderTopLeftRadius: '3px',
        borderTopRightRadius: '3px',
        borderBottomRightRadius: '3px',
        borderBottomLeftRadius: '3px',
        borderTopWidth: '1px',
        borderRightWidth: '1px',
        borderBottomWidth: '1px',
        borderLeftWidth: '1px',
        paddingTop: '10px',
        paddingRight: '10px',
        paddingBottom: '10px',
        paddingLeft: '10px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        color: '#F37934',
        fontFamily: 'Poppins',
        fontWeight: 500,
        fontSize: '12px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0px',
        fontStyle: 'normal',
        background: '#ffffff',
        borderStyle: 'solid',
        borderColor: '#F3793480',
        boxShadow: 'none',
        maxWidth: '100%',
        height: '100%',
        width: '80%',
        textAlign: 'center',
      },
      buttonSettings: {
        borderRadiusStyle: 'custom',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: true,
        paddingSuffix: 'px',
        linkedMarginInput: false,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: 'rgba(0, 0, 0, 0.5)',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: '#ffffff',
        backgroundColorStyle: 'color',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        buttonValue: 'Products Details',
        buttonSize: 'medium',
        editIcon: false,
        icon: 'fas arrow-right',
        iconColor: '#F37934',
        iconSize: '12px',
        iconAfter: true,
        iconSpacing: '2px',
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      buttonHoverStyles: {
        borderTopLeftRadius: '100px',
        borderTopRightRadius: '100px',
        borderBottomRightRadius: '100px',
        borderBottomLeftRadius: '100px',
        borderTopWidth: '1px',
        borderRightWidth: '1px',
        borderBottomWidth: '1px',
        borderLeftWidth: '1px',
        color: '#ffffff',
        fontFamily: 'Quicksand',
        fontWeight: 900,
        fontSize: '24px',
        textTransform: 'none',
        textDecoration: 'none',
        lineHeight: '1',
        letterSpacing: '0px',
        fontStyle: 'normal',
        background: '#F37934',
        borderStyle: 'solid',
        borderColor: '#F37934',
        boxShadow: '0px 0px 0px 0px rgba(0, 0, 0, 0.3)',
        maxWidth: '100%',
        paddingTop: '16px',
        paddingRight: '16px',
        paddingBottom: '16px',
        paddingLeft: '16px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        width: '88%',
        textAlign: 'center',
      },
      buttonHoverSettings: {
        boxShadowStyle: 'custom',
        boxShadowColor: 'rgba(0, 0, 0, 0.3)',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: '#F37934',
        backgroundColorStyle: 'color',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        editHover: true,
        editIcon: false,
        icon: '',
        iconColor: '#ffffff',
        iconSize: 28,
        iconAfter: false,
        iconSpacing: '3',
        borderRadiusStyle: 'capsule_round',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: true,
        paddingSuffix: 'px',
        linkedMarginInput: false,
        marginSuffix: 'px',
        customId: '',
        customClass: '',
        buttonSize: 'small',
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      blockStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        width: 'auto',
        maxWidth: '100%',
        position: 'relative',
        zIndex: 0,
      },
      blockStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        customId: '',
        customClass: '',
        hidden: false,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      ...DATA_MIGRATE[STANDARDS_BLOCKS.BUTTON.name],
    },
    createdAt: '09/05/2022 - 14:15:27',
  },
];

export const FILTERS_DEFAULT: TFilters = {
  // OR: [
  //   {
  //     AND: [
  //       {
  //         operator: null,
  //         data_type: null,
  //         column: null,
  //         condition_type: 'comp_attr',
  //         value_type: 'normal',
  //         value: null,
  //       },
  //     ],
  //   },
  // ],
  OR: [],
};

export const PRODUCT_RANKING_DEFAULT: TRanking = {
  type: 'algorithms',
  algorithms: {
    sort: 'mix',
    value: [
      {
        value: 'seen_products',
        quantity: 5,
      },
    ],
    filters: [],
  },
  custom: '',
};

export const GET_TOP_RANKING_DEFAULT: TRanking = {
  type: 'algorithms',
  algorithms: {
    sort: 'mix',
    value: [
      {
        value: 'get_top',
        quantity: 5,
        sort_type: 'desc',
        sort_by: 'last_updated',
      },
    ],
    filters: [],
  },
  custom: '',
};

export const ARTICLE_RANKING_DEFAULT: TRanking = {
  type: 'algorithms',
  algorithms: {
    sort: 'mix',
    value: [
      {
        value: 'seen_products',
        quantity: 5,
      },
    ],
    filters: [],
  },
  custom: '',
};

export const PRODUCT_ITEM_TYPE_ID = 1;

export const BUSINESS_OBJECT_SETTINGS_DEFAULT: TBusinessObjectSettings = {
  itemTypeId: null,
  itemTypeName: null,
  itemTypeDisplay: null,
  filters: FILTERS_DEFAULT,
  ranking: PRODUCT_RANKING_DEFAULT,
  fallback: FALLBACK_SELECTION.HIDDEN.value,
};

export const CONTENT_SOURCES_SETTINGS_DEFAULT: TContentSourceSettings = {
  groups: [],
  expanded: [],
  isLoadingDataBO: false,
  isExcludeDuplicate: true,
};

export const UTM_TRACKING_SETTING_DEFAULT: TUtmTrackingSettings = {
  source: [],
  medium: [],
  campaign: [],
  term: [],
  content: [],
};

export const TRACKING_MODULE_DATA_DEFAULT: TTrackingModuleData = {
  source: [],
  medium: [],
  campaign: [],
  term: [],
  content: [],
};

export const TRACKING_MODULE_DEFAULT: TTrackingModule = {
  isTrackingItem: false,
  mode: 'utm',
  data: TRACKING_MODULE_DATA_DEFAULT,
};

export const TABLE_COLUMN_SETTINGS_DEFAULT = {
  id: '',
  dataType: '',
  placement: 'left',
  value: '',
  label: '',
  width: '0',
} as TColumnTableBlock;

export const JOURNEY_SETTINGS_DEFAULT = {
  unsubscribeSegmentType: 'customer',
  triggerType: 'event_based',
  triggerEvent: {
    eventActionId: '',
    eventCategoryId: '',
    insightPropertyIds: [],
  },
};

export const OBJECTIVE_TYPES_DEFAULT = [OBJECTIVE_VALUES.PERSONALIZATION_CONTENT];

export const ITEM_TYPE_NAME = {
  ARTICLE: 'article',
};

export const ITEM_TYPE_NAME_DISPLAY_LEVEL = [ITEM_TYPE_NAME.ARTICLE];

export const ITEM_TYPE_NAME_DISPLAY_SORT = [ITEM_TYPE_NAME.ARTICLE];

/* NOTE: This constants is used to migrate data for per block settings */
export const MIGRATE_BLOCK_SETTINGS = {
  breakpoints: BREAK_POINTS_DEFAULT,
};
