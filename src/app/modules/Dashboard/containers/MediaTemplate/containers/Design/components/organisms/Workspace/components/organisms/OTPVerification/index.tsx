import React, { <PERSON>actNode, useMemo } from 'react';

import { DEFAULT_PROPERTIES_OTP, DEFAULT_STYLE_RESEND, DEFAULT_STYLE_TIME_EXPIRE, DISPLAY_LAYOUT } from './constants';
import { ButtonBlock } from '../YesNoBlock';
import { useDispatch, useSelector } from 'react-redux';
import { mediaTemplateDesignActions } from '../../../../../../slice';
import { SIDE_PANEL_TABS } from '../../../../SidePanel/constants';
import { selectSidePanel, selectViewPages } from '../../../../../../slice/selectors';
import { handleError } from 'app/utils/handleError';
import { SIDE_PANEL_COLLAPSE, TYPE_SETTING } from '../../../../../../constants';
import {
  ContentColumn,
  ContentMessage,
  ExpireTimeElement,
  InputItem,
  WrapperInputField,
  WrapperItem,
  WrapperNotiMessage,
  WrapperOTPVerificaction,
} from './styled';
import { VALUE_STYLE_INPUT_FIELD } from '../../../../SidePanel/components/organisms/BlockEditing/OTPVerification/constants';
import { get } from 'lodash';
import { getStyleHyphens } from './utils';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/organisms/OTPVerification/index.tsx';

export const OTPVerificationBlock: React.FC<any> = props => {
  const { settings, namespace, id, type, isPreviewMode } = props;

  const dispatch = useDispatch();
  const sidePanel = useSelector(selectSidePanel);
  const viewPages = useSelector(selectViewPages);

  // find otp length
  const blockProperties = useMemo(() => {
    let data = { ...DEFAULT_PROPERTIES_OTP };
    let listOptinField: any = [];
    viewPages.forEach(page => {
      Object.values(page?.blocks || {}).forEach(block => {
        if (block?.type === 'optin_fields') {
          listOptinField.push(block);
        }
      });
    });
    if (listOptinField && listOptinField.length) {
      const optinFieldWithOTP = listOptinField.find(optin => {
        const actionData = optin.settings?.actions;
        if (actionData && actionData.FieldsElementButton) {
          if (actionData.FieldsElementButton.providerInfo) {
            return true;
          }
          return false;
        }
      });

      if (optinFieldWithOTP) {
        const providerSettings = get(
          optinFieldWithOTP,
          'settings.actions.FieldsElementButton.providerInfo.destinationSetting',
          {},
        );
        if (providerSettings) {
          data.numberInput = providerSettings.otpLength;
          data.timeExpiration = providerSettings.expirationTime;
        }
      }
    }
    return data;
  }, [viewPages]);

  const { otpForm, resendButton, verifyButton, displayMessage = false, messages, inputFieldStyle } = settings;

  const {
    numberInput,
    timeExpiration: { sec, min },
  } = blockProperties;

  const { setSidePanel } = mediaTemplateDesignActions;

  const renderBlockInput = () => {
    const { styleValue, inputStyles } = inputFieldStyle;
    let items: ReactNode[] = [];
    let index = 0;
    const isWholeBox = styleValue === VALUE_STYLE_INPUT_FIELD.WHOLE_BOX;
    const isHyphensBox = styleValue === VALUE_STYLE_INPUT_FIELD.HYPHENS;

    const { styleHyphens, restInputStyle } = getStyleHyphens(inputStyles);

    if (isWholeBox) {
      const arrInput = Array.from({ length: 6 }, (_, i) => i);
      const itemEl = (
        <InputItem fullWidth key={index} style={{ ...inputStyles }}>
          {arrInput.map(number => {
            return <span style={{ zIndex: 2 }}>{number + 1}</span>;
          })}
        </InputItem>
      );
      items.push(itemEl);
    } else {
      while (index < numberInput) {
        const itemEl = (
          <InputItem
            key={index}
            style={isHyphensBox ? { ...restInputStyle, background: 'transparent' } : { ...inputStyles }}
          >
            <span style={{ zIndex: 2 }}>{index + 1}</span>
            {isHyphensBox && (
              <div
                className="block-hyphens"
                style={{
                  ...styleHyphens,
                  background: inputStyles.background,
                  color: 'unset',
                }}
              />
            )}
          </InputItem>
        );
        items.push(itemEl);
        index++;
      }
    }

    return (
      <WrapperItem className="ants-justify-between" data-otp-input-type={styleValue}>
        {items}
      </WrapperItem>
    );
  };

  const styledBlockOTP = useMemo(() => {
    const { blockStyles = {}, backgroundImageObj = {} } = otpForm;
    const {
      lineHeight,
      fontSize,
      color,
      letterSpacing,
      textTransform,
      fontFamily,
      textDecoration,
      fontStyle,
      fontWeight,
      ...restStyles
    } = blockStyles;
    const style = {
      ...restStyles,
      width: `${otpForm?.width}${otpForm?.suffix}`,
    };
    if (backgroundImageObj.previewUrl) {
      style.background = `url(${backgroundImageObj.previewUrl}) no-repeat center center`;
    }
    return style;
  }, [otpForm]);

  const styledTimeExpire = DEFAULT_STYLE_TIME_EXPIRE;
  const styledResend = DEFAULT_STYLE_RESEND;

  const onClickElement = (e, buttonType: string) => {
    e.stopPropagation();
    try {
      if (!sidePanel.isCollapsed) {
        setTimeout(() => {
          dispatch(
            setSidePanel({
              type: type,
              blockSelectedId: id,
              activeTab: SIDE_PANEL_TABS.CONTENT.name,
              slideShowId: '',
              slideId: '',
              activePanel: buttonType,
            }),
          );
        }, 100);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickElement',
        args: {},
      });
    }
  };

  const renderButtonBlock = (configBlock, isVerifyBtn = true) => {
    const buttonSettings = configBlock?.buttonSettings;
    const buttonStyles = configBlock?.buttonStyles;
    const buttonHoverSettings = configBlock?.buttonHoverSettings;
    const buttonHoverStyles = configBlock?.buttonHoverStyles;
    return (
      <ButtonBlock
        id={`${namespace}-OTPFormElement--${id}`}
        settings={settings}
        buttonSettings={buttonSettings}
        buttonStyles={buttonStyles}
        buttonHoverSettings={buttonHoverSettings}
        buttonHoverStyles={buttonHoverStyles}
        onClick={e => onClickElement(e, SIDE_PANEL_COLLAPSE.RESEND_BUTTON)}
        actions={settings.actions.YesButtonElement}
        isPreviewMode={isPreviewMode}
        {...(isVerifyBtn
          ? {
              'data-action-button-verify': 1,
            }
          : { 'data-action-button-resend': 1 })}
      />
    );
  };

  const renderTimeExpire = () => {
    const { countDownColor, color, ...restStyles } = otpForm?.blockStyles || {};
    return (
      <ExpireTimeElement style={restStyles} labelPosition={otpForm.labelPosition}>
        <span style={{ color }} className="expire-time-title">
          {otpForm.timerLabel}
        </span>
        <span
          style={{ color: countDownColor }}
          className="time-remain"
          data-countdown-min={min}
          data-countdown-sec={sec}
        >
          {min < 10 ? `0${min}` : min}:{sec < 10 ? `0${sec}` : sec}
        </span>
      </ExpireTimeElement>
    );
  };

  const renderOTPContentWithLayout = () => {
    let element: ReactNode = null;
    const { descriptionColor = '' } = resendButton?.buttonStyles || {};
    switch (settings?.layout) {
      case DISPLAY_LAYOUT.POPULAR:
        element = (
          <>
            <WrapperInputField>{renderBlockInput()}</WrapperInputField>
            <div className="ants-my-4">
              <WrapperItem style={styledTimeExpire}>{renderTimeExpire()}</WrapperItem>
            </div>
            <div className="ants-mt-36">
              <ContentColumn alignContent={verifyButton?.buttonStyles.textAlign}>
                {renderButtonBlock(verifyButton)}
              </ContentColumn>
            </div>
            <div className="ants-mt-5">
              <WrapperItem style={styledResend}>
                {resendButton?.description && (
                  <ContentColumn style={{ justifyContent: 'flex-end', color: descriptionColor }}>
                    <span className="ants-mr-2">{resendButton?.description}</span>
                  </ContentColumn>
                )}
                <ContentColumn alignContent={resendButton.align}>
                  {renderButtonBlock(resendButton, false)}
                </ContentColumn>
              </WrapperItem>
            </div>
          </>
        );
        break;

      case DISPLAY_LAYOUT.NEAT:
        element = (
          <>
            <WrapperInputField>{renderBlockInput()}</WrapperInputField>
            <div className="ants-mt-4">
              <WrapperItem style={styledTimeExpire}>{renderTimeExpire()}</WrapperItem>
            </div>
            <div className="ants-mt-5">
              <WrapperItem style={styledResend}>
                {resendButton?.description && (
                  <ContentColumn style={{ justifyContent: 'flex-end', color: descriptionColor }}>
                    <span className="ants-mr-2">{resendButton?.description}</span>
                  </ContentColumn>
                )}
                <ContentColumn alignContent={resendButton.align}>
                  {renderButtonBlock(resendButton, false)}
                </ContentColumn>
              </WrapperItem>
            </div>
            <div className="ants-mt-[108px] ants-mb-[49px]">
              <ContentColumn alignContent={verifyButton?.buttonStyles.textAlign}>
                {renderButtonBlock(verifyButton)}
              </ContentColumn>
            </div>
          </>
        );
        break;
      case DISPLAY_LAYOUT.SPREAD:
        element = (
          <>
            <WrapperInputField>{renderBlockInput()}</WrapperInputField>
            <div className="ants-mt-5">
              <WrapperItem style={styledResend}>
                <ContentColumn alignContent={verifyButton?.buttonStyles.textAlign}>
                  {renderButtonBlock(verifyButton)}
                </ContentColumn>
                <ContentColumn alignContent={resendButton.align}>
                  {renderButtonBlock(resendButton, false)}
                </ContentColumn>
              </WrapperItem>
            </div>
            <div className="ants-mt-4">
              <WrapperItem style={styledTimeExpire}>{renderTimeExpire()}</WrapperItem>
            </div>
          </>
        );
        break;

      default:
        break;
    }
    return element;
  };

  const renderFailedMessage = () => {
    const { failedVerification } = messages;
    return (
      <WrapperNotiMessage
        style={{ display: displayMessage ? 'block' : 'none' }}
        type={failedVerification?.position}
        data-failed-verification-el
      >
        <ContentMessage style={{ ...failedVerification?.blockStyles }}>
          <span>{failedVerification?.content}</span>
        </ContentMessage>
      </WrapperNotiMessage>
    );
  };
  const renderResentMessage = () => {
    const { resentOTP } = messages;
    return (
      <WrapperNotiMessage
        style={{ display: displayMessage ? 'block' : 'none' }}
        type={resentOTP?.position}
        data-resent-otp-el
      >
        <ContentMessage style={{ ...resentOTP?.blockStyles }}>
          <span>{resentOTP?.content}</span>
        </ContentMessage>
      </WrapperNotiMessage>
    );
  };

  return (
    <WrapperOTPVerificaction style={styledBlockOTP} id={`${namespace}-OTPVerificationElement--${id}`}>
      {renderOTPContentWithLayout()}
      {renderFailedMessage()}
      {renderResentMessage()}
    </WrapperOTPVerificaction>
  );
};
