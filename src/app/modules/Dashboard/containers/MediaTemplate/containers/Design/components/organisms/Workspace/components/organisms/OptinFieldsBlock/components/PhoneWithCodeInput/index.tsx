import { FC, useState, useEffect, useRef } from 'react';

// Molecules
import { SelectCustom } from 'app/components/molecules';

// Constants
import { COUNTRY_OPTIONS, ARRAY_COUNTRIES } from 'app/components/molecules/DragBlock/constant';

// Utils
import { buildCusFieldDataset, ignoreStyles } from '../../utils';
import { getFontStyles, getBorderStyles } from '../../../../../../SidePanel/utils';

// Types
import { TField } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FormFieldsSetting/types';

// export interface IField {
//   inputName: string;
//   id: string;
//   fieldId: string;
//   type: string;
//   placeholder: string;
//   errorText: string;
//   name: string;
//   required: boolean;
//   phoneValidationEnabled: boolean;
// }

interface PhoneWithCodeInputProps {
  field: TField;
  styles: React.CSSProperties;
  namespace: string | undefined;
  isPreviewMode: boolean | undefined;
  tabIndex: number;
  onClickElement: Function;
  placeholderColor: string;
}

const CODE_WIDTH = 47;

const PhoneWithCodeInput: FC<PhoneWithCodeInputProps> = ({
  field,
  styles,
  namespace,
  isPreviewMode,
  tabIndex,
  onClickElement,
  placeholderColor = '',
}) => {
  const [maxHeight, setMaxHeight] = useState<number | 'unset'>('unset');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setMaxHeight(inputRef.current ? inputRef.current.getBoundingClientRect().height : 'unset');
  }, [styles]);

  const renderInput = () => (
    <input
      ref={inputRef}
      id={field.fieldId || `${namespace}-${field.inputName}`}
      key={field.id}
      className="!ants-h-[fit-content]"
      name={`${namespace}-${field.inputName}`}
      type={field.type}
      placeholder={field.placeholder}
      tabIndex={tabIndex}
      required={field.required}
      autoComplete="off"
      {...(field.useCountryCode
        ? {
            'data-has-parent': true,
          }
        : {
            'data-error': field.errorText,
            'data-invalid': field.invalidText,
            'data-fieldid': field.id,
            'data-name': field.name,
            'data-phone-validation-enabled': field.phoneValidationEnabled,
          })}
      style={{
        ...styles,
        outline: 'none',
        width: '100%',
        ...(field.useCountryCode && {
          borderLeft: 'none',
          borderTopLeftRadius: 0,
          borderBottomLeftRadius: 0,
          flex: 1,
        }),
      }}
      readOnly={!isPreviewMode}
      onClick={() => onClickElement(field.id)}
    />
  );

  return field.useCountryCode ? (
    <div
      className="ants-flex ants-align-center"
      data-phone-validation-enabled={field.phoneValidationEnabled}
      {...buildCusFieldDataset(field, namespace || '', placeholderColor)}
    >
      <>
        <SelectCustom
          icon="1"
          isPreviewMode={isPreviewMode}
          key={`${field.id}-select`}
          value={field?.countryCodeDefault.value || ''}
          options={ARRAY_COUNTRIES.map(item => ({
            label: `${item.country} +${item.code}`,
            value: item.countryCode,
            code: item.code,
          }))}
          action="phoneCode"
          style={{
            ...ignoreStyles(styles, ['padding']),
            height: '100%',
            // width: CODE_WIDTH,
            outline: 'none',
            borderTopRightRadius: 0,
            borderBottomRightRadius: 0,
            padding: '5px',
            maxHeight: `${maxHeight}px`,
          }}
          selectStyle={{
            height: '100%',
          }}
          display={'icon'}
        />
        <div
          className="cus-phone-code-placeholder"
          style={{
            ...getFontStyles(styles),
            ...getBorderStyles(styles),
            borderLeft: 'none',
            borderRight: 'none',
            width: 'fit-content',
            display: 'flex',
            alignItems: 'center',
            paddingLeft: 5,
            background: styles.background,
            color: styles.color, //'#999999',
            height: `${maxHeight}px`,
          }}
        >
          +{field?.countryCodeDefault?.code || '84'}
        </div>
      </>

      {renderInput()}
    </div>
  ) : (
    renderInput()
  );
};

export default PhoneWithCodeInput;
