/* eslint-disable @typescript-eslint/no-use-before-define */
// Libraries
import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RadioChangeEvent } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { DragDropContext, Draggable, Droppable, DropResult } from 'react-beautiful-dnd';
import produce from 'immer';
import cls from 'classnames';
import sortBy from 'lodash/sortBy';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Button, Checkbox, Divider, Icon, Radio, RequiredLabel, Space, Text } from 'app/components/atoms';

// Molecules
import {
  InputNumber,
  RadioGroup,
  TabPane,
  SelectMulti,
  SelectMultiOption,
  SelectOptionLabel,
  Select,
} from 'app/components/molecules';
import { SettingWrapper } from '../../molecules';
import { EditorScript } from 'app/components/molecules/EditorScript';
import { SelectEventAttribute } from '../../molecules/SelectEventAttribute';

// Styled
import { AlgorithmsSettingWrapper, AlgorithmWrapper, StyledSelect, StyledTabs } from './styled';

// Constants
import {
  ALGORITHM_TABS,
  COMPARE_TYPE,
  DEFAULT_DISTANCE_ALGORITHM,
  isCheckStatusCollection,
  MAX_RETARGET_SEARCH_BY,
  OPERATOR_OPTIONS,
  RANKING_TYPE,
  SORT_OPTIONS,
  INTEREST_OPTIONS,
  CATEGORY_OPTIONS,
  SORT_TYPE_OPTIONS,
  SIMILAR_CONTENT_OPTIONS,
  ARTICLE_TREND_BY_OPTIONS,
  ARTICLE_TREND_IN_OPTIONS,
  ARTICLE_TREND_SORT_BY_OPTIONS,
  EXCLUDE_ARTICLE_FILTER,
  DEFAULT_TRENDING_ALGORITHM,
} from './constants';

// Utils
import { handleError } from 'app/utils/handleError';
import { reorder } from 'app/utils/common';
import { isExistKey, isOperatorInRange } from './utils';
import {
  buildOptionAttrArchive,
  buildOptionArchive,
  checkStatusAttr,
  checkStatusCollection,
  optionNotExitsValue,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Slices
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Services
import {
  getListAlgorithmsBO,
  getListCollectionsBO,
  getListFiltersBO,
} from 'app/services/MediaTemplateDesign/BusinessObject';

// Queries
import { isCheckStatusAttr } from '../../molecules/DynamicSetting/constants';
import { useGetListBO } from 'app/queries/BusinessObject';
import { SelectMultOptGroup } from 'app/components/molecules/SelectMulti';
import { useGetListAttributeBO } from 'app/queries/BusinessObject/useGetListAttributeBO';

import { TRanking } from '../../../../../../types';
import {
  ITEM_TYPE_NAME,
  ITEM_TYPE_NAME_DISPLAY_LEVEL,
  ITEM_TYPE_NAME_DISPLAY_SORT,
  PRODUCT_ITEM_TYPE_ID,
} from '../../../../../../config';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/AlgorithmsSetting/index.tsx';

interface AlgorithmsSettingProps {
  isShowErrorAlert: boolean;
  journeySettings: Record<string, any>;
  contentSourceGroupId: string;
  itemTypeId: number;
  itemTypeName?: string | null;
  data: TRanking;
  // Max quantity of algorithms value
  algorithmQuantityMax?: number;
  onChange: (value: TRanking) => void;
}

interface TAlgoOption {
  value: string;
  label: string;
  type: string;
}

export const AlgorithmsSetting: React.FC<AlgorithmsSettingProps> = (props: AlgorithmsSettingProps) => {
  const {
    data,
    itemTypeId,
    itemTypeName,
    isShowErrorAlert,
    journeySettings,
    contentSourceGroupId,
    onChange,
    algorithmQuantityMax = 30,
  } = props;

  // Dispatch
  const dispatch = useDispatch();
  // Translations
  const { t } = useTranslation();

  // Selectors
  const ranking = data ?? {};
  const {
    algorithms = {
      sort: 'mix',
      value: [],
      filters: [],
    },
  } = ranking;

  // Queries
  const { data: listBO } = useGetListBO();

  // States
  const [listFilter, setListFilter] = useState<any[]>([]);
  const [listCollection, setListCollection] = useState<any[]>([]);
  const [selectedAlgos, setSelectedAlgos] = useState<string[]>([]);
  const [isShowSelect, setIsShowSelect] = useState(false);
  const [isShowDropdownSelect, setIsShowDropdownSelect] = useState<boolean>(false);

  // const [activeType, setActiveType] = useState(RANKING_TYPE.ALGORITHMS.value);
  const [activeTab, setActiveTab] = useState(ALGORITHM_TABS.ALGORITHMS.value);
  const [listAlgo, setListAlgo] = useState<TAlgoOption[]>([]);
  const [expandedAlgoTypes, setExpandAlgoTypes] = useState<string[]>([]);

  useEffect(() => {
    getListAlgorithms(itemTypeName);
    getListFilters();
  }, [itemTypeName]);

  useEffect(() => {
    if (itemTypeId === 1 || itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
      getListCollections();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemTypeId]);

  // map algo redux
  const mapAlgoSelected = useMemo(
    () =>
      algorithms.value.reduce((acc, cur) => {
        acc[cur.value] = cur;
        return acc;
      }, {}),
    [algorithms],
  );

  // map list algo api
  const mapListAlgo = useMemo(
    () =>
      listAlgo.reduce((acc, cur) => {
        acc[cur.value] = cur;
        return acc;
      }, {}),
    [listAlgo],
  );

  const listAlgoByTypes = useMemo(() => {
    const initalValue = new Map<string, TAlgoOption[]>();

    return sortBy(listAlgo, ({ type }) => type).reduce((acc, algo) => {
      const algosByType = acc.get(algo.type) ?? [];
      algosByType.push(algo);

      return acc.set(algo.type, algosByType);
    }, initalValue);
  }, [listAlgo]);

  // Services
  const getListAlgorithms = async itemTypeName => {
    const { rows } = await getListAlgorithmsBO({ itemTypeName });
    const initExpanedAlgoTypes: string[] = [];

    const listAlgorithms = rows
      .map(item => {
        if (item.triggerTypes.includes(journeySettings.triggerType)) {
          initExpanedAlgoTypes.push(item.type);

          return {
            type: item.type,
            value: item.value,
            label: item.label,
          };
        }
      })
      .filter(Boolean);

    setListAlgo(listAlgorithms);
    setExpandAlgoTypes(initExpanedAlgoTypes.filter((value, index, array) => array.indexOf(value) === index));
  };

  // Queries
  const {
    data: [listAttribute, listAttributeRetarget],
    isFetching: isLoadingAttribute,
  } = useGetListAttributeBO<any>({
    itemTypeIds: [itemTypeId],
    options: {
      select(data) {
        const { rows = [] } = data || {};

        const listAttr =
          rows[0]?.properties.map(item => ({
            ...item,
            value: item.itemPropertyName,
            label: item.translateLabel,
            disabled: parseInt(item.status) === 4,
          })) || [];

        return [listAttr, listAttr.filter(each => each.dataType === 'string' || each.dataType === 'text')];
      },
    },
  });

  const getListFilters = async () => {
    const { rows } = await getListFiltersBO();
    const filters = rows
      .map(item => ({
        value: item.value,
        label: item.label,
      }))
      .filter(filter => {
        if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
          return EXCLUDE_ARTICLE_FILTER.includes(filter.value);
        }
        return true;
      })
      .map(each => {
        if (itemTypeName === ITEM_TYPE_NAME.ARTICLE && each.value === 'seen_products') {
          return {
            ...each,
            label: 'Seen Articles',
          };
        }
        return each;
      });

    setListFilter(filters);
  };

  const getListCollections = async () => {
    const { rows } = await getListCollectionsBO({ itemTypeId });
    const filters = rows.map(item => ({
      value: item.value,
      id: item.value,
      label: item.label,
      disabled: parseInt(item.model.status) === 4,
      status: parseInt(item.model.status),
    }));
    setListCollection(filters);
  };

  // Handlers Algorithms
  const onChangeType = (event: RadioChangeEvent) => {
    try {
      onChange({
        ...data,
        type: event.target.value,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onChangeType.name,
        args: {},
      });
    }
  };

  // const onChangeSwitchCustomRanking = (checked: boolean) => {
  //   try {
  //     dispatch(
  //       setContentSourceGroup({
  //         groupId: contentSourceGroupId,
  //         values: {
  //           ranking: checked
  //             ? {
  //                 type: 'custom',
  //                 algorithms: { sort: 'mix', value: [], filters: [] },
  //                 custom: '',
  //               }
  //             : undefined,
  //         },
  //       }),
  //     );
  //   } catch (error) {
  //     handleError(error, {
  //       path: PATH,
  //       name: 'onChangeSwitchCustomRanking',
  //       args: { checked },
  //     });
  //   }
  // };

  const onChangeTabs = (tab: string) => {
    setActiveTab(tab);
  };

  const onChangeCheckedAlgo = useCallback(
    val => {
      const selected = [...selectedAlgos];
      const index = selected.findIndex(item => item === val);

      if (index > -1) {
        // uncheck
        selected.splice(index, 1);
      } else {
        // check
        selected.push(val);
      }
      setSelectedAlgos(selected);
    },
    [selectedAlgos],
  );

  const onChangeAlgorithm = (item: any, value: any, ignoreUndoAction?: boolean) => {
    try {
      const newListAlgo = produce(data.algorithms.value, (draft: any) => {
        const index = draft.findIndex((algo: any) => algo.value === item.value);
        draft[index] = {
          ...draft[index],
          ...value,
        };
      });

      onChange({
        ...data,
        algorithms: {
          ...data.algorithms,
          value: newListAlgo,
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onChangeAlgorithm.name,
        args: {},
      });
    }
  };

  const onRemoveAlgo = item => {
    try {
      const newListAlgo = data.algorithms.value.filter(algo => algo.value !== item.value);

      onChange({
        ...data,
        algorithms: {
          ...data.algorithms,
          value: newListAlgo,
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onRemoveAlgo.name,
        args: {},
      });
    }
  };

  const onApplySelect = () => {
    setIsShowSelect(false);
    setIsShowDropdownSelect(false);

    // set redux selected based on checked items when apply
    const selected = selectedAlgos.map(item =>
      isExistKey(algorithms.value, item)
        ? // checked item
          { ...mapAlgoSelected[item] }
        : // add new
          {
            value: item,
            quantity: 5,
            ...(['similarity_bought', 'similarity_seen', 'similarity_cart'].includes(item) && { last: 1 }),
            ...(item === 'recency' && { to: 'today', last: 1 }),
            ...(item === 'distance' && DEFAULT_DISTANCE_ALGORITHM),
            ...(item === 'retarget_search' && { last: 20 }),
            ...(item === 'notify' && { last: 7, interest: undefined }),
            ...(item === 'get_top' && { sort_by: undefined, sort_type: 'desc' }),
            ...(item === 'trending' && DEFAULT_TRENDING_ALGORITHM),
          },
    );

    onChange({
      ...data,
      algorithms: {
        ...data.algorithms,
        value: selected,
      },
    });
  };

  const onChangeDropdownVisible = isOpen => {
    setIsShowDropdownSelect(isOpen);
    // set checked based on redux selected when open & cancel
    const selected = algorithms.value.map(item => item.value);
    setSelectedAlgos(selected);
  };

  const onChangeShowSelect = () => {
    setIsShowSelect(true);
  };

  // Handlers Filters
  const onChangeFilters = item => {
    try {
      let newFilters = [...data.algorithms.filters];
      if (newFilters.includes(item)) {
        // uncheck
        newFilters = newFilters.filter(filter => filter !== item);
      } else {
        // check
        newFilters = [...newFilters, item];
      }

      onChange({
        ...data,
        algorithms: {
          ...data.algorithms,
          filters: newFilters,
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onRemoveAlgo.name,
        args: {},
      });
    }
  };

  // Handlers Script
  const onBlurScript = (_, editor) => {
    try {
      onChange({
        ...data,
        custom: editor.getValue(),
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onBlurScript',
        args: {},
      });
    }
  };

  const onChangeSort = (event: any) => {
    try {
      onChange({
        ...data,
        algorithms: {
          ...data.algorithms,
          sort: event.target.value,
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeSort',
        args: {},
      });
    }
  };

  const renderAlgorithmBy = (algo: any) => {
    let element: ReactNode | null = null;

    switch (algo.value) {
      case 'viral_products': {
        const { errorMessage, isDisable } = checkStatusCollection({
          listBO: listBO as any,
          itemTypeId,
          listCollection,
          field: algo.by,
          checkDisable: true,
        });

        element = (
          <AlgorithmWrapper>
            <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
              {t(translations.by.title)}:
            </RequiredLabel>
            <Select
              required
              focused={isShowErrorAlert}
              disabled={isDisable}
              showSearch
              placeholder={t(translations.selectCollection.title)}
              className="!border-t-0 !border-x-0 ants-w-100"
              value={optionNotExitsValue(listCollection, algo.by)}
              options={buildOptionArchive(listCollection, algo.by)}
              errorArchive={errorMessage}
              onChange={value => onChangeAlgorithm(algo, { by: value })}
            />
          </AlgorithmWrapper>
        );
        break;
      }
      case 'same_category': {
        element = (
          <AlgorithmWrapper>
            <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
              {t(translations.by.title)}:
            </RequiredLabel>
            <Select
              required
              showSearch
              focused={isShowErrorAlert}
              placeholder={t(translations.selectCategory.title)}
              className="!border-t-0 !border-x-0 ants-w-100"
              value={algo.by}
              options={CATEGORY_OPTIONS}
              onChange={value => onChangeAlgorithm(algo, { by: value })}
            />
          </AlgorithmWrapper>
        );
        break;
      }
      case 'recency': {
        element = (
          <>
            <AlgorithmWrapper>
              <Text type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.last.title)}:{' '}
              </Text>
              <div className="ants-flex ants-items-center">
                <InputNumber
                  value={algo.last}
                  width={90}
                  onChange={value => onChangeAlgorithm(algo, { last: value })}
                  min={1}
                  max={90}
                  required
                  // controls={false}
                />
                <Text className="ants-ml-2">{t(translations.days.title)}</Text>
              </div>
            </AlgorithmWrapper>
            <AlgorithmWrapper className="!ants-mt-1">
              <Text type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.to.title)}:{' '}
              </Text>
              <Radio.Group onChange={event => onChangeAlgorithm(algo, { to: event.target.value })} value={algo.to}>
                <Radio value="today">{t(translations.today.title)}</Radio>
                <Radio value="yesterday">{t(translations.yesterday.title)}</Radio>
              </Radio.Group>
            </AlgorithmWrapper>
          </>
        );
        break;
      }
      case 'retarget_search': {
        const { errorMessage, isDisable } = checkStatusAttr({
          listBO: listBO as any,
          itemTypeId,
          listAttribute: listAttributeRetarget,
          field: algo.by,
        });

        element = (
          <>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.by.title)}:
              </RequiredLabel>
              <StyledSelect
                required
                mode="multiple"
                loading={isLoadingAttribute}
                allowClear
                showSearch
                focused={isShowErrorAlert}
                errorArchive={errorMessage}
                disabled={isDisable}
                placeholder={t(translations.selectAField.title)}
                className="ants-w-full"
                maxTagTextLength={18}
                value={algo.by}
                {...(algo.by?.length === MAX_RETARGET_SEARCH_BY && { open: false })}
                options={buildOptionAttrArchive(listAttributeRetarget, algo.by)}
                onChange={value => onChangeAlgorithm(algo, { by: value })}
              />
            </AlgorithmWrapper>

            <AlgorithmWrapper>
              <Text type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.last.title)}:{' '}
              </Text>
              <div className="ants-flex ants-items-center">
                <InputNumber
                  value={algo.last}
                  width={90}
                  onChange={value => onChangeAlgorithm(algo, { last: value })}
                  min={1}
                  max={20}
                  required
                  // controls={false}
                />
                <Text className="ants-ml-2">{t(translations.keywords.title).toLowerCase()}</Text>
              </div>
            </AlgorithmWrapper>
          </>
        );

        break;
      }
      case 'similarity_bought':
      case 'similarity_cart':
      case 'similarity_seen':
        element = (
          <AlgorithmWrapper>
            <Text type="secondary" className="ants-text-right ants-mr-2">
              {t(translations.last.title)}:{' '}
            </Text>
            <div className="ants-flex ants-items-center">
              <InputNumber
                value={algo.last}
                width={90}
                onChange={value => onChangeAlgorithm(algo, { last: value })}
                min={1}
                max={90}
                required
                // controls={false}
              />
              <Text className="ants-ml-2">
                {itemTypeName === ITEM_TYPE_NAME.ARTICLE
                  ? t(translations.articles.title)
                  : t(translations.products.title)}
              </Text>
            </div>
          </AlgorithmWrapper>
        );

        break;
      case 'similar_content': {
        const { errorMessage, isDisable } = isCheckStatusAttr(listBO, itemTypeId, SIMILAR_CONTENT_OPTIONS, algo.by);

        element = (
          <>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.by.title)}:
              </RequiredLabel>
              <StyledSelect
                required
                mode="multiple"
                allowClear
                showSearch
                focused={isShowErrorAlert}
                errorArchive={errorMessage}
                disabled={isDisable}
                placeholder={t(translations.selectContent.title)}
                className="ants-w-full"
                maxTagTextLength={18}
                value={algo.by}
                options={SIMILAR_CONTENT_OPTIONS}
                onChange={value => onChangeAlgorithm(algo, { by: value })}
              />
            </AlgorithmWrapper>
          </>
        );

        break;
      }
      case 'distance': {
        const { errorMessage, isDisable } = isCheckStatusAttr(listBO, itemTypeId, listAttribute, algo.by);
        element = (
          <>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.by.title)}:
              </RequiredLabel>
              <Select
                required
                disabled={isDisable}
                focused={isShowErrorAlert}
                loading={isLoadingAttribute}
                showSearch
                placeholder={t(translations.selectAField.title)}
                className="ants-w-100"
                value={algo.by}
                options={buildOptionAttrArchive(listAttribute, algo.by)}
                onChange={value => onChangeAlgorithm(algo, { by: value })}
                errorArchive={errorMessage}
              />
            </AlgorithmWrapper>

            <AlgorithmWrapper>
              <div></div>
              <Space direction="vertical" size={10}>
                <Select
                  showSearch
                  defaultValue={OPERATOR_OPTIONS.GREATER_THAN.value}
                  className="ants-w-100"
                  value={algo.operator}
                  options={Object.values(OPERATOR_OPTIONS)}
                  onChange={value =>
                    onChangeAlgorithm(algo, { operator: value, rangeType: ['value', 'value'], rangeValue: [0, 0] })
                  }
                />
                {isOperatorInRange(algo.operator) ? (
                  <>
                    <div className="ants-grid ants-grid-cols-2 ants-items-end ants-gap-x-2">
                      <Select
                        defaultValue={COMPARE_TYPE.VALUE.value}
                        className="ants-w-100"
                        value={algo.rangeType[0]}
                        options={Object.values(COMPARE_TYPE)}
                        onChange={value => {
                          const toType = algo.rangeType[1];
                          const [fromValue, toValue] = algo.rangeValue;
                          onChangeAlgorithm(algo, { rangeType: [value, toType] });

                          if (fromValue > 100) {
                            onChangeAlgorithm(algo, {
                              rangeType: [value, toType],
                              rangeValue: [100, toValue],
                            });
                          }
                        }}
                      />
                      <InputNumber
                        width={'100%'}
                        min={0}
                        max={algo.rangeType[0] === COMPARE_TYPE.PERCENTAGE.value ? 100 : Infinity}
                        required
                        defaultValue={0}
                        value={algo.rangeValue[0]}
                        onChange={value => onChangeAlgorithm(algo, { rangeValue: [value, algo.rangeValue[1]] })}
                      />
                    </div>
                    <div className="ants-grid ants-grid-cols-2 ants-items-end ants-gap-x-2">
                      <Select
                        defaultValue={COMPARE_TYPE.VALUE.value}
                        className="ants-w-100"
                        value={algo.rangeType[1]}
                        options={Object.values(COMPARE_TYPE)}
                        onChange={value => {
                          const fromType = algo.rangeType[0];
                          const [fromValue, toValue] = algo.rangeValue;
                          onChangeAlgorithm(algo, { rangeType: [fromType, value] });

                          if (toValue > 100) {
                            onChangeAlgorithm(algo, {
                              rangeType: [fromType, value],
                              rangeValue: [fromValue, 100],
                            });
                          }
                        }}
                      />
                      <InputNumber
                        width={'100%'}
                        min={0}
                        max={algo.rangeType[1] === COMPARE_TYPE.PERCENTAGE.value ? 100 : Infinity}
                        required
                        defaultValue={0}
                        value={algo.rangeValue[1]}
                        onChange={value => onChangeAlgorithm(algo, { rangeValue: [algo.rangeValue[0], value] })}
                      />
                    </div>
                  </>
                ) : (
                  <div className="ants-grid ants-grid-cols-2 ants-items-end ants-gap-x-2">
                    <Select
                      defaultValue={COMPARE_TYPE.VALUE.value}
                      className="ants-w-100"
                      value={algo.compareType}
                      options={Object.values(COMPARE_TYPE)}
                      onChange={value => {
                        onChangeAlgorithm(algo, { compareType: value });

                        if (algo.compareValue > 100) {
                          onChangeAlgorithm(algo, { compareType: value, compareValue: 100 });
                        }
                      }}
                    />
                    <InputNumber
                      width={'100%'}
                      min={0}
                      max={algo.compareType === COMPARE_TYPE.PERCENTAGE.value ? 100 : Infinity}
                      required
                      defaultValue={0}
                      value={algo.compareValue}
                      onChange={value => onChangeAlgorithm(algo, { compareValue: value })}
                    />
                  </div>
                )}
              </Space>
            </AlgorithmWrapper>
            <AlgorithmWrapper className="!ants-items-baseline">
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.by.title)}:
              </RequiredLabel>
              <SelectEventAttribute
                isShowErrorAlert={isShowErrorAlert}
                journeySettings={journeySettings}
                eventMetadata={algo.with || {}}
                onChange={(eventMetaData, ignoreUndoAction) =>
                  onChangeAlgorithm(algo, { with: eventMetaData }, ignoreUndoAction)
                }
              />
            </AlgorithmWrapper>
          </>
        );

        break;
      }
      case 'notify': {
        const optionNotify = INTEREST_OPTIONS.filter(opt => {
          if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
            return opt.value === 'wishlist';
          }
          return true;
        });
        element = (
          <>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.interest.title)}:{' '}
              </RequiredLabel>
              <Select
                required
                showSearch
                focused={isShowErrorAlert}
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.interest}
                options={optionNotify}
                onChange={value => onChangeAlgorithm(algo, { interest: value })}
              />
            </AlgorithmWrapper>
            <AlgorithmWrapper>
              <Text type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.last.title)}:{' '}
              </Text>
              <div className="ants-flex ants-items-center">
                <InputNumber
                  value={algo.last}
                  width={90}
                  onChange={value => onChangeAlgorithm(algo, { last: value })}
                  min={1}
                  max={90}
                  required
                  // controls={false}
                />
                <Text className="ants-ml-2">{t(translations.days.title)}</Text>
              </div>
            </AlgorithmWrapper>
          </>
        );

        break;
      }
      case 'get_top': {
        const { errorMessage, isDisable } = checkStatusAttr({
          listBO: listBO as any,
          itemTypeId,
          listAttribute,
          field: algo.sort_by,
        });

        const options = buildOptionAttrArchive(
          listAttribute.filter(({ isSort }) => isSort === 1),
          algo.sort_by,
        );

        element = (
          <>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.with.title).toLowerCase()}:{' '}
              </RequiredLabel>
              <Select
                required
                showSearch
                disabled={isDisable}
                status={!!errorMessage ? 'error' : ''}
                errorMsg={errorMessage}
                focused={isShowErrorAlert}
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.sort_by}
                options={options}
                onChange={value => onChangeAlgorithm(algo, { sort_by: value })}
              />
            </AlgorithmWrapper>
            <AlgorithmWrapper>
              <Text type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.sortBy.title).toLowerCase()}:{' '}
              </Text>
              <Select
                showSearch
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.sort_type}
                options={SORT_TYPE_OPTIONS}
                onChange={value => onChangeAlgorithm(algo, { sort_type: value })}
              />
            </AlgorithmWrapper>
          </>
        );

        break;
      }

      case 'trending': {
        element = (
          <>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.by.title).toLowerCase()}:{' '}
              </RequiredLabel>
              <Select
                required
                showSearch
                // disabled={isDisable}
                // status={!!errorMessage ? 'error' : ''}
                // errorMsg={errorMessage}
                focused={isShowErrorAlert}
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.by}
                options={ARTICLE_TREND_BY_OPTIONS}
                onChange={value => onChangeAlgorithm(algo, { by: value })}
              />
            </AlgorithmWrapper>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.in.title).toLowerCase()}:{' '}
              </RequiredLabel>
              <Select
                required
                showSearch
                // disabled={isDisable}
                // status={!!errorMessage ? 'error' : ''}
                // errorMsg={errorMessage}
                focused={isShowErrorAlert}
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.in}
                options={ARTICLE_TREND_IN_OPTIONS}
                onChange={value => onChangeAlgorithm(algo, { in: value })}
              />
            </AlgorithmWrapper>
            {algo.by === 'compare_with_before' && (
              <>
                <AlgorithmWrapper>
                  <div></div>
                  <Space direction="vertical" size={10}>
                    <Select
                      showSearch
                      defaultValue={OPERATOR_OPTIONS.GREATER_THAN.value}
                      className="ants-w-100"
                      value={algo.operator}
                      options={Object.values(OPERATOR_OPTIONS)}
                      onChange={value =>
                        onChangeAlgorithm(algo, { operator: value, rangeType: ['value', 'value'], rangeValue: [0, 0] })
                      }
                    />
                    {isOperatorInRange(algo.operator) ? (
                      <>
                        <div className="ants-grid ants-grid-cols-2 ants-items-end ants-gap-x-2">
                          <Select
                            defaultValue={COMPARE_TYPE.VALUE.value}
                            className="ants-w-100"
                            value={algo.rangeType[0]}
                            options={Object.values(COMPARE_TYPE)}
                            onChange={value => {
                              const [fromValue, toValue] = algo.rangeValue;

                              onChangeAlgorithm(algo, {
                                rangeType: [value, value],
                                rangeValue: [fromValue, toValue],
                              });
                            }}
                          />
                          <InputNumber
                            width={'100%'}
                            min={0}
                            required
                            defaultValue={0}
                            value={algo.rangeValue[0]}
                            onChange={value => onChangeAlgorithm(algo, { rangeValue: [value, algo.rangeValue[1]] })}
                          />
                        </div>
                        <div className="ants-grid ants-grid-cols-2 ants-items-end ants-gap-x-2">
                          <Select
                            defaultValue={COMPARE_TYPE.VALUE.value}
                            className="ants-w-100"
                            value={algo.rangeType[1]}
                            options={Object.values(COMPARE_TYPE)}
                            onChange={value => {
                              const [fromValue, toValue] = algo.rangeValue;

                              onChangeAlgorithm(algo, {
                                rangeType: [value, value],
                                rangeValue: [fromValue, toValue],
                              });
                            }}
                          />
                          <InputNumber
                            width={'100%'}
                            min={0}
                            required
                            defaultValue={0}
                            value={algo.rangeValue[1]}
                            onChange={value => onChangeAlgorithm(algo, { rangeValue: [algo.rangeValue[0], value] })}
                          />
                        </div>
                      </>
                    ) : (
                      <div className="ants-grid ants-grid-cols-2 ants-items-end ants-gap-x-2">
                        <Select
                          defaultValue={COMPARE_TYPE.VALUE.value}
                          className="ants-w-100"
                          value={algo.compareType}
                          options={Object.values(COMPARE_TYPE)}
                          onChange={value => {
                            onChangeAlgorithm(algo, { compareType: value });
                          }}
                        />
                        <InputNumber
                          width={'100%'}
                          min={0}
                          required
                          defaultValue={0}
                          value={algo.compareValue}
                          onChange={value => onChangeAlgorithm(algo, { compareValue: value })}
                        />
                      </div>
                    )}
                  </Space>
                </AlgorithmWrapper>
              </>
            )}
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.sortBy.title).toLowerCase()}:{' '}
              </RequiredLabel>
              <Select
                required
                showSearch
                // disabled={isDisable}
                // status={!!errorMessage ? 'error' : ''}
                // errorMsg={errorMessage}
                focused={isShowErrorAlert}
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.sort_by}
                options={ARTICLE_TREND_SORT_BY_OPTIONS}
                onChange={value => onChangeAlgorithm(algo, { sort_by: value })}
              />
            </AlgorithmWrapper>
            <AlgorithmWrapper>
              <RequiredLabel type="secondary" className="ants-text-right ants-mr-2">
                {t(translations.sortOrder.title).toLowerCase()}:{' '}
              </RequiredLabel>
              <Select
                required
                showSearch
                // disabled={isDisable}
                // status={!!errorMessage ? 'error' : ''}
                // errorMsg={errorMessage}
                focused={isShowErrorAlert}
                placeholder={t(translations.selectAnItem.title)}
                className="!border-t-0 !border-x-0 ants-w-100"
                value={algo.sort_order}
                options={SORT_TYPE_OPTIONS}
                onChange={value => onChangeAlgorithm(algo, { sort_order: value })}
              />
            </AlgorithmWrapper>
          </>
        );
        break;
      }
      default: {
        element = null;
        break;
      }
    }
    return element;
  };

  const onDragEndAlgorithms = (result: DropResult) => {
    try {
      const { source, destination } = result;
      const { index: sourceIndex } = source || {};
      const { index: destinationIndex } = destination || {};

      if (destinationIndex != null) {
        const reorderAlgorithms = reorder(algorithms.value, sourceIndex, destinationIndex);

        onChange(
          produce(data, draft => {
            draft.algorithms.value = reorderAlgorithms;
          }) as any,
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onDragEndAlgorithms',
        args: {},
      });
    }
  };

  const renderAlgorithmOptions = useMemo(() => {
    const groupOptions: JSX.Element[] = [];

    listAlgoByTypes.forEach((algosByType = [], type) => {
      // Temporarily hide
      const draftAlgosByType =
        [...algosByType].filter(({ value }) => !['recent_high_ctrs', 'collaborative_filtering'].includes(value)) || [];

      const numAlgoSelectedByType = draftAlgosByType.reduce((sum, current) => {
        if (selectedAlgos.includes(current.value)) return ++sum;

        return sum;
      }, 0);

      const isExpaned = expandedAlgoTypes.includes(type);

      groupOptions.push(
        <SelectMultOptGroup
          label={
            <React.Fragment>
              {!!groupOptions.length && <Divider dot className="!ants-mb-2 !ants-mt-0 !ants-border-t-2" />}

              <div
                className="ants-flex ants-text-gray ants-justify-between ants-items-center ants-cursor-pointer"
                onClick={() => {
                  if (isExpaned) {
                    setExpandAlgoTypes(oldExpand => oldExpand.filter(t => t !== type));
                    return;
                  }
                  setExpandAlgoTypes(oldExpand => [...oldExpand, type]);
                }}
              >
                <div>{`${type} (${numAlgoSelectedByType}/${draftAlgosByType.length})`}</div>
                <Icon
                  type="icon-ants-expand-more"
                  size={16}
                  className={cls(
                    {
                      'ants-rotate-180': isExpaned,
                    },
                    'ants-transition-[transform] ants-duration-150 ants-ease-in',
                  )}
                />
              </div>
            </React.Fragment>
          }
          key={type}
        >
          {draftAlgosByType.map(algo => (
            <SelectMultiOption
              className={cls(
                {
                  'ants-min-h-0 ants-max-h-0 ants-py-0': !isExpaned,
                },
                'ants-transition-all ants-ease-in ants-duration-150',
              )}
              key={algo.value}
            >
              <SelectOptionLabel
                label={algo.label}
                checked={selectedAlgos.includes(algo.value)}
                onClick={() => onChangeCheckedAlgo(algo.value)}
                disabled={selectedAlgos.length >= 3 && !selectedAlgos.includes(algo.value)}
              />
            </SelectMultiOption>
          ))}
        </SelectMultOptGroup>,
      );
    });

    return groupOptions;
  }, [onChangeCheckedAlgo, selectedAlgos, listAlgoByTypes, expandedAlgoTypes]);

  const renderTabContent = () => {
    try {
      switch (activeTab) {
        case ALGORITHM_TABS.ALGORITHMS.value:
          return (
            <React.Fragment>
              {itemTypeId === PRODUCT_ITEM_TYPE_ID || itemTypeName === ITEM_TYPE_NAME.ARTICLE ? (
                <SettingWrapper
                  label={t(translations.sort.title)}
                  labelClassName="ants-font-bold"
                  className="ants-px-2 ants-pt-5px ants-pb-15px"
                >
                  <RadioGroup
                    options={Object.values(SORT_OPTIONS)}
                    defaultValue={SORT_OPTIONS.MIX.value}
                    value={algorithms.sort || 'mix'}
                    onChange={value => onChangeSort(value)}
                  />
                </SettingWrapper>
              ) : null}

              <DragDropContext onDragEnd={onDragEndAlgorithms}>
                <Droppable droppableId="algorithms">
                  {provided => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      {algorithms.value.map((algo, index) => (
                        <Draggable key={algo.value} draggableId={algo.value} index={index}>
                          {(provided, snapshot) => (
                            <div
                              {...provided.draggableProps}
                              ref={provided.innerRef}
                              className={classNames('ants-bg-white', {
                                'ants-border ants-border-gray-2 ants-rounded-sm': snapshot.isDragging,
                              })}
                            >
                              <AlgorithmWrapper>
                                <div
                                  className={classNames('ants-flex ants-items-center ants-justify-end', {
                                    '!ants-justify-between': algorithms?.sort === SORT_OPTIONS.ORDER.value,
                                  })}
                                >
                                  <div
                                    style={{
                                      height: 16,
                                      display: algorithms?.sort === SORT_OPTIONS.ORDER.value ? 'block' : 'none',
                                    }}
                                    {...provided.dragHandleProps}
                                  >
                                    <Icon
                                      type="icon-ants-double-three-dots"
                                      size={16}
                                      className="ants-text-cus-second ants-opacity-40"
                                    />
                                  </div>
                                  <Text type="secondary" className="ants-text-right ants-mr-2">
                                    {mapListAlgo[algo.value]?.label}:{' '}
                                  </Text>
                                </div>
                                <InputNumber
                                  value={algo.quantity}
                                  width={90}
                                  onChange={value => onChangeAlgorithm(algo, { quantity: value })}
                                  min={1}
                                  max={algorithmQuantityMax}
                                  required
                                  // controls={false}
                                />
                                {algorithms.value.length > 1 ? (
                                  <Button
                                    onClick={() => onRemoveAlgo(algo)}
                                    type="text"
                                    icon={<Icon type="icon-ants-remove-slim" size={15} />}
                                    className="ants-justify-self-center"
                                  />
                                ) : null}
                              </AlgorithmWrapper>
                              {renderAlgorithmBy(algo)}

                              {index < algorithms.value.length - 1 && !snapshot.isDragging ? (
                                <Divider dashed className="!ants-mt-4 !ants-mb-3 !ants-mx-auto" width="300px" />
                              ) : null}
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>

              {itemTypeId === PRODUCT_ITEM_TYPE_ID || itemTypeName === ITEM_TYPE_NAME.ARTICLE ? (
                <div className="ants-mt-4">
                  {isShowSelect ? (
                    <SelectMulti
                      open={isShowDropdownSelect}
                      mode="multiple"
                      label={t(translations.selectAlgorithms.title)}
                      placeholder={t(translations.multiSelectAlgorithms.title)}
                      // khong dung onChange ma dung onClick cua SelectOptionLabel
                      // onChange={() => {}}
                      placement="topLeft"
                      dropdownRender={menu => (
                        <>
                          {menu}
                          <Divider className="!ants-my-2" />
                          <div className="ants-m-2 ants-flex ants-items-center">
                            <Button onClick={onApplySelect} disabled={selectedAlgos.length === 0} type="primary">
                              {t(translations.apply.title)}
                            </Button>
                            <Button onClick={() => onChangeDropdownVisible(false)} className="ants-ml-2">
                              {t(translations.cancel.title)}
                            </Button>
                          </div>
                        </>
                      )}
                      // or focus + blur
                      onDropdownVisibleChange={onChangeDropdownVisible}
                    >
                      {renderAlgorithmOptions}
                    </SelectMulti>
                  ) : (
                    <Button type="text" onClick={onChangeShowSelect} disabled={ranking.algorithms.value.length >= 3}>
                      + {t(translations.addAlgorithms.title)}
                    </Button>
                  )}
                </div>
              ) : null}
            </React.Fragment>
          );

        case ALGORITHM_TABS.FILTERS.value:
          return (
            <div className="ants-grid ants-grid-cols-2 ants-gap-x-10 ants-gap-y-5 ants-items-center ants-px-2 ants-pb-2">
              <Text className="ants-font-bold">
                {itemTypeName === ITEM_TYPE_NAME.ARTICLE ? 'Article Filters' : 'Product Filters'}
              </Text>
              <Text className="ants-font-bold">Hide</Text>
              {listFilter.map(filter => (
                <React.Fragment key={filter.value}>
                  <Text>{filter.label}</Text>
                  <Checkbox
                    checked={algorithms.filters.includes(filter.value)}
                    onChange={() => onChangeFilters(filter.value)}
                  />
                </React.Fragment>
              ))}
            </div>
          );

        default:
          return '';
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderTabContent',
        args: {},
      });
    }
  };

  return (
    <AlgorithmsSettingWrapper>
      <SettingWrapper label={t(translations.ranking.title)} labelClassName="ants-font-bold">
        <RadioGroup options={Object.values(RANKING_TYPE)} value={ranking.type} onChange={onChangeType} />
      </SettingWrapper>

      {ranking.type === RANKING_TYPE.ALGORITHMS.value ? (
        <StyledTabs defaultActiveKey={activeTab} activeKey={activeTab} onChange={onChangeTabs} destroyInactiveTabPane>
          {Object.values(ALGORITHM_TABS)
            .filter(({ value }) => {
              if (itemTypeId === PRODUCT_ITEM_TYPE_ID || itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
                return true;
              }
              return value === ALGORITHM_TABS.ALGORITHMS.value;
            })
            .map(({ value, label }) => (
              <TabPane tab={label} key={value}>
                {renderTabContent()}
              </TabPane>
            ))}
        </StyledTabs>
      ) : ranking.type === RANKING_TYPE.CUSTOM.value ? (
        <div className="ants-mt-4">
          <EditorScript
            expandModalLabel={t(translations.customCode.title)}
            value={ranking.custom}
            onBlur={onBlurScript}
          />
        </div>
      ) : null}
    </AlgorithmsSettingWrapper>
  );
};
