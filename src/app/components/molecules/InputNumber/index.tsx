// Libraries
import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

// Components
import { InputNumberProps as AntdInputNumberProps } from 'antd/lib/input-number';

import { Icon, RequiredLabel, Text } from 'app/components/atoms';

// Styled

import { InputNumberWrapper } from './styled';
import { handleError } from 'app/utils/handleError';

// Types
import { valueType } from 'antd/lib/statistic/utils';

// Locales
import { translations } from 'locales/translations';

// Utils
import { getPreventKeyboardAction } from 'app/utils/web';

export interface InputNumberProps extends AntdInputNumberProps {
  focused?: boolean;
  errorMsg?: ReactNode;
  label?: ReactNode;
  disableUndo?: boolean;
}

const PATH = 'src/app/components/molecules/InputNumber/index.tsx';

export const InputNumber: React.FC<InputNumberProps> = props => {
  const { label, required, focused, errorMsg, onChange, ...restOf } = props;

  //I18n
  const { t } = useTranslation();

  const [isFocused, setFocused] = useState(false);

  const requiredMsg = useMemo(() => {
    let msg = '';
    const isEmptyValue = props.value == null;

    if (required && isEmptyValue && isFocused) {
      msg = t(translations.messageError.fieldIsRequired.message);
    }

    return msg;
  }, [props.value, required, isFocused, t]);

  useEffect(() => {
    if (focused) {
      setFocused(focused);
    }
  }, [focused]);

  const onChangeInputNumber = (value: valueType | null) => {
    try {
      if (typeof onChange !== 'function') {
        return;
      }

      if (required) {
        if (value !== null) {
          onChange(value);
        }
        return;
      }

      onChange(value);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeInputNumber',
        args: {},
      });
    }
  };

  const renderRequiredLabel = (label: ReactNode) => {
    if (required) {
      return (
        <RequiredLabel className="ants-mb-5px" color="#666666">
          {label}
        </RequiredLabel>
      );
    }

    return (
      <Text className="ants-mb-5px" color="#666666">
        {label}
      </Text>
    );
  };

  const listDisableActions: Parameters<typeof getPreventKeyboardAction>[0] = [];

  if (props.disableUndo) {
    listDisableActions.push('undo');
  }

  return (
    <div>
      {label && renderRequiredLabel(label)}

      <InputNumberWrapper
        {...restOf}
        required={required}
        onChange={onChangeInputNumber}
        onBlur={e => {
          if (!focused) {
            setFocused(true);
          }

          props.onBlur && props.onBlur(e);
        }}
        upHandler={<Icon type={'icon-ants-caret-up'} size={9} />}
        downHandler={<Icon type={'icon-ants-caret-down'} size={9} />}
        {...getPreventKeyboardAction(listDisableActions)}
      />
      {(props.status === 'error' && errorMsg) || requiredMsg ? (
        <Text color="#ff4d4f" className="ants-ml-2 ants-mt-5px">
          {errorMsg || requiredMsg}
        </Text>
      ) : null}
    </div>
  );
};

InputNumber.defaultProps = {
  required: false,
  width: 57,
};
