// Libraries
import React from 'react';
import classnames from 'classnames';

// Components
import { Button } from 'app/components/atoms';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

interface HistoryProps {
  disabled?: boolean;
  onClick?: () => void;
}

export const Redo: React.FC<HistoryProps> = props => {
  // Hooks
  const { t } = useTranslation();

  return (
    <Button className="at-ql-redo" title={t(translations.textEditor.toolbar.redo.tooltip)} onClick={props.onClick}>
      <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
        <path d="M0 0h24v24H0z" fill="none" />
        <path
          className={classnames('ql-fill', { disabled: props.disabled })}
          d="M18.4 10.6C16.55 8.99 14.15 8 11.5 8c-4.65 0-8.58 3.03-9.96 7.22L3.9 16c1.05-3.19 4.05-5.5 7.6-5.5 1.95 0 3.73.72 5.12 1.88L13 16h9V7l-3.6 3.6z"
        />
      </svg>
    </Button>
  );
};

export const Undo: React.FC<HistoryProps> = props => {
  // Hooks
  const { t } = useTranslation();

  return (
    <Button className="at-ql-undo" onClick={props.onClick} title={t(translations.textEditor.toolbar.undo.tooltip)}>
      <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
        <path d="M0 0h24v24H0z" fill="none" />
        <path
          className="ql-fill"
          d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"
        />
      </svg>
    </Button>
  );
};
