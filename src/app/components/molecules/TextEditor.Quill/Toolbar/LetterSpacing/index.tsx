// Libraries
import React, { useEffect, useState } from 'react';

import classnames from 'classnames';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
import { LetterSpacingConfig } from '../../types';

interface LetterSpacingProps {
  preselectedLetterSpacing?: LetterSpacingConfig;
  whitelist: LetterSpacingConfig[];

  onChange?: (option: LetterSpacingConfig) => void;
}

export const LetterSpacing: React.FC<LetterSpacingProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectedLetterSpacing, whitelist, onChange } = props;

  // States
  const [selectedLetterSpacing, setSelectedLetterSpacing] = useState<LetterSpacingConfig>(whitelist[0]);

  // Initial Effects
  useEffect(() => {
    if (preselectedLetterSpacing && preselectedLetterSpacing.value !== selectedLetterSpacing.value) {
      setSelectedLetterSpacing(preselectedLetterSpacing);
    }
  }, [preselectedLetterSpacing]);

  // Handlers
  const onClickOptions = option => {
    setSelectedLetterSpacing(option);

    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu style={{ width: 70 }}>
      {whitelist.map((letterSpacing, index) => (
        <Menu.Item
          className={classnames({ selected: letterSpacing.value === selectedLetterSpacing.value })}
          key={index}
          onClick={() => onClickOptions(letterSpacing)}
        >
          {letterSpacing.label || letterSpacing.value}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.letterSpacing.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-letter-spacing">
          <svg xmlns="http://www.w3.org/2000/svg" width="27" height="23" viewBox="0 0 27 23">
            <text
              className="ql-fill"
              transform="translate(1 18)"
              fill="#005fb8"
              stroke="rgba(0,0,0,0)"
              strokeWidth="1"
              fontSize="16"
              fontFamily="Roboto-Bold, Roboto"
              fontWeight="700"
              letterSpacing="-0.1em"
            >
              <tspan x="0" y="0">
                V/A
              </tspan>
            </text>
          </svg>

          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
