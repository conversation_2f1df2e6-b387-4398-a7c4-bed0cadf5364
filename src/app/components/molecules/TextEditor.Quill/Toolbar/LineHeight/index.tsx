import React, { useEffect, useState } from 'react';

import classnames from 'classnames';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
import { LineHeightConfig } from '../../types';

interface LineHeightProps {
  preselectedLineHeight?: LineHeightConfig;
  whitelist: LineHeightConfig[];

  onChange?: (option: LineHeightConfig) => void;
}

export const LineHeight: React.FC<LineHeightProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectedLineHeight, whitelist, onChange } = props;

  // States
  const [selectedLineHeight, setSelectedLineHeight] = useState<LineHeightConfig>(whitelist[0]);

  // Initial Effects
  useEffect(() => {
    if (preselectedLineHeight && preselectedLineHeight.value !== selectedLineHeight.value) {
      setSelectedLineHeight(preselectedLineHeight);
    }
  }, [preselectedLineHeight]);

  // Handlers
  const onClickOptions = option => {
    setSelectedLineHeight(option);

    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu style={{ width: 70 }}>
      {whitelist.map((lineHeight, index) => (
        <Menu.Item
          className={classnames({ selected: lineHeight.value === selectedLineHeight.value })}
          key={index}
          onClick={() => onClickOptions(lineHeight)}
        >
          {lineHeight.label || lineHeight.value}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.lineHeight.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-line-height">
          <svg xmlns="http://www.w3.org/2000/svg" className="ql-fill" height="24px" viewBox="0 0 24 24" width="24px">
            <path d="M0 0h24v24H0V0z" fill="none" />
            <path d="M6 7h2.5L5 3.5 1.5 7H4v10H1.5L5 20.5 8.5 17H6V7zm4-2v2h12V5H10zm0 14h12v-2H10v2zm0-6h12v-2H10v2z" />
          </svg>

          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
