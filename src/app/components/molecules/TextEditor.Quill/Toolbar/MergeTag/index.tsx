// Libraries
import React from 'react';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

interface MergeTagProps {
  whitelist: string[];
  onChange?: (option: string) => void;
}

export const MergeTag: React.FC<MergeTagProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { whitelist, onChange } = props;

  // Handlers
  const onClickOptions = option => {
    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu>
      {whitelist.map((mergeTag, index) => (
        <Menu.Item key={index} onClick={() => onClickOptions(mergeTag)}>
          {mergeTag}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.mergeTags.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-merge-tags">
          <svg xmlns="http://www.w3.org/2000/svg" width="16.478" height="18.417" viewBox="0 0 16.478 18.417">
            <g transform="translate(-718.367 -283.339)">
              <path
                className="ql-fill"
                d="M4.939,3.164A3.9,3.9,0,0,1,1.775-.923v-1.74q0-1.775-1.424-1.775v-2q1.424,0,1.424-1.784v-1.846A4.032,4.032,0,0,1,2.6-12.621a4.505,4.505,0,0,1,2.342-1.415l.554,1.547q-1.107.422-1.151,2.364v1.907A2.85,2.85,0,0,1,2.716-5.44,2.857,2.857,0,0,1,4.342-2.654v1.9q.044,1.942,1.151,2.364Zm5.748-1.556q1.107-.422,1.151-2.364V-2.663A2.837,2.837,0,0,1,13.474-5.44a2.837,2.837,0,0,1-1.635-2.777v-1.907q-.044-1.942-1.151-2.364l.554-1.547A4.425,4.425,0,0,1,13.605-12.6a4.122,4.122,0,0,1,.8,2.615v1.767q0,1.784,1.424,1.784v2q-1.424,0-1.424,1.767V-.8a3.887,3.887,0,0,1-3.164,3.964Z"
                transform="translate(718.516 297.984)"
                stroke="rgba(0,0,0,0)"
                strokeWidth="1"
              />
            </g>
          </svg>

          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
