@import "./ColorPicker/styles";
@import "./Emphasis/styles";
@import "./FontFormatting/styles";
@import "./History/styles";
@import "./Indent/styles";
@import "./LetterSpacing/styles";
@import "./LineHeight/styles";
@import "./List/styles";
@import "./MergeTag/styles";
@import "./Script/styles";
@import "./SelectAll/styles";
@import "./TextFormatting/styles";

.ql-editor {
  padding: 7px 10px;

  a::before, a::after {
    display: none !important;
  }
}

.ql-fill {
  fill: var(--primary-color) !important;

  &.disabled {
    fill: var(--btn-disable-color) !important;
  }
}

.ql-formats {
  height: 28px !important;

  display: flex !important;
  flex-direction: row;
  align-items: center;

  margin: 15px 0 !important;
  padding: 0 15px;

  &:not(:last-child) {
    border-right: 2px dotted #D2D2D2;
  }
}

.ql-picker {
  &:hover, &.ql-expanded {
    background: var(--btn-primary-active-color);
  }

  .ql-picker-label {
    padding-left: 0 !important;

    > svg {
      display: none;
    }

    &:after {
      float: right;
      margin-right: 2.5px;

      content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='16px' viewBox='0 0 18 18' width='16px' fill='%23666666'%3E%3Cpath d='M24 24H0V0h24v24z' fill='none' opacity='.87'/%3E%3Cpath d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6-1.41-1.41z'/%3E%3C/svg%3E");
    }
  }

  .ql-picker-options {
    overflow-x: hidden;
    overflow-y: auto;

    min-width: 70px;
    max-height: 116px;

    padding: 0;

    background-color: #ffffff;
    border-radius: 3px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);

    .ql-picker-item {
      padding: 15px 10px 10px;

      &:hover {
        background-color: var(--btn-primary-active-color);
      }
    }
  }
}

.ql-stroke {
  stroke: var(--primary-color) !important;
}

.ql-toolbar {
  cursor: default;

  button {
    width: 24px !important;
    height: 24px !important;

    margin: 2px 0;
    padding: 0 !important;

    &:focus {
      color: var(--primary-color) !important;
    }

    &:hover, &.ql-active {
      background-color: var(--btn-primary-active-color);
      color: var(--primary-color) !important;
    }
  }
}

.ql-tooltip {
  border-radius: 3px !important;

  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);

  background-color: #ffffff !important;
  color: var(--primary-color) !important;

  z-index: 1001;

  &.ql-hidden[aria-keep="true"] {
    display: inherit !important;
  }

  &.ql-hidden[aria-keep="false"] {
    display: none !important;
  }
}

.ql-tooltip-arrow {
  display: none !important;
}

