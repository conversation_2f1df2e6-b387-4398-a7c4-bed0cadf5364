// Libraries
import React, { useEffect, useState } from 'react';

import classnames from 'classnames';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
interface FontWeightProps {
  preselectedFontWeight?: number;
  whitelist: number[];

  onChange?: (option: number) => void;
}

export const FontWeight: React.FC<FontWeightProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectedFontWeight, whitelist, onChange } = props;

  // States
  const [selectedFontWeight, setSelectedFontWeight] = useState<number>(whitelist[0]);

  // Initial Effects
  useEffect(() => {
    if (preselectedFontWeight && preselectedFontWeight !== selectedFontWeight) {
      setSelectedFontWeight(preselectedFontWeight);
    }
  }, [preselectedFontWeight]);

  // Handlers
  const onClickOptions = option => {
    setSelectedFontWeight(option);

    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu style={{ width: 70 }}>
      {whitelist.map((fontWeight, index) => (
        <Menu.Item
          className={classnames({ selected: fontWeight === selectedFontWeight })}
          key={index}
          onClick={() => onClickOptions(fontWeight)}
        >
          {fontWeight}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.fontWeight.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-font-weight">
          <svg xmlns="http://www.w3.org/2000/svg" width="22.5" height="17.5" viewBox="0 0 22.5 17.5">
            <g transform="translate(-713.5 -281.484)">
              <path
                className="ql-fill"
                d="M5.016,3.984v3H10.5V18.969h3V6.981h5.484v-3Z"
                transform="translate(708.984 278)"
                stroke="rgba(0,0,0,0)"
                strokeWidth="1"
              />
              <path
                className="ql-fill"
                d="M6,0,7.8,3.693,12,4.2,8.912,6.993,9.708,11,6,9.032,2.292,11l.8-4.007L0,4.2l4.2-.509Z"
                transform="translate(724 287.984)"
              />
            </g>
          </svg>

          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
