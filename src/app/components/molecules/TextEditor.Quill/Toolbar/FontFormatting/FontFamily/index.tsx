// Libraries
import React, { useEffect, useState } from 'react';
import classnames from 'classnames';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
interface FontFamilyProps {
  preselectedFont?: { label?: string; name: string };
  whitelist: Array<{ label?: string; name: string }>;

  onChange?: (option: { label?: string; name: string }) => void;
}

export const FontFamily: React.FC<FontFamilyProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectedFont, whitelist, onChange } = props;

  // States
  const [selectedFont, setSelectedFont] = useState<{ label?: string; name: string }>(whitelist[0]);

  // Initial Effects
  useEffect(() => {
    if (preselectedFont && preselectedFont.name !== selectedFont.name) {
      setSelectedFont(preselectedFont);
    }
  }, [preselectedFont]);

  // Handlers
  const onClickOptions = option => {
    setSelectedFont(option);

    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu>
      {whitelist.map((font, index) => (
        <Menu.Item
          className={classnames({ selected: font.name === selectedFont.name })}
          style={{ fontFamily: font.name }}
          key={index}
          onClick={() => onClickOptions(font)}
        >
          {font.label || font.name}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.fontFamily.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-font">
          <span style={{ fontFamily: selectedFont.name }}>{selectedFont.label || selectedFont.name}</span>
          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
