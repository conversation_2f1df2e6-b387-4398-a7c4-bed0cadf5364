// Libraries
import React, { useEffect, useState } from 'react';
import classnames from 'classnames';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
import { FontSizeConfig } from '../../../types';

interface FontSizeProps {
  preselectedFontSize?: FontSizeConfig;
  whitelist: Array<FontSizeConfig>;

  onChange?: (option: FontSizeConfig) => void;
}

export const FontSize: React.FC<FontSizeProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectedFontSize, whitelist, onChange } = props;

  // States
  const [selectedFontSize, setSelectedFontSize] = useState<FontSizeConfig>(whitelist[0]);

  // Initial Effects
  useEffect(() => {
    if (preselectedFontSize && preselectedFontSize.value !== selectedFontSize.value) {
      setSelectedFontSize(preselectedFontSize);
    }
  }, [preselectedFontSize]);

  // Handlers
  const onClickOptions = option => {
    setSelectedFontSize(option);

    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu style={{ width: 70 }}>
      {whitelist.map((font, index) => (
        <Menu.Item
          className={classnames({ selected: font.value === selectedFontSize.value })}
          key={index}
          onClick={() => onClickOptions(font)}
        >
          {font.label || font.value}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.fontSize.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-size">
          <span>{selectedFontSize.label || selectedFontSize.value}</span>
          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
