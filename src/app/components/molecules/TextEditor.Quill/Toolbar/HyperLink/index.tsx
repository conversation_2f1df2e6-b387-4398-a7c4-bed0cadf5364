// Libraries
import React, { useState } from 'react';

// Components
import { Button, Icon, Input } from 'app/components/atoms';
import { Form, Modal } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

interface HyperLinkProps {
  onChange?: (link: string) => void;
}

export const HyperLink: React.FC<HyperLinkProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { onChange } = props;

  // States
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const [form] = Form.useForm();

  // Handlers
  const onClickShowModal = () => {
    setModalVisible(true);
  };

  const handleOnCancelModal = () => {
    setModalVisible(false);
  };
  const handleOnOkModal = () => {
    setModalVisible(false);

    if (onChange) {
      onChange(form.getFieldValue('link'));

      form.resetFields();
    }
  };

  return (
    <>
      <Button icon={<Icon size={24} type="icon-ants-hyperlink" />} onClick={onClickShowModal} />

      <Modal
        centered
        okText={t(translations.save.title)}
        visible={modalVisible}
        onCancel={handleOnCancelModal}
        onOk={handleOnOkModal}
      >
        <Form
          form={form}
          initialValues={{ link: APP_CONFIG.SITE_URL }}
          labelAlign="left"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 19 }}
        >
          <Form.Item className="ants-items-center" label={t(translations.textEditor.toolbar.link.tooltip)} name="link">
            <Input autoFocus />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
