// Libraries
import React, { useEffect, useState } from 'react';

import classnames from 'classnames';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
import { TextAlignConfig } from '../../../types';

interface TextAlignProps {
  preselectedTextAlign?: TextAlignConfig;
  onChange?: (option: TextAlignConfig) => void;
}

export const TextAlign: React.FC<TextAlignProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectedTextAlign, onChange } = props;

  // States
  const [selectedTextAlign, setSelectedTextAlign] = useState<TextAlignConfig>('left');

  // Initial Effects
  useEffect(() => {
    if (preselectedTextAlign && preselectedTextAlign !== selectedTextAlign) {
      setSelectedTextAlign(preselectedTextAlign);
    }
  }, [preselectedTextAlign]);

  // Handlers
  const onClickOptions = option => {
    setSelectedTextAlign(option);

    if (onChange) {
      onChange(option);
    }
  };

  const iconAlignCenter = (
    <svg xmlns="http://www.w3.org/2000/svg" className="ql-fill" height="26px" viewBox="0 0 24 24" width="26px">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z" />
    </svg>
  );
  const iconAlignLeft = (
    <svg xmlns="http://www.w3.org/2000/svg" className="ql-fill" height="24px" viewBox="0 0 24 24" width="24px">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z" />
    </svg>
  );
  const iconAlignJustify = (
    <svg xmlns="http://www.w3.org/2000/svg" className="ql-fill" height="24px" viewBox="0 0 24 24" width="24px">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M3 21h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18V7H3v2zm0-6v2h18V3H3z" />
    </svg>
  );
  const iconAlignRight = (
    <svg xmlns="http://www.w3.org/2000/svg" className="ql-fill" width="26px" height="26px" viewBox="0 0 24 24">
      <path
        d="M-8.625-11.443v1.887h11.5v-1.887Zm0,6.693H8.625V-6.682H-8.625Zm0-11.5v1.932h5.75V-16.25Z"
        transform="translate(11.625 1.25) rotate(180)"
      />
    </svg>
  );

  let dropdownOverlay = (
    <Menu style={{ width: 50 }}>
      <Menu.Item
        key="left"
        className={classnames({ selected: selectedTextAlign === 'left' })}
        title={t(translations.textEditor.toolbar.textAlign.options.left)}
        onClick={() => onClickOptions('left')}
      >
        {iconAlignLeft}
      </Menu.Item>
      <Menu.Item
        key="center"
        className={classnames({ selected: selectedTextAlign === 'center' })}
        title={t(translations.textEditor.toolbar.textAlign.options.center)}
        onClick={() => onClickOptions('center')}
      >
        {iconAlignCenter}
      </Menu.Item>
      <Menu.Item
        key="right"
        className={classnames({ selected: selectedTextAlign === 'right' })}
        title={t(translations.textEditor.toolbar.textAlign.options.right)}
        onClick={() => onClickOptions('right')}
      >
        {iconAlignRight}
      </Menu.Item>
      <Menu.Item
        key="justify"
        className={classnames({ selected: selectedTextAlign === 'justify' })}
        title={t(translations.textEditor.toolbar.textAlign.options.justify)}
        onClick={() => onClickOptions('justify')}
      >
        {iconAlignJustify}
      </Menu.Item>
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.textAlign.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-text-align">
          {selectedTextAlign === 'left' && iconAlignLeft}
          {selectedTextAlign === 'center' && iconAlignCenter}
          {selectedTextAlign === 'right' && iconAlignRight}
          {selectedTextAlign === 'justify' && iconAlignJustify}

          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
