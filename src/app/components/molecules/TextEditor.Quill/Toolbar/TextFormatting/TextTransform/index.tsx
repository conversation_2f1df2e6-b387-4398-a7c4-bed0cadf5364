// Libraries
import React, { useEffect, useState } from 'react';

// Components
import { Icon } from 'app/components/atoms';
import { Dropdown, Menu } from 'app/components/molecules';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import classnames from 'classnames';

// Types
import { TextTransformConfig } from '../../../types';

interface TextTransformProps {
  preselectTextTransform?: TextTransformConfig;
  onChange?: (option: TextTransformConfig) => void;
}

export const TextTransform: React.FC<TextTransformProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props
  const { preselectTextTransform, onChange } = props;

  // States
  const [selectedTextTransform, setSelectedTextTransform] = useState<TextTransformConfig>('none');

  // Initial Effects
  useEffect(() => {
    if (preselectTextTransform && preselectTextTransform !== selectedTextTransform) {
      setSelectedTextTransform(preselectTextTransform);
    }
  }, [preselectTextTransform]);

  // Handlers
  const onClickOptions = (option: TextTransformConfig) => {
    setSelectedTextTransform(option);

    if (onChange) {
      onChange(option);
    }
  };

  let dropdownOverlay = (
    <Menu style={{ width: 100 }}>
      <Menu.Item
        key="none"
        className={classnames({ selected: 'none' === selectedTextTransform })}
        onClick={() => onClickOptions('none')}
      >
        {t(translations.textEditor.toolbar.textTransform.options.none)}
      </Menu.Item>
      <Menu.Item
        key="capitalize"
        className={classnames({ selected: 'capitalize' === selectedTextTransform })}
        onClick={() => onClickOptions('capitalize')}
      >
        {t(translations.textEditor.toolbar.textTransform.options.capitalize)}
      </Menu.Item>
      <Menu.Item
        key="uppercase"
        className={classnames({ selected: 'uppercase' === selectedTextTransform })}
        onClick={() => onClickOptions('uppercase')}
      >
        {t(translations.textEditor.toolbar.textTransform.options.uppercase)}
      </Menu.Item>
      <Menu.Item
        key="lowercase"
        className={classnames({ selected: 'lowercase' === selectedTextTransform })}
        onClick={() => onClickOptions('lowercase')}
      >
        {t(translations.textEditor.toolbar.textTransform.options.lowercase)}
      </Menu.Item>
    </Menu>
  );

  return (
    <div title={t(translations.textEditor.toolbar.textTransform.tooltip)}>
      <Dropdown overlay={dropdownOverlay} placement="bottom" trigger={['click']}>
        <div className="at-ql-text-transform">
          <svg xmlns="http://www.w3.org/2000/svg" width="18.5" height="23" viewBox="0 0 18.5 23">
            <g transform="translate(-713.5 -279)">
              <text
                className="ql-fill"
                transform="translate(725 297)"
                stroke="rgba(0,0,0,0)"
                strokeWidth="1"
                fontSize="16"
                fontFamily="Roboto-Bold, Roboto"
                fontWeight="700"
              >
                <tspan x="0" y="0">
                  t
                </tspan>
              </text>
              <path
                className="ql-fill"
                d="M5.016,3.984v3H10.5v12h3v-12h5.484v-3Z"
                transform="translate(708.984 278)"
                stroke="rgba(0,0,0,0)"
                strokeWidth="1"
              />
            </g>
          </svg>

          <Icon size={8} type="icon-ants-angle-left" />
        </div>
      </Dropdown>
    </div>
  );
};
