// Libraries
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactQuill, { Quill } from 'react-quill';

import { flattenDeep, isEqual, uniq } from 'lodash';

// Components
import { ColorPicker } from 'app/components/molecules';

import { ClearFormatting } from './Toolbar/ClearFormatting';
import { Emphasis, EmphasisType } from './Toolbar/Emphasis';
import { Redo, Undo } from './Toolbar/History';
import { HyperLink } from './Toolbar/HyperLink';
import { FontFamily, FontSize, FontWeight } from './Toolbar/FontFormatting';
import { Indent, IndentType } from './Toolbar/Indent';
import { LetterSpacing } from './Toolbar/LetterSpacing';
import { LineHeight } from './Toolbar/LineHeight';
import { List, ListType } from './Toolbar/List';
import { MergeTag } from './Toolbar/MergeTag';
import { Script, ScriptType } from './Toolbar/Script';
import { handleSelectAll, SelectAll } from './Toolbar/SelectAll';
import { TextAlign, TextTransform } from './Toolbar/TextFormatting';

// Quill Parchment
import {
  FontWeightAttributor,
  LetterSpacingAttributor,
  LineHeightAttributor,
  SoftLineBreakBlot,
  TextAlignAttributor,
  TextTransformAttributor,
} from './Toolbar/parchment';

// Hooks
import { useDebounce } from 'app/hooks';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
import { Delta, RangeStatic, Sources } from 'quill';

import type {
  FontConfig,
  FontSizeConfig,
  LetterSpacingConfig,
  LineHeightConfig,
  TextEditorProps,
  TextTransformConfig,
  Toolbar,
  ToolbarProps,
} from './types';
import { TextAlignConfig, ToolbarModuleName } from './types';

// Styles
import { TextEditorWrapper, ToolbarWrapper } from './styled';

import 'react-quill/dist/quill.bubble.css';
import 'react-quill/dist/quill.snow.css';

import './Toolbar/styles.scss';

// Utils
import { isDescendant, random } from 'app/utils/common';
import { getTranslateMessage } from 'utils/messages';

const DEFAULT_FONT_CONFIGS: FontConfig[] = [
  {
    fontFamily: { name: 'Arial' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Georgia' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Helvetica' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Helvetica Neue' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Lucida Grande' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Tahoma' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Times New Roman' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Verdana' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Abril Fatface' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Aleo' },
    fontWeight: [300, 400, 700],
  },
  {
    fontFamily: { name: 'Arvo' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Bitter' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Bree Serif' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Cabin' },
    fontWeight: [400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Cookie' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Delius Swash Caps' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Dosis' },
    fontWeight: [200, 300, 400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Droid Sans' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Droid Serif' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'EB Garamond' },
    fontWeight: [400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Josefin Slab' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Just Another Hand' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Lakki Reddy' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Lato' },
    fontWeight: [100, 300, 400, 700, 900],
  },
  {
    fontFamily: { name: 'Libre Baskerville' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Lobster' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Lora' },
    fontWeight: [400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Mali' },
    fontWeight: [200, 300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Merriweather' },
    fontWeight: [300, 400, 700, 900],
  },
  {
    fontFamily: { name: 'Montserrat' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Noto Sans' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Noto Serif' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Nunito' },
    fontWeight: [200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Open Sans' },
    fontWeight: [300, 400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Oswald' },
    fontWeight: [200, 300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'PT Sans' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'PT Serif' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Pinyon Script' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Playfair Display' },
    fontWeight: [400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Poppins' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Quicksand' },
    fontWeight: [300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Raleway' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Righteous' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Roboto' },
    fontWeight: [100, 300, 400, 500, 700, 900],
  },
  {
    fontFamily: { name: 'Roboto Slab' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Rubik' },
    fontWeight: [300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Sacramento' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Sarabun' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Source Sans Pro' },
    fontWeight: [200, 300, 400, 600, 700, 900],
  },
  {
    fontFamily: { name: 'Ubuntu' },
    fontWeight: [200, 300, 400, 600, 700, 900],
  },
  {
    fontFamily: { name: 'Vollkorn' },
    fontWeight: [400, 500, 600, 700, 800, 900],
  },
];
const DEFAULT_FONT_SIZE_CONFIGS: FontSizeConfig[] = [
  { value: '8px', label: '8' },
  { value: '9px', label: '9' },
  { value: '10px', label: '10' },
  { value: '11px', label: '11' },
  { value: '12px', label: '12' },
  { value: '13px', label: '13' },
  { value: '14px', label: '14' },
  { value: '15px', label: '15' },
  { value: '16px', label: '16' },
  { value: '17px', label: '17' },
  { value: '18px', label: '18' },
  { value: '19px', label: '19' },
  { value: '20px', label: '20' },
  { value: '21px', label: '21' },
  { value: '22px', label: '22' },
  { value: '23px', label: '23' },
  { value: '24px', label: '24' },
  { value: '25px', label: '25' },
  { value: '26px', label: '26' },
  { value: '27px', label: '27' },
  { value: '28px', label: '28' },
  { value: '29px', label: '29' },
  { value: '30px', label: '30' },
  { value: '31px', label: '31' },
  { value: '32px', label: '32' },
  { value: '33px', label: '33' },
  { value: '34px', label: '34' },
  { value: '35px', label: '35' },
  { value: '36px', label: '36' },
  { value: '38px', label: '38' },
  { value: '40px', label: '40' },
  { value: '42px', label: '42' },
  { value: '44px', label: '44' },
  { value: '46px', label: '46' },
  { value: '48px', label: '48' },
  { value: '50px', label: '50' },
  { value: '52px', label: '52' },
  { value: '54px', label: '54' },
  { value: '56px', label: '56' },
  { value: '58px', label: '58' },
  { value: '60px', label: '60' },
  { value: '64px', label: '64' },
  { value: '68px', label: '68' },
  { value: '72px', label: '72' },
  { value: '76px', label: '76' },
  { value: '80px', label: '80' },
  { value: '84px', label: '84' },
  { value: '88px', label: '88' },
  { value: '92px', label: '92' },
  { value: '96px', label: '96' },
];
const DEFAULT_LETTER_SPACING_CONFIGS: LetterSpacingConfig[] = [
  { value: '-10px', label: '-10' },
  { value: '-9px', label: '-9' },
  { value: '-8px', label: '-8' },
  { value: '-7px', label: '-7' },
  { value: '-6px', label: '-6' },
  { value: '-5px', label: '-5' },
  { value: '-4px', label: '-4' },
  { value: '-3px', label: '-3' },
  { value: '-2px', label: '-2' },
  { value: '-1px', label: '-1' },
  { value: '0px', label: '0' },
  { value: '1px', label: '1' },
  { value: '2px', label: '2' },
  { value: '3px', label: '3' },
  { value: '4px', label: '4' },
  { value: '5px', label: '5' },
  { value: '6px', label: '6' },
  { value: '7px', label: '7' },
  { value: '8px', label: '8' },
  { value: '9px', label: '9' },
  { value: '10px', label: '10' },
];
const DEFAULT_LINE_HEIGHT_CONFIGS: LineHeightConfig[] = [
  { value: 'normal', label: getTranslateMessage(translations.textEditor.toolbar.lineHeight.options.default) },
  { value: '1', label: getTranslateMessage(translations.textEditor.toolbar.lineHeight.options.single) },
  { value: '2', label: getTranslateMessage(translations.textEditor.toolbar.lineHeight.options.double) },
  { value: '1.1', label: '1.1' },
  { value: '1.2', label: '1.2' },
  { value: '1.3', label: '1.3' },
  { value: '1.4', label: '1.4' },
  { value: '1.5', label: '1.5' },
  { value: '1.6', label: '1.6' },
  { value: '1.7', label: '1.7' },
  { value: '1.8', label: '1.8' },
  { value: '1.9', label: '1.9' },
  { value: '2.0', label: '2.0' },
  { value: '2.1', label: '2.1' },
  { value: '2.2', label: '2.2' },
  { value: '2.3', label: '2.3' },
  { value: '2.4', label: '2.4' },
  { value: '2.5', label: '2.5' },
  { value: '2.6', label: '2.6' },
  { value: '2.7', label: '2.7' },
  { value: '2.8', label: '2.8' },
  { value: '2.9', label: '2.9' },
  { value: '3.0', label: '2.0' },
];
const DEFAULT_MERGE_TAG_CONFIGS = [
  '$$campaign_name$$',
  '$$country$$',
  '$$country_code$$',
  '$$zip$$',
  '$$postal_code$$',
  '$$region_code$$',
  '$$region$$',
  '$$state$$',
  '$$territory$$',
  '$$province$$',
  '$$city$$',
  '$$town$$',
  '$$latitude$$',
  '$$longitude$$',
  '$$day$$',
  '$$month$$',
  '$$year$$',
  '$$date$$',
  '$$coupon_code$$',
  '$$coupon_label$$',
  '$$page_url$$',
  '$$referer_url$$',
  '$$referrer_url$$',
  '$$pages_visited$$',
  '$$time_on_site$$',
  '$$visit_timestamp$$',
  '$$page_title$$',
  '$$form_name$$',
  '$$form_first_name$$',
  '$$form_last_name$$',
  '$$form_email$$',
  '$$form_phone$$',
];

const DEFAULT_FONT_FAMILY = { name: 'Montserrat' };
const DEFAULT_FONT_SIZE: FontSizeConfig = { value: '16px', label: '16' };
const DEFAULT_FONT_WEIGHT = 400;

const DEFAULT_LETTER_SPACING: LetterSpacingConfig = { value: '0px' };
const DEFAULT_LINE_HEIGHT: LineHeightConfig = { value: 'normal' };

const DEFAULT_TEXT_ALIGN: TextAlignConfig = 'left';
const DEFAULT_TEXT_TRANSFORM: TextTransformConfig = 'none';

const DEFAULT_HISTORY_DELAY = 500;
const DEFAULT_HISTORY_MAX_TACK = 1000;

const DEFAULT_TOOLBAR: Toolbar = {
  section1: {
    group1: [
      ToolbarModuleName.FONT_FAMILY,
      ToolbarModuleName.FONT_SIZE,
      ToolbarModuleName.BOLD,
      ToolbarModuleName.ITALIC,
      ToolbarModuleName.UNDERLINE,
      ToolbarModuleName.HYPERLINK,
    ],
    group2: [ToolbarModuleName.STRIKE_THROUGH, ToolbarModuleName.SUB_SCRIPT, ToolbarModuleName.SUPER_SCRIPT],
    group3: [
      ToolbarModuleName.FONT_WEIGHT,
      ToolbarModuleName.TEXT_TRANSFORM,
      ToolbarModuleName.TEXT_COLOR,
      ToolbarModuleName.BACKGROUND_COLOR,
      ToolbarModuleName.MERGE_TAG,
    ],
  },
  section2: {
    group1: [
      ToolbarModuleName.LIST_ORDERED,
      ToolbarModuleName.LIST_BULLET,
      ToolbarModuleName.TEXT_ALIGN,
      ToolbarModuleName.LINE_HEIGHT,
      ToolbarModuleName.LETTER_SPACING,
      ToolbarModuleName.INDENT_INCREASE,
      ToolbarModuleName.INDENT_DECREASE,
    ],
    group2: [
      ToolbarModuleName.UNDO,
      ToolbarModuleName.REDO,
      ToolbarModuleName.SELECT_ALL,
      ToolbarModuleName.CLEAR_FORMATTING,
    ],
  },
};

const ToolbarComponent = React.forwardRef<React.MutableRefObject<any>, ToolbarProps>((props, ref) => {
  // Hooks
  const { t } = useTranslation();

  // Props Value
  const { config = {}, defaultFormat = {}, handlers = {}, id, toolbar } = props;
  const { style } = props;

  const { font = DEFAULT_FONT_CONFIGS } = config;
  const { fontSize = DEFAULT_FONT_SIZE_CONFIGS } = config;
  const { letterSpacing = DEFAULT_LETTER_SPACING_CONFIGS } = config;
  const { lineHeight = DEFAULT_LINE_HEIGHT_CONFIGS } = config;
  const { mergeTags = DEFAULT_MERGE_TAG_CONFIGS } = config;

  const { fontFamily: defaultFontFamily = DEFAULT_FONT_FAMILY } = defaultFormat;
  const { fontSize: defaultFontSize = DEFAULT_FONT_SIZE } = defaultFormat;
  const { fontWeight: defaultFontWeight = DEFAULT_FONT_WEIGHT } = defaultFormat;
  const { letterSpacing: defaultLetterSpacing = DEFAULT_LETTER_SPACING } = defaultFormat;
  const { lineHeight: defaultLineHeight = DEFAULT_LINE_HEIGHT } = defaultFormat;
  const { textAlign: defaultTextAlign = DEFAULT_TEXT_ALIGN } = defaultFormat;
  const { textTransform: defaultTextTransform = DEFAULT_TEXT_TRANSFORM } = defaultFormat;

  // Refs
  const { historyRef, quillRef } = (ref as React.MutableRefObject<any>).current;
  const { redoRef, undoRef } = (historyRef as React.MutableRefObject<any>).current;

  // States
  const [selectedFontFamily, setSelectedFontFamily] = useState<{ label?: string; name: string }>(defaultFontFamily);
  const [selectedFontSize, setSelectedFontSize] = useState<FontSizeConfig>(defaultFontSize);
  const [selectedFontWeight, setSelectedFontWeight] = useState<number>(defaultFontWeight);

  const [selectedLetterSpacing, setSelectedLetterSpacing] = useState<LetterSpacingConfig>(defaultLetterSpacing);
  const [selectedLineHeight, setSelectedLineHeight] = useState<LineHeightConfig>(defaultLineHeight);

  const [selectedTextAlign, setSelectedTextAlign] = useState<TextAlignConfig>(defaultTextAlign);
  const [selectedTextTransform, setSelectedTextTransform] = useState<TextTransformConfig>(defaultTextTransform);

  const [currentFormat, setCurrentFormat] = useState<Record<string, any>>({});
  const [currentSelection, setCurrentSelection] = useState<RangeStatic>();

  // Initial toolbar HTML behaviors effects
  useEffect(() => {
    const listQuillPickerLabels = document?.querySelectorAll(`#${id} .ql-picker .ql-picker-label`);

    const handleClickPickerLabel = event => {
      for (let i = 0; i < listQuillPickerLabels.length; i++) {
        let ele = listQuillPickerLabels[i];

        if (event.target !== ele) {
          ele?.parentElement?.classList.remove('ql-expanded');
          ele.setAttribute('aria-expanded', 'false');
          ele?.nextElementSibling?.setAttribute('aria-hidden', 'true');
        }
      }
    };

    for (let i = 0; i < listQuillPickerLabels.length; i++) {
      let ele = listQuillPickerLabels[i];

      ele.addEventListener('mousedown', handleClickPickerLabel);
    }

    // Init toolbar options tooltips
    document // ToolbarModuleName.BACKGROUND_COLOR
      ?.querySelector(`#${id} span.ql-background-color`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.backgroundColor.tooltip));
    document // ToolbarModuleName.BOLD
      ?.querySelector(`#${id} button.ql-bold`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.bold.tooltip));
    document // ToolbarModuleName.CLEAR_FORMATTING
      ?.querySelector(`#${id} button.ql-clean`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.clean.tooltip));
    document // ToolbarModuleName.INDENT_DECREASE
      ?.querySelector(`#${id} button.ql-indent[value="-1"]`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.indentDecrease.tooltip));
    document // ToolbarModuleName.INDENT_INCREASE
      ?.querySelector(`#${id} button.ql-indent[value="+1"]`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.indentIncrease.tooltip));
    document // ToolbarModuleName.ITALIC
      ?.querySelector(`#${id} button.ql-italic`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.italic.tooltip));
    document // ToolbarModuleName.LETTER_SPACING
      ?.querySelector(`#${id} span.ql-letter-spacing`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.letterSpacing.tooltip));
    document // ToolbarModuleName.LIST_BULLET
      ?.querySelector(`#${id} button.ql-list[value="bullet"]`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.listBullet.tooltip));
    document // ToolbarModuleName.LIST_ORDERED
      ?.querySelector(`#${id} button.ql-list[value="ordered"]`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.listOrdered.tooltip));
    document // ToolbarModuleName.HYPERLINK
      ?.querySelector(`#${id} button.ql-hyperlink`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.link.tooltip));
    document // ToolbarModuleName.SELECT_ALL
      ?.querySelector(`#${id} button.ql-select-all`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.selectAll.tooltip));
    document // ToolbarModuleName.STRIKE_THROUGH
      ?.querySelector(`#${id} button.ql-strike`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.strike.tooltip));
    document // ToolbarModuleName.SUB_SCRIPT
      ?.querySelector(`#${id} button.ql-script[value="sub"]`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.subscript.tooltip));
    document // ToolbarModuleName.SUPER_SCRIPT
      ?.querySelector(`#${id} button.ql-script[value="super"]`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.superscript.tooltip));
    document // ToolbarModuleName.TEXT_COLOR
      ?.querySelector(`#${id} span.ql-text-color`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.textColor.tooltip));
    document // ToolbarModuleName.UNDERLINE
      ?.querySelector(`#${id} button.ql-underline`)
      ?.setAttribute('title', t(translations.textEditor.toolbar.underline.tooltip));
    return () => {
      for (let i = 0; i < listQuillPickerLabels.length; i++) {
        let ele = listQuillPickerLabels[i];

        ele.removeEventListener('mousedown', handleClickPickerLabel);
      }
    };
  }, [id]);

  // Init observe toolbar effects
  useEffect(() => {
    const observer = new MutationObserver(function (mutations) {
      if (quillRef?.current) {
        const format = quillRef?.current.getEditor().getFormat();
        const selection = quillRef?.current.getEditor().getSelection();

        if (!isEqual(format, currentFormat)) {
          setCurrentFormat(format);
        }

        if (!isEqual(selection, currentSelection)) {
          setCurrentSelection(selection);
        }
      }
    });

    const target = document.querySelector(`#${id}`)?.parentNode;

    if (target) {
      observer.observe(target, {
        attributes: true,
        attributeFilter: ['style'],
      });
    }
  }, [id, quillRef]);

  // Update dropdown default select effects
  useEffect(() => {
    if (!currentFormat?.font) {
      if (selectedFontFamily.name !== defaultFontFamily.name) {
        setSelectedFontFamily(defaultFontFamily);
      }
    } else if (currentFormat.font !== selectedFontFamily.name) {
      const _f = font.find(f => f.fontFamily.name === currentFormat.font)?.fontFamily || defaultFontFamily;

      setSelectedFontFamily(_f);
    }
  }, [currentFormat, font]); // Update selected Font Family
  useEffect(() => {
    if (!currentFormat.size) {
      if (selectedFontSize.value !== defaultFontSize.value) {
        setSelectedFontSize(defaultFontSize);
      }
    } else if (currentFormat.size !== selectedFontSize.value) {
      const _s = fontSize.find(s => s.value === currentFormat.size) || defaultFontSize;

      setSelectedFontSize(_s);
    }
  }, [currentFormat, fontSize]); // Update selected Font Size
  useEffect(() => {
    if (!currentFormat?.hasOwnProperty('font-weight')) {
      if (selectedFontWeight !== defaultFontWeight) {
        setSelectedFontWeight(defaultFontWeight);
      }
    } else if (+currentFormat['font-weight'] !== selectedFontWeight) {
      const _f = font.find(f => f.fontFamily.name === selectedFontFamily.name);
      const _w = _f?.fontWeight.includes(+currentFormat['font-weight'])
        ? +currentFormat['font-weight']
        : defaultFontWeight;

      setSelectedFontWeight(_w);
    }
  }, [currentFormat, font]); // Update selected Font Weight

  useEffect(() => {
    if (!currentFormat?.hasOwnProperty('letter-spacing')) {
      if (selectedLetterSpacing.value !== defaultLetterSpacing.value) {
        setSelectedLetterSpacing(defaultLetterSpacing);
      }
    } else if (currentFormat['letter-spacing'] !== selectedLetterSpacing.value) {
      const _ls = letterSpacing.find(ls => ls.value === currentFormat['letter-spacing']) || defaultLetterSpacing;

      setSelectedLetterSpacing(_ls);
    }
  }, [currentFormat, letterSpacing]); // Update selected Letter Spacing
  useEffect(() => {
    if (!currentFormat?.hasOwnProperty('line-height')) {
      if (selectedLineHeight.value !== defaultLineHeight.value) {
        setSelectedLineHeight(defaultLineHeight);
      }
    } else if (currentFormat['line-height'] !== selectedLineHeight.value) {
      const _l = lineHeight.find(l => l.value === currentFormat['line-height']) || defaultLineHeight;

      setSelectedLineHeight(_l);
    }
  }, [currentFormat, lineHeight]); // Update selected Line Height

  useEffect(() => {
    if (!currentFormat?.hasOwnProperty('text-align')) {
      if (selectedTextAlign !== defaultTextAlign) {
        setSelectedTextAlign(defaultTextAlign);
      }
    } else if (currentFormat['text-align'] !== selectedTextAlign) {
      setSelectedTextAlign(currentFormat['text-align']);
    }
  }, [currentFormat]); // Update selected Text Align
  useEffect(() => {
    if (!currentFormat?.hasOwnProperty('text-transform')) {
      if (selectedTextTransform !== defaultTextTransform) {
        setSelectedTextTransform(defaultTextTransform);
      }
    } else if (currentFormat['text-transform'] !== selectedTextTransform) {
      setSelectedTextTransform(currentFormat['text-transform']);
    }
  }, [currentFormat]); // Update selected Text Transform

  // Handlers
  const handleOnClick = event => {
    let ele = event.target;

    if (ele?.classList?.contains('__toolbar-section') || ele?.parentNode?.classList?.contains('__toolbar-section')) {
      const listQuillPickerLabels = document?.querySelectorAll(`#${id} .ql-picker .ql-picker-label`);

      for (let i = 0; i < listQuillPickerLabels.length; i++) {
        let ele = listQuillPickerLabels[i];

        ele?.parentElement?.classList.remove('ql-expanded');
        ele.setAttribute('aria-expanded', 'false');
        ele?.nextElementSibling?.setAttribute('aria-hidden', 'true');
      }
    }
  };

  const handleOnBlur = event => {
    if (quillRef?.current) {
      if (event?.relatedTarget) {
        quillRef?.current?.getEditor()?.theme?.tooltip.root.setAttribute('aria-keep', 'true');
      } else {
        quillRef?.current?.getEditor()?.theme?.tooltip.root.setAttribute('aria-keep', 'false');
      }
    }
  };

  // Toolbar Handlers
  const handleInsertHyperLink = link => {
    if (quillRef?.current) {
      quillRef.current.getEditor().format('link', link.toString().trim().length ? link : undefined, 'user');
    }
  };

  const handleInsertMergeTags = mergeTags => {
    if (quillRef?.current) {
      const quill = quillRef.current.getEditor();

      if (currentSelection) {
        const format = quill.getFormat(currentSelection);

        quill.deleteText(currentSelection.index, currentSelection.length, 'user');
        quill.insertText(currentSelection.index, mergeTags, format, 'user');
        quill.setSelection(currentSelection.index, mergeTags.length, 'user');
      }
    }
  };

  const handleSelectBackgroundColor = color => {
    if (quillRef?.current) {
      quillRef.current.getEditor().format('background', color, 'user');
    }
  };

  const handleSelectFontFamily = fontFamily => {
    const fontConfig = font.find(f => f.fontFamily.name === fontFamily.name);

    if (selectedFontFamily.name !== fontFamily.name) {
      setSelectedFontFamily(fontFamily);
    }

    if (quillRef?.current) {
      const quill = quillRef.current.getEditor();
      const format = quill.getFormat();

      quill.format('font', fontFamily.name, 'user');

      if (
        format?.hasOwnProperty('font-weight') &&
        fontConfig &&
        !fontConfig.fontWeight.includes(+format['font-weight'])
      ) {
        setSelectedFontWeight(fontConfig.fontWeight[0]);
        quill.format('font-weight', `${fontConfig.fontWeight[0]}`, 'user');
      }
    }
  };
  const handleSelectFontSize = fontSize => {
    if (selectedFontSize.value !== fontSize.value) {
      setSelectedFontSize(fontSize);
    }

    if (quillRef?.current) {
      quillRef.current.getEditor().format('size', fontSize.value, 'user');
    }
  };
  const handleSelectFontWeight = fontWeight => {
    if (fontWeight !== selectedFontWeight) {
      setSelectedFontWeight(fontWeight);
    }

    if (quillRef?.current) {
      quillRef.current.getEditor().format('font-weight', `${fontWeight}`, 'user');
    }
  };

  const handleSelectLetterSpacing = letterSpacing => {
    if (selectedLetterSpacing.value !== letterSpacing.value) {
      setSelectedLetterSpacing(letterSpacing);
    }

    if (quillRef?.current) {
      quillRef.current.getEditor().format('letter-spacing', letterSpacing.value, 'user');
    }
  };
  const handleSelectLineHeight = lineHeight => {
    if (selectedLineHeight.value !== lineHeight.value) {
      setSelectedLineHeight(lineHeight);
    }

    if (quillRef?.current) {
      quillRef.current.getEditor().format('line-height', lineHeight.value, 'user');
    }
  };

  const handleSelectTextAlign = textAlign => {
    if (textAlign !== selectedTextAlign) {
      setSelectedTextAlign(textAlign);
    }

    if (quillRef?.current) {
      quillRef.current.getEditor().format('text-align', textAlign, 'user');
    }
  };
  const handleSelectTextColor = color => {
    if (quillRef?.current) {
      quillRef.current.getEditor().format('color', color, 'user');
    }
  };
  const handleSelectTextTransform = textTransform => {
    if (textTransform !== selectedTextTransform) {
      setSelectedTextTransform(textTransform);
    }

    if (quillRef?.current) {
      quillRef.current.getEditor().format('text-transform', textTransform, 'user');
    }
  };

  // Render Functions
  const renderToolbar = () => {
    if (toolbar && Object.keys(toolbar).length) {
      return Object.keys(toolbar)
        .sort()
        .map(toolbarIndex => {
          const toolbarSection = toolbar[toolbarIndex];

          if (toolbarSection && Object.keys(toolbarSection).length) {
            return (
              <div key={toolbarIndex} className="__toolbar-section">
                {Object.keys(toolbarSection)
                  .sort()
                  .map(sectionIndex => {
                    const toolbarGroup = toolbarSection[sectionIndex];

                    if (toolbarGroup?.length) {
                      return (
                        <span key={sectionIndex} className="ql-formats">
                          {toolbarGroup.map((module, index) => {
                            switch (module) {
                              case ToolbarModuleName.BACKGROUND_COLOR:
                                return (
                                  <ColorPicker
                                    key={index}
                                    className="ql-color-picker ql-background-color"
                                    defaultColor="#ffffff"
                                    icon="icon-ants-material-drive-file-rename-outline"
                                    showInput={false}
                                    onChange={handleSelectBackgroundColor}
                                  />
                                );
                              case ToolbarModuleName.BOLD:
                                return <Emphasis key={index} type={EmphasisType.BOLD} />;
                              case ToolbarModuleName.CLEAR_FORMATTING:
                                return <ClearFormatting key={index} />;
                              case ToolbarModuleName.FONT_FAMILY:
                                return (
                                  <FontFamily
                                    key={index}
                                    preselectedFont={selectedFontFamily}
                                    whitelist={font.map(config => config.fontFamily)}
                                    onChange={handleSelectFontFamily}
                                  />
                                );
                              case ToolbarModuleName.FONT_SIZE:
                                return (
                                  <FontSize
                                    key={index}
                                    preselectedFontSize={selectedFontSize}
                                    whitelist={fontSize}
                                    onChange={handleSelectFontSize}
                                  />
                                );
                              case ToolbarModuleName.FONT_WEIGHT:
                                return (
                                  <FontWeight
                                    key={index}
                                    preselectedFontWeight={selectedFontWeight}
                                    whitelist={
                                      (font.find(f => f.fontFamily.name === selectedFontFamily.name) || font[0])
                                        .fontWeight
                                    }
                                    onChange={handleSelectFontWeight}
                                  />
                                );
                              case ToolbarModuleName.HYPERLINK:
                                return <HyperLink key={index} onChange={handleInsertHyperLink} />;
                              case ToolbarModuleName.INDENT_DECREASE:
                                return <Indent key={index} type={IndentType.DECREASE} />;
                              case ToolbarModuleName.INDENT_INCREASE:
                                return <Indent key={index} type={IndentType.INCREASE} />;
                              case ToolbarModuleName.ITALIC:
                                return <Emphasis key={index} type={EmphasisType.ITALIC} />;
                              case ToolbarModuleName.LETTER_SPACING:
                                return (
                                  <LetterSpacing
                                    key={index}
                                    preselectedLetterSpacing={selectedLetterSpacing}
                                    whitelist={letterSpacing}
                                    onChange={handleSelectLetterSpacing}
                                  />
                                );
                              case ToolbarModuleName.LINE_HEIGHT:
                                return (
                                  <LineHeight
                                    key={index}
                                    preselectedLineHeight={selectedLineHeight}
                                    whitelist={lineHeight}
                                    onChange={handleSelectLineHeight}
                                  />
                                );
                              case ToolbarModuleName.LIST_BULLET:
                                return <List key={index} type={ListType.BULLET} />;
                              case ToolbarModuleName.LIST_ORDERED:
                                return <List key={index} type={ListType.ORDERED} />;
                              case ToolbarModuleName.MERGE_TAG:
                                return <MergeTag key={index} whitelist={mergeTags} onChange={handleInsertMergeTags} />;
                              case ToolbarModuleName.REDO:
                                return (
                                  <Redo key={index} disabled={!redoRef?.current?.length} onClick={handlers[module]} />
                                );
                              case ToolbarModuleName.SELECT_ALL:
                                return <SelectAll key={index} />;
                              case ToolbarModuleName.STRIKE_THROUGH:
                                return <Emphasis key={index} type={EmphasisType.STRIKE_THROUGH} />;
                              case ToolbarModuleName.SUB_SCRIPT:
                                return <Script key={index} type={ScriptType.SUB} />;
                              case ToolbarModuleName.SUPER_SCRIPT:
                                return <Script key={index} type={ScriptType.SUPER} />;
                              case ToolbarModuleName.TEXT_ALIGN:
                                return (
                                  <TextAlign
                                    key={index}
                                    preselectedTextAlign={selectedTextAlign}
                                    onChange={handleSelectTextAlign}
                                  />
                                );
                              case ToolbarModuleName.TEXT_COLOR:
                                return (
                                  <ColorPicker
                                    key={index}
                                    className="ql-color-picker ql-text-color"
                                    icon="icon-ants-text-color"
                                    showInput={false}
                                    onChange={handleSelectTextColor}
                                  />
                                );
                              case ToolbarModuleName.TEXT_TRANSFORM:
                                return (
                                  <TextTransform
                                    key={index}
                                    preselectTextTransform={selectedTextTransform}
                                    onChange={handleSelectTextTransform}
                                  />
                                );
                              case ToolbarModuleName.UNDERLINE:
                                return <Emphasis key={index} type={EmphasisType.UNDERLINE} />;
                              case ToolbarModuleName.UNDO:
                                return (
                                  <Undo key={index} disabled={!undoRef?.current?.length} onClick={handlers[module]} />
                                );
                            }

                            return null;
                          })}
                        </span>
                      );
                    }

                    return null;
                  })}
              </div>
            );
          }

          return null;
        })
        .filter(Boolean);
    }

    return null;
  };

  return (
    <ToolbarWrapper id={id} style={style} onBlur={handleOnBlur} onClick={handleOnClick}>
      {renderToolbar()}
    </ToolbarWrapper>
  );
});

export const TextEditor: React.FC<TextEditorProps> = props => {
  // Hooks
  const { t } = useTranslation();

  // Props Value
  const {
    boundSelector = '',
    config = {},
    defaultFormat = {},
    defaultValue = '',
    disabled = false,
    placeholder = t(translations.textEditor.placeholder),
    toolbar = DEFAULT_TOOLBAR,
    toolbarMode = 'float',
    onChange,
    onChangeSelection,
  } = props;

  const { font = DEFAULT_FONT_CONFIGS } = config;
  const { fontSize = DEFAULT_FONT_SIZE_CONFIGS } = config;
  const { letterSpacing = DEFAULT_LETTER_SPACING_CONFIGS } = config;
  const { lineHeight = DEFAULT_LINE_HEIGHT_CONFIGS } = config;

  const { fontFamily: defaultFontFamily = DEFAULT_FONT_FAMILY } = defaultFormat;
  const { fontSize: defaultFontSize = DEFAULT_FONT_SIZE } = defaultFormat;
  const { fontWeight: defaultFontWeight = DEFAULT_FONT_WEIGHT } = defaultFormat;
  const { letterSpacing: defaultLetterSpacing = DEFAULT_LETTER_SPACING } = defaultFormat;
  const { lineHeight: defaultLineHeight = DEFAULT_LINE_HEIGHT } = defaultFormat;
  const { textAlign: defaultTextAlign = DEFAULT_TEXT_ALIGN } = defaultFormat;
  const { textTransform: defaultTextTransform = DEFAULT_TEXT_TRANSFORM } = defaultFormat;

  // States
  const [value, setValue] = useState(defaultValue);
  const debounceValue = useDebounce(defaultValue, config?.history?.delay || DEFAULT_HISTORY_DELAY);

  // Ref
  const redoRef = useRef<any[]>([]);
  const undoRef = useRef<any[]>([]);

  const quillRef = useRef<any>();
  const historyRef = useRef<any>({ redoRef, undoRef });

  const ref = useRef<any>({ historyRef, quillRef });

  const textEditorId = useRef<string>('editor-' + random(8));
  const toolbarId = useRef<string>('toolbar-' + random(8));

  // Init Quill effects
  useEffect(() => {
    // Quill Formats declare
    let Font = Quill.import('attributors/style/font');
    Font.whitelist = font.map(config => config.fontFamily.name);

    let Size = Quill.import('attributors/style/size');
    Size.whitelist = fontSize.map(size => size.value);

    // Quill Custom Parchments declare
    let FontWeight = new FontWeightAttributor(uniq(flattenDeep(font.map(config => config.fontWeight))));
    let LetterSpacing = new LetterSpacingAttributor(letterSpacing.map(space => space.value));
    let LineHeight = new LineHeightAttributor(lineHeight.map(line => line.value));
    let TextAlign = new TextAlignAttributor();
    let TextTransform = new TextTransformAttributor();

    // Register Quill
    Quill.register(Font, true);
    Quill.register({ 'formats/font-weight': FontWeight }, true);
    Quill.register({ 'formats/letter-spacing': LetterSpacing }, true);
    Quill.register({ 'formats/line-height': LineHeight }, true);
    Quill.register(Size, true);
    Quill.register(SoftLineBreakBlot, true);
    Quill.register({ 'formats/text-align': TextAlign }, true);
    Quill.register({ 'formats/text-transform': TextTransform }, true);
  }, [font, fontSize, letterSpacing, lineHeight]);
  useEffect(() => {
    if (quillRef?.current) {
      const quill = quillRef.current.getEditor();

      quill.clipboard.addMatcher('BR', function (node, delta) {
        let Delta = Quill.import('delta');
        let newDelta = new Delta();

        if (node?.parentElement?.childElementCount > 1) {
          newDelta.insert({ softbreak: true });
        }

        return newDelta;
      });

      quill.root.setAttribute('spellcheck', !disabled + '');
    }
  }, [quillRef, disabled]);

  // Update default value when the props changes
  useEffect(() => {
    if (quillRef?.current) {
      const quill = quillRef.current.getEditor();
      const delta = quill.clipboard.convert(defaultValue);

      // Update default format
      const currentDelta = quill.getContents();
      const currentSelection = quill.getSelection(true);

      // Update default value
      if (!isEqual(currentDelta, delta)) {
        setTimeout(() => {
          quill.setContents(delta);
          quill.setSelection(currentSelection || { index: defaultValue.length + 1, length: 1 });

          // Update default format
          const format = quill.getFormat();

          if (!format?.font) {
            quill.format('font', defaultFontFamily.name, 'user');
          }
          if (!format?.size) {
            quill.format('size', defaultFontSize.value, 'user');
          }
          if (!format?.hasOwnProperty('font-weight')) {
            quill.format('font-weight', defaultFontWeight, 'user');
          }

          if (!format?.hasOwnProperty('letter-spacing')) {
            quill.format('letter-spacing', defaultLetterSpacing.value, 'user');
          }
          if (!format?.hasOwnProperty('line-height')) {
            quill.format('line-height', defaultLineHeight.value, 'user');
          }

          if (!format?.hasOwnProperty('text-align')) {
            quill.format('text-align', defaultTextAlign, 'user');
          }
          if (!format?.hasOwnProperty('text-transform')) {
            quill.format('text-transform', defaultTextTransform, 'user');
          }
        }, 0);
      }
    }
  }, [defaultValue]);

  // Update history stacks
  useEffect(() => {
    if (undoRef?.current) {
      undoRef.current.push(debounceValue);
    }
  }, [debounceValue]);
  useEffect(() => {
    if (undoRef?.current && undoRef.current.length > (config?.history?.maxStack || DEFAULT_HISTORY_MAX_TACK)) {
      undoRef.current.splice(0, undoRef.current.length - DEFAULT_HISTORY_MAX_TACK);
    }
  }, [config?.history?.maxStack]);

  // Quill Icons declare
  const Icons = Quill.import('ui/icons');
  Icons['bold'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0V0z" fill="none"/>
  <path class="ql-fill" d="M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z"/>
</svg>
`;
  Icons['clean'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0z" fill="none"/>
  <path class="ql-fill" d="M3.27 5L2 6.27l6.97 6.97L6.5 19h3l1.57-3.66L16.73 21 18 19.73 3.55 5.27 3.27 5zM6 5v.18L8.82 8h2.4l-.72 1.68 2.1 2.1L14.21 8H20V5H6z"/>
</svg>
  `;
  Icons['indent']['+1'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0z" fill="none"/>
  <path class="ql-fill" d="M3 21h18v-2H3v2zM3 8v8l4-4-4-4zm8 9h10v-2H11v2zM3 3v2h18V3H3zm8 6h10V7H11v2zm0 4h10v-2H11v2z"/>
</svg>
  `;
  Icons['indent']['-1'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0z" fill="none"/>
  <path class="ql-fill" d="M11 17h10v-2H11v2zm-8-5l4 4V8l-4 4zm0 9h18v-2H3v2zM3 3v2h18V3H3zm8 6h10V7H11v2zm0 4h10v-2H11v2z"/>
</svg>  `;
  Icons['italic'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0V0z" fill="none"/>
  <path class="ql-fill" d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4h-8z"/>
</svg>
  `;
  Icons['script']['sub'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="1 0 24 24" width="24px">
  <g>
    <rect fill="none" height="24" width="24"/>
    <path class="ql-fill" d="M22,18h-2v1h3v1h-4v-2c0-0.55,0.45-1,1-1h2v-1h-3v-1h3c0.55,0,1,0.45,1,1v1C23,17.55,22.55,18,22,18z M5.88,18h2.66 l3.4-5.42h0.12l3.4,5.42h2.66l-4.65-7.27L17.81,4h-2.68l-3.07,4.99h-0.12L8.85,4H6.19l4.32,6.73L5.88,18z"/>
  </g>
</svg>
  `;
  Icons['script']['super'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="1 1 24 24" width="24px">
  <g>
    <rect fill="none" height="24" width="24" x="0" y="0"/>
    <path class="ql-fill" d="M22,7h-2v1h3v1h-4V7c0-0.55,0.45-1,1-1h2V5h-3V4h3c0.55,0,1,0.45,1,1v1C23,6.55,22.55,7,22,7z M5.88,20h2.66l3.4-5.42h0.12 l3.4,5.42h2.66l-4.65-7.27L17.81,6h-2.68l-3.07,4.99h-0.12L8.85,6H6.19l4.32,6.73L5.88,20z"/>
  </g>
</svg>
  `;
  Icons['strike'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0V0z" fill="none"/>
  <path class="ql-fill" d="M10 19h4v-3h-4v3zM5 4v3h5v3h4V7h5V4H5zM3 14h18v-2H3v2z"/>
</svg>
  `;
  Icons['underline'] = `
<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
  <path d="M0 0h24v24H0V0z" fill="none"/>
  <path class="ql-fill" d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>
</svg>
  `;

  // Text Editor Handlers
  const handleOnBlur = event => {
    if (quillRef?.current) {
      if (!event?.relatedTarget) {
        quillRef?.current?.getEditor()?.theme?.tooltip.root.setAttribute('aria-keep', 'false');
        quillRef?.current?.getEditor()?.theme?.tooltip?.hide();
      } else {
        let shouldHide = false;

        // Check if the related target (the element receiving focus event) has relation with the target (the element firing blur event)
        if (event.target.classList.contains('ql-editor')) {
          const bound = document.querySelector(`#${textEditorId.current}`);

          shouldHide = bound !== event.relatedTarget && !isDescendant(bound, event.relatedTarget);
        }

        if (event.relatedTarget.classList.contains('ql-editor')) {
          const bound = document.querySelector(`#${textEditorId.current}`);

          shouldHide = bound !== event.target && !isDescendant(bound, event.target);
        }

        if (shouldHide) {
          quillRef?.current?.getEditor()?.theme?.tooltip.root.setAttribute('aria-keep', 'false');
          quillRef?.current?.getEditor()?.theme?.tooltip?.hide();
        }
      }
    }
  };

  const handleOnChange = (content: string, _delta: Delta, source: Sources) => {
    if (quillRef?.current) {
      const quill = quillRef.current.getEditor();

      const deltaDefault = quill.clipboard.convert(defaultValue);
      const deltaContent = quill.clipboard.convert(content);

      if (!isEqual(deltaDefault, deltaContent) && source === 'user') {
        setValue(content);

        if (onChange) {
          onChange(content);
        }
      }
    }
  };
  const handleOnChangeSelection = (range: RangeStatic, source: Sources, editor) => {
    if (onChangeSelection) {
      onChangeSelection(range, source, editor);
    }
  };

  // Toolbar Handlers
  const handleSoftBreak = useCallback(function (this: any, range: RangeStatic) {
    this.quill.insertEmbed(range.index, 'softbreak', true, 'user');
    this.quill.setSelection(range.index + 1, 'silent');
  }, []);

  const handleRedo = () => {
    if (quillRef?.current && redoRef?.current && redoRef.current.length) {
      const quill = quillRef.current.getEditor();

      const value = redoRef.current.pop();
      const delta = quill.clipboard.convert(value);

      if (onChange) {
        onChange(value);
      }

      setValue(value);
      quill.setContents(delta);
    }
  };
  const handleUndo = () => {
    if (quillRef?.current && undoRef?.current && undoRef.current.length) {
      const quill = quillRef.current.getEditor();

      const redoValue = undoRef.current.pop();

      const undoValue = undoRef.current.pop();
      const delta = quill.clipboard.convert(undoValue);

      redoRef?.current.push(redoValue);
      if (redoRef?.current && redoRef?.current?.length > DEFAULT_HISTORY_MAX_TACK) {
        redoRef?.current?.splice(0, redoRef?.current.length - DEFAULT_HISTORY_MAX_TACK);
      }

      if (onChange) {
        onChange(undoValue);
      }

      setValue(undoValue);
      quill.setContents(delta);
    }
  };

  return (
    <TextEditorWrapper id={textEditorId.current} tabIndex={1} onBlur={handleOnBlur}>
      <ToolbarComponent
        config={config}
        handlers={{
          [ToolbarModuleName.REDO]: handleRedo,
          [ToolbarModuleName.UNDO]: handleUndo,
        }}
        id={toolbarId.current}
        ref={ref}
        style={{ height: 117.5, width: 819 }}
        toolbar={toolbar}
      />
      <ReactQuill
        bounds={boundSelector || `#${textEditorId.current}`}
        formats={[
          'background',
          'bold',
          'color',
          'font',
          'font-weight',
          'indent',
          'italic',
          'letter-spacing',
          'line-height',
          'link',
          'list',
          'size',
          'strike',
          'script',
          'text-align',
          'text-transform',
          'underline',
        ]}
        modules={{
          keyboard: {
            bindings: {
              'shift enter': {
                key: 13,
                shiftKey: true,
                handler: handleSoftBreak,
              },
            },
          },
          toolbar: {
            container: `#${toolbarId.current}`,
            handlers: {
              'select-all': handleSelectAll,
            },
          },
        }}
        readOnly={disabled}
        ref={quillRef}
        placeholder={placeholder}
        theme={toolbarMode === 'float' ? 'bubble' : 'snow'}
        value={value}
        onChange={handleOnChange}
        onChangeSelection={handleOnChangeSelection}
      />
    </TextEditorWrapper>
  );
};
TextEditor.defaultProps = {
  toolbar: DEFAULT_TOOLBAR,
};
