/* eslint-disable react-hooks/exhaustive-deps */
// Libraries
import React, { useEffect, useState, useRef, useMemo } from 'react';
import Upload, { UploadProps as AntdUploadProps } from 'antd/lib/upload';
import { cloneDeep, flattenDeep } from 'lodash';
import { useQueryClient } from '@tanstack/react-query';
import { useInView } from 'react-intersection-observer';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Assets
import PlaceholderImage from 'assets/images/placeholder-image.png';

// Hooks
import { useDebounce } from 'app/hooks';

// Service
import { uploadFile } from 'app/services/MediaTemplateDesign/UploadFile';

//Atoms
import { Button, Icon, Text, Message, Spin, Input, Space } from 'app/components/atoms';

//Molecules
import { Modal, InputSearch, Select } from 'app/components/molecules';

// Model
import { SavedImage } from 'app/models/SavedImage';

// Styled
import { UploadImageWrapper } from './styled';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Queries
import { useAddSavedMedia, useDeleteSavedMedia, useStoreSavedMedia } from 'app/queries/SavedImage';
import { QUERY_KEYS } from 'constants/queries';

interface UploadImageProps extends AntdUploadProps {
  onRemoveImage?: Function;
  onChangeImage?: Function;
  selectedImage?: UploadImageObject;
  isOpen?: boolean;
  extensions?: string[];
  maxSize?: number;
  title?: string;
  showImageURL?: boolean;
  required?: boolean;
  focused?: boolean;
  titleImageUrl?: string;
}

const SORT_OPTIONS = {
  BY_UPLOAD_DATE: {
    value: 1,
    label: getTranslateMessage(translations.sortByUploadDate.title),
  },
  BY_SIZE: {
    value: 2,
    label: getTranslateMessage(translations.sortBySize.title),
  },
};

export const MEDIA_TYPE = {
  IMAGE: 1,
  VIDEO: 2,
} as const;

export type MediaType = typeof MEDIA_TYPE[keyof typeof MEDIA_TYPE];

interface UploadImageObject {
  url: string;
}

export const UploadImage: React.FC<UploadImageProps> = props => {
  // i18n
  const { t } = useTranslation();

  const {
    onRemoveImage,
    onChangeImage,
    isOpen,
    extensions,
    maxSize,
    title,
    showImageURL,
    required,
    focused,
    titleImageUrl,
  } = props;

  const { Dragger } = Upload;

  // Query
  const {
    data: storeSavedMedia,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingStored,
    refetch,
  } = useStoreSavedMedia();

  const queryClient = useQueryClient();

  const { mutateAsync: deleteSavedImage } = useDeleteSavedMedia();
  const { mutateAsync: addSavedImage } = useAddSavedMedia();

  const [selectedImage, setSelectedImage] = useState<UploadImageObject>(props.selectedImage || { url: '' });
  const [isModalVisible, setIsModalVisible] = useState(isOpen);
  const [textSearch, setTextSearch] = useState('');
  const textSearchDebounce = useDebounce(textSearch, 350);

  const [sortOption, setSortOption] = useState(SORT_OPTIONS.BY_UPLOAD_DATE.value);
  const [loading, setLoading] = useState(false);
  const [isOpenConfirmDelete, setOpenConfirmDelete] = useState(false);

  const uploadFilesRef = useRef<File[]>([]);
  const uploadFilesTimeoutRef = useRef<NodeJS.Timeout>();
  const deleteImageRef = useRef<SavedImage | undefined>();

  const { ref } = useInView({
    triggerOnce: false,
    onChange: inView => {
      if (inView && hasNextPage && !isFetchingNextPage && !textSearchDebounce) {
        fetchNextPage();
      }
    },
  });

  const flattenedMedia = useMemo(() => {
    const flattened = cloneDeep(flattenDeep(storeSavedMedia?.pages));

    // Filter media based on search text
    const filteredMedia = textSearchDebounce
      ? flattened.filter(media => media.name?.toLowerCase().includes(textSearchDebounce.toLowerCase()))
      : flattened;

    // Sort media based on the selected sort option
    if (sortOption === SORT_OPTIONS.BY_SIZE.value) {
      return filteredMedia.sort((media1, media2) => media1.size - media2.size);
    }

    return filteredMedia.sort((media1, media2) =>
      media1.createdAt && media2.createdAt ? (media1.createdAt.isAfter(media2.createdAt) ? -1 : 1) : 0,
    );
  }, [storeSavedMedia?.pages, sortOption, textSearchDebounce]);

  // When selectedImage onChange
  useEffect(() => {
    setSelectedImage(props.selectedImage || { url: '' });
  }, [props.selectedImage]);

  const showModal = (e: any) => {
    e.stopPropagation();
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setTextSearch('');
  };

  const handleSelectImage = image => {
    if (typeof onChangeImage === 'function') {
      onChangeImage(image);
    }
    setSelectedImage(image);
    handleCancel();
  };

  const onChangeSort = option => {
    setSortOption(option);
  };

  const renderListImages = (listImagesRender: SavedImage[]) =>
    listImagesRender.map((image, idx) => (
      <div
        className="ants-group ants-flex ants-flex-col ants-items-center ants-w-250px ants-h-auto ants-text-center"
        key={idx}
      >
        <div className="ants-flex ants-relative ants-justify-center ants-items-center ants-w-250px ants-h-250px ants-border hover:ants-border-2 ants-border-alto ants-border-solid">
          <img src={image.url} alt="img" className="ants-object-contain ants-w-full ants-h-full" />

          <div className="ants-flex ants-absolute ants-z-3 ants-gap-15px ants-justify-center ants-items-center ants-opacity-0 group-hover:ants-opacity-100 ants-transition ants-duration-200 ants-ease-in-out">
            <Button onClick={() => handleSelectImage(image)} className="ants-bg-white">
              {t(translations.USE.title)}
            </Button>
            <Button onClick={() => handleRemoveUploadedImage(image)} className="ants-bg-white">
              <Icon type="icon-ants-remove-trash" size={15} className="ants-text-primary" />
            </Button>
          </div>

          <div className="ants-absolute ants-inset-0 ants-z-2 ants-bg-background-transparent-03 ants-opacity-0 group-hover:ants-opacity-100 ants-transition ants-duration-200 ants-ease-in-out"></div>
        </div>
        <p
          className="ants-inline-block ants-mt-5px ants-w-full ants-text-normal"
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
          }}
          title={image.name}
        >
          {image.name}
        </p>
        <p className="ants-inline-block ants-mt-5px ants-w-full ants-text-normal">
          {t(translations.uploadedAt.title, {
            date: image.createdAt ? image.createdAt.format('DD/MM/YYYY') : null,
            time: image.createdAt ? image.createdAt.format('hh:mm:ss A') : null,
          })}
        </p>
        <p className="ants-inline-block ants-mt-5px ants-w-full ants-text-normal">
          {t(translations.size.title)}: {image.sizeString}
        </p>
      </div>
    ));

  const onChangeSearchImage = e => {
    const { value } = e.target;

    setTextSearch(value.trim());
  };

  const handleRemoveImage = () => {
    if (onRemoveImage) {
      onRemoveImage();
    }
    setSelectedImage({ url: '' });
  };

  const handleRemoveUploadedImage = async (image: SavedImage | undefined, isConfirm = true) => {
    if (isConfirm) {
      deleteImageRef.current = image;
      setOpenConfirmDelete(true);
    } else if (image) {
      await deleteSavedImage(+image.id);

      setOpenConfirmDelete(false);
      deleteImageRef.current = undefined;

      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVED_IMAGES_PAGINATION], {
        exact: false,
      });
    }
  };

  const renderSelectedImage = () => {
    return selectedImage && selectedImage.url ? (
      <img
        src={selectedImage.url}
        alt=""
        className="ants-object-contain ants-absolute ants-w-full ants-h-full"
        onError={(e: any) => {
          e.target.src = PlaceholderImage;
        }}
      />
    ) : null;
  };

  // const handleBeforeUploadFile = async (file, files) => {
  // if (!['image/png', 'image/jpeg'].includes(file.type)) {
  //   Message.error(t(translations.invalidFileExtension.title));
  //   return false;
  // }
  // if (!(files?.length === 1)) {
  //   Message.error(t(translations.notAllowUploadMultipleFile.title));
  //   return false;
  // }
  // if (maxSize && maxSize > 0 && file.size > maxSize * 1024 * 1024) {
  //   Message.error(t(translations.fileSizeTooBig.title));
  //   return false;
  // }
  // setLoading(true);
  // };

  const customRequestUpload = async options => {
    try {
      const {
        // onSuccess,
        //  onError,
        file,
        //  onProgress
      } = options;

      uploadFilesRef.current.push(file);
      if (uploadFilesTimeoutRef.current) {
        clearTimeout(uploadFilesTimeoutRef.current);
      }

      uploadFilesTimeoutRef.current = setTimeout(async () => {
        if (!uploadFilesRef.current.length) {
          uploadFilesRef.current = [];
          return;
        }

        if (uploadFilesRef.current.length > 10) {
          Message.error(t(translations.maximumFileUpload.title));
          uploadFilesRef.current = [];
          return false;
        }

        for (let file of uploadFilesRef.current) {
          if (!extensions?.map(extension => extension.replace('.', 'image/')).includes(file.type)) {
            Message.error(t(translations.invalidFileExtension.title));
            uploadFilesRef.current = [];
            return false;
          }

          if (maxSize && maxSize > 0 && file.size > maxSize * 1024 * 1024) {
            Message.error(t(translations.fileSizeTooBig.title));
            uploadFilesRef.current = [];
            return false;
          }
        }

        setLoading(true);

        const result = await uploadFile(uploadFilesRef.current).finally(() => {
          uploadFilesRef.current = [];
        });

        if (result?.length) {
          const arrPromise: Promise<any>[] = [];

          result.forEach(data =>
            arrPromise.push(
              addSavedImage({
                name: data.fileName,
                type: MEDIA_TYPE.IMAGE,
                properties: {
                  url: data.url,
                  file: data.file,
                  extension: data.extension,
                  dimensions_file: data.dimensionsFile,
                  size: data.size,
                  file_name: data.fileName,
                },
              }),
            ),
          );

          await Promise.all(arrPromise);

          setLoading(false);

          refetch({ refetchPage: (_page, index) => index === 0 });
        } else {
          setLoading(false);
          Message.error(t(translations.cannotUploadImage.title));
        }
      }, 300);
    } catch (e) {
      uploadFilesRef.current = [];
      setLoading(false);
      Message.error(t(translations.cannotUploadImage.title));
    }
  };

  // const onChangeFileUpload = async fileData => {
  //   console.log('ON CHANGE', fileData);

  //   if (fileData?.file?.response && fileData.file.response.code === 200 && fileData.file.response.data) {
  //     const { data } = fileData.file.response;
  //     // Save image
  //     const result = await createSavedImage({
  //       image_name: data.file_name,
  //       properties: {
  //         url: data.url,
  //         image: data.file,
  //         extension: data.extension,
  //         dimensions_file: data.dimensions_file,
  //         size: data.size,
  //         file_name: data.file_name,
  //       },
  //     });

  //     dispatch(createSagaAction(ACTIONS.FETCH_LIST_SAVED_IMAGES));
  //   } else if (fileData?.file?.status === 'error') {
  //     setLoading(false);
  //     Message.error(t(translations.cannotUploadImage.title));
  //   }
  // };

  return (
    <>
      <UploadImageWrapper>
        {title ? <Text className="ants-mb-2.5">{title}</Text> : null}

        <div className={`image-upload-content ${selectedImage && selectedImage.url ? 'uploaded' : ''}`}>
          {renderSelectedImage()}
          {!selectedImage || !selectedImage.url ? (
            <Icon type="icon-ants-image-3" size={36} className="ants-z-1 ants-ml-25px ants-text-cus-second" />
          ) : null}

          <div className="ants-flex ants-z-1 ants-gap-15px ants-justify-center ants-items-center">
            {/* <p className="ant-upload-text text-cus-dark !mb-10px">Drag &#38; Drop file here</p> */}
            <Button onClick={showModal} className="ants-bg-white">
              {t(translations.browseImage.title)}
            </Button>
            {selectedImage && selectedImage.url ? (
              <Button onClick={handleRemoveImage} className="ants-bg-white">
                <Icon type="icon-ants-remove-trash" size={15} className="ants-text-primary" />
              </Button>
            ) : null}
          </div>
        </div>

        <div className="ants-mt-3 upload-file-info">
          <Text>
            {t(translations.fileType.title)}: {extensions?.join(', ')}
          </Text>
          <Text>
            {t(translations.fileSize.title)}: {t(translations.under.title)} {maxSize}MB
          </Text>
        </div>

        {showImageURL && (
          <Space className="ants-mt-3" size={5} direction={'vertical'}>
            <Input
              label={titleImageUrl || t(translations.imageURL.title)}
              required={required}
              placeholder={t(translations.imageURL.placeholder)}
              value={selectedImage.url ? selectedImage.url : ''}
              focused={focused}
              onAfterChange={value =>
                handleSelectImage({
                  url: value,
                })
              }
            />
          </Space>
        )}
      </UploadImageWrapper>
      <Modal
        destroyOnClose
        title={t(translations.deleteImage.title)}
        visible={isOpenConfirmDelete}
        onOk={() => handleRemoveUploadedImage(deleteImageRef.current, false)}
        onCancel={() => setOpenConfirmDelete(prev => !prev)}
        okText={t(translations.ok.title)}
        cancelText={t(translations.cancel.title)}
      >
        <p>
          {t(translations.deleteImage.description, {
            name: deleteImageRef.current?.name,
          })}
          ?
        </p>
      </Modal>
      <Modal
        wrapClassName="icons-selection-modal"
        title={
          <div className="ants-flex ants-gap-2 ants-items-center">
            <Icon type="icon-ants-image-3" size={20} />
            {t(translations.imageSelection.title)}
          </div>
        }
        centered
        headerStyle={{
          padding: '20px 20px 0',
          border: 'none',
        }}
        bodyStyle={{
          padding: 20,
          maxHeight: '90vh',
          width: '100%',
          overflow: 'auto',
        }}
        width={1155}
        footer={null}
        visible={isModalVisible}
        destroyOnClose
        onCancel={handleCancel}
      >
        <div className="ants-flex ants-justify-between ants-items-center ants-mb-5">
          <InputSearch
            className="ants-w-[232px]"
            value={textSearch}
            onChange={onChangeSearchImage}
            placeholder={`${t(translations.searchImage.title)}...`}
          />
          <Select
            defaultValue={SORT_OPTIONS.BY_UPLOAD_DATE.value}
            value={sortOption}
            options={Object.values(SORT_OPTIONS)}
            className="ants-w-[232px]"
            onChange={onChangeSort}
          />
        </div>

        <Spin spinning={loading || isLoadingStored || isFetchingNextPage}>
          <Dragger
            {...props}
            accept={extensions?.join(',')}
            // action={`${APP_CONFIG.API_URL}/file-upload/file?&_token=${userInfo?.token}&_user_id=${userInfo?.user_id}&_account_id=${userInfo?.account_id}`}
            // beforeUpload={handleBeforeUploadFile}
            // onChange={onChangeFileUpload}
            showUploadList={false}
            multiple={true}
            customRequest={customRequestUpload}
          >
            <div className="ants-flex ants-gap-15px ants-justify-center ants-items-center ants-h-20">
              {/* {renderSelectedImage()} */}
              <Icon type="icon-ants-image-3" size={36} className="ants-text-cus-second" />
              <div className="ants-flex ants-gap-15px ants-justify-start ants-items-center">
                <span className="ants-text-cus-dark ant-upload-text">{t(translations.dragDropFileHere.title)}</span>
                <span>{t(translations.or.title)}</span>
                <Button className="ants-bg-white">{t(translations.selectImageFromComputer.title)}</Button>
                <Button onClick={handleRemoveImage} className="!ants-hidden ants-bg-white">
                  <Icon type="icon-ants-remove-trash" size={15} className="ants-text-primary" />
                </Button>
              </div>
            </div>
          </Dragger>
        </Spin>

        <div className="ants-flex ants-flex-wrap ants-gap-35px ants-pt-5 ants-w-full">
          {renderListImages(flattenedMedia)}
          <div ref={ref} />
        </div>
      </Modal>
    </>
  );
};

UploadImage.defaultProps = {
  isOpen: false,
  extensions: ['.jpg', '.png', '.jfif', '.jpeg', '.gif', '.webp'],
  maxSize: 10,
  showImageURL: true,
  titleImageUrl: '',
};
