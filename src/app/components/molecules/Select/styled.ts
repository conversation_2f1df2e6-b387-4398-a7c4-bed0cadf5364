// Libraries
import styled, { css } from 'styled-components';
import tw from 'twin.macro';

// Antd components
import AntdSelect from 'antd/lib/select';
import { DefaultTag } from 'app/components/atoms';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

export const SelectWrapper = styled.div`
  ${tw`ants-relative ants-flex ants-flex-col`}
`;

export const StyledSelect = styled(AntdSelect)`
  ${tw`ants-font-roboto`}

  .ant-select-arrow {
    ${tw`ants-flex ants-items-center`}
    color: var(--text-base-color) !important;
  }

  &.ant-select-open {
    .ant-select-selector {
      ${tw`ants-bg-blue-second`}
      --tw-shadow: 0 1px 0 0 #194e8d !important;
      box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
    }
  }

  &.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    ${tw`ants-h-30px`}
  }

  .ant-select-selector {
    ${tw`
      ants-justify-between ants-min-h-[30px] ants-px-5px
      ants-text-normal ants-text-cus-base ants-font-normal
      placeholder:ants-text-accent-5
      hover:ants-bg-blue-second
      ants-transition-all
    `}

    height: ${props => props.className?.includes('select-auto-height') && 'auto'};
    border-radius: 0px !important;
    border-style: none !important;
    --tw-shadow: 0 1px 0 0 #e0e0e0 !important;
    --tw-shadow-error: 0 1px 0 0 #ef3340 !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;

    &:active,
    &:focus {
      ${tw`ants-bg-blue-second`}
      --tw-shadow: 0 1px 0 0 #194e8d !important;
      box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
    }

    &:disabled {
      ${tw`ants-bg-accent-1 ants-text-accent-5 ants-shadow-cus-input`}
    }

    .ant-select-selection-overflow {
      row-gap: 5px;
    }

    ${props =>
      props.status === 'error' || props.status === 'warning'
        ? 'box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow-error) !important;'
        : ''}
  }

  &.ant-select-disabled {
    .ant-select-arrow {
      color: var(--text-disable-color) !important;
    }

    .ant-tag {
      opacity: 0.5 !important;
    }
  }
  &.ant-select-status-error {
    /* border-bottom: solid 1px #ff4d4f !important; */
    border-color: #ef3340 !important;
    ::placeholder {
      color: #ff4d4f !important;
    }
  }

  &.left-12 .ant-select-selection-placeholder {
    left: 12px;
  }

  .ant-select-clear {
    ${tw`ants-bg-transparent`}
  }
`;

export const StyledTag = styled(DefaultTag)`
  position: relative;
  display: flex;
  align-items: center;
  width: fit-content;
  height: 24px;
  margin-right: 5px;
  padding: 5px 10px;
  border-radius: 15px;
  border: none;
  background-color: #cae5fe;
  cursor: pointer;

  .ant-tag-close-icon {
    opacity: 0;
    position: absolute;
    right: 2px;
    transition: all 0.3s;
  }

  &:hover {
    .ant-tag-close-icon {
      opacity: 1;
    }
  }
`;

export const CloseButton = styled.div<{ borderColor?: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 26px;
  border: 2px solid ${p => (p.borderColor ? p.borderColor : '#cae5fe')};
  background-color: #fff;
`;

export const SelectOptionWrapper = styled.div``;

export const StyledOptGroup = styled(AntdSelect.OptGroup)``;
