// Libraries
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import isEmpty from 'lodash/isEmpty';

import Froala from 'froala-editor';

// Components
import FroalaEditor from 'react-froala-wysiwyg';

// Froala JS files.
import 'froala-editor/js/froala_editor.pkgd.min.js';
import 'froala-editor/js/languages/vi.js';

// Froala CSS files.
import 'froala-editor/css/froala_style.min.css';
import 'froala-editor/css/froala_editor.pkgd.min.css';

import 'froala-editor/css/plugins/colors.min.css';
import 'froala-editor/css/plugins/emoticons.min.css';

// Froala Plugins
import 'froala-editor/js/plugins/align.min.js';
import 'froala-editor/js/plugins/colors.min.js';
import 'froala-editor/js/plugins/emoticons.min.js';
import 'froala-editor/js/plugins/font_family.min.js';
import 'froala-editor/js/plugins/font_size.min.js';
import 'froala-editor/js/plugins/line_height.min.js';
import 'froala-editor/js/plugins/link.min.js';
import 'froala-editor/js/plugins/lists.min.js';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Types
import type { FontConfig, FontSizeConfig, LetterSpacingConfig, LineHeightConfig, TextTransformConfig } from './types';
import { TextEditorProps } from './types';

// CSS
import 'font-awesome/css/font-awesome.css';

// Styled
import { TextEditorWrapper } from './styled';

// Utils
import { random } from 'app/utils/common';
import { findAncestor } from 'app/utils/dom';
import { isPrintableKey } from 'app/utils/key';
import { getTranslateMessage } from 'utils/messages';
import { getEffectiveFontFamily } from './utils';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { KEY_IME } from 'constants/keyCode';

// prettier-ignore
const DEFAULT_COLOR_CONFIGS = [
  '#61BD6D', '#1ABC9C', '#54ACD2', '#2C82C9', '#9365B8', '#475577', '#CCCCCC',
  '#41A85F', '#00A885', '#3D8EB9', '#2969B0', '#553982', '#28324E', '#000000',
  '#F7DA64', '#FBA026', '#EB6B56', '#E25041', '#A38F84', '#EFEFEF', '#FFFFFF',
  '#FAC51C', '#F37934', '#D14841', '#B8312F', '#7C706B', '#D1D5D8', 'REMOVE',
];

const DEFAULT_FONT_CONFIGS: FontConfig[] = [
  {
    fontFamily: { name: 'Arial' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Georgia' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Helvetica' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Helvetica Neue' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Lucida Grande' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Tahoma' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Times New Roman' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Verdana' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Abril Fatface' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Aleo' },
    fontWeight: [300, 400, 700],
  },
  {
    fontFamily: { name: 'Arvo' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Bitter' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Bree Serif' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Cabin' },
    fontWeight: [400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Cookie' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Delius Swash Caps' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Dosis' },
    fontWeight: [200, 300, 400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Droid Sans' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Droid Serif' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'EB Garamond' },
    fontWeight: [400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Josefin Slab' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Just Another Hand' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Lakki Reddy' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Lato' },
    fontWeight: [100, 300, 400, 700, 900],
  },
  {
    fontFamily: { name: 'Libre Baskerville' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Lobster' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Lora' },
    fontWeight: [400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Mali' },
    fontWeight: [200, 300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Merriweather' },
    fontWeight: [300, 400, 700, 900],
  },
  {
    fontFamily: { name: 'Montserrat' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Noto Sans' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Noto Serif' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Nunito' },
    fontWeight: [200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Open Sans' },
    fontWeight: [300, 400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Oswald' },
    fontWeight: [200, 300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'PT Sans' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'PT Serif' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Pinyon Script' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Playfair Display' },
    fontWeight: [400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Poppins' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Quicksand' },
    fontWeight: [300, 400, 500, 600, 700],
  },
  {
    fontFamily: { name: 'Raleway' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Righteous' },
    fontWeight: [400],
  },
  {
    fontFamily: { name: 'Roboto' },
    fontWeight: [100, 300, 400, 500, 700, 900],
  },
  {
    fontFamily: { name: 'Roboto Slab' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Rubik' },
    fontWeight: [300, 400, 500, 600, 700, 800, 900],
  },
  {
    fontFamily: { name: 'Sacramento' },
    fontWeight: [400, 700],
  },
  {
    fontFamily: { name: 'Sarabun' },
    fontWeight: [100, 200, 300, 400, 500, 600, 700, 800],
  },
  {
    fontFamily: { name: 'Source Sans Pro' },
    fontWeight: [200, 300, 400, 600, 700, 900],
  },
  {
    fontFamily: { name: 'Ubuntu' },
    fontWeight: [200, 300, 400, 600, 700, 900],
  },
  {
    fontFamily: { name: 'Vollkorn' },
    fontWeight: [400, 500, 600, 700, 800, 900],
  },
];
const DEFAULT_FONT_SIZE_CONFIGS: FontSizeConfig[] = [
  { value: '8px', label: '8' },
  { value: '9px', label: '9' },
  { value: '10px', label: '10' },
  { value: '11px', label: '11' },
  { value: '12px', label: '12' },
  { value: '13px', label: '13' },
  { value: '14px', label: '14' },
  { value: '15px', label: '15' },
  { value: '16px', label: '16' },
  { value: '17px', label: '17' },
  { value: '18px', label: '18' },
  { value: '19px', label: '19' },
  { value: '20px', label: '20' },
  { value: '21px', label: '21' },
  { value: '22px', label: '22' },
  { value: '23px', label: '23' },
  { value: '24px', label: '24' },
  { value: '25px', label: '25' },
  { value: '26px', label: '26' },
  { value: '27px', label: '27' },
  { value: '28px', label: '28' },
  { value: '29px', label: '29' },
  { value: '30px', label: '30' },
  { value: '31px', label: '31' },
  { value: '32px', label: '32' },
  { value: '33px', label: '33' },
  { value: '34px', label: '34' },
  { value: '35px', label: '35' },
  { value: '36px', label: '36' },
  { value: '38px', label: '38' },
  { value: '40px', label: '40' },
  { value: '42px', label: '42' },
  { value: '44px', label: '44' },
  { value: '46px', label: '46' },
  { value: '48px', label: '48' },
  { value: '50px', label: '50' },
  { value: '52px', label: '52' },
  { value: '54px', label: '54' },
  { value: '56px', label: '56' },
  { value: '58px', label: '58' },
  { value: '60px', label: '60' },
  { value: '64px', label: '64' },
  { value: '68px', label: '68' },
  { value: '72px', label: '72' },
  { value: '76px', label: '76' },
  { value: '80px', label: '80' },
  { value: '84px', label: '84' },
  { value: '88px', label: '88' },
  { value: '92px', label: '92' },
  { value: '96px', label: '96' },
];
const DEFAULT_LETTER_SPACING_CONFIGS: LetterSpacingConfig[] = [
  { value: '-10px', label: '-10' },
  { value: '-9px', label: '-9' },
  { value: '-8px', label: '-8' },
  { value: '-7px', label: '-7' },
  { value: '-6px', label: '-6' },
  { value: '-5px', label: '-5' },
  { value: '-4px', label: '-4' },
  { value: '-3px', label: '-3' },
  { value: '-2px', label: '-2' },
  { value: '-1px', label: '-1' },
  { value: '0px', label: '0' },
  { value: '1px', label: '1' },
  { value: '2px', label: '2' },
  { value: '3px', label: '3' },
  { value: '4px', label: '4' },
  { value: '5px', label: '5' },
  { value: '6px', label: '6' },
  { value: '7px', label: '7' },
  { value: '8px', label: '8' },
  { value: '9px', label: '9' },
  { value: '10px', label: '10' },
];
const DEFAULT_LINE_HEIGHT_CONFIGS: LineHeightConfig[] = [
  { value: '', label: getTranslateMessage(translations.textEditor.toolbar.lineHeight.options.default) },
  { value: '1', label: getTranslateMessage(translations.textEditor.toolbar.lineHeight.options.single) },
  { value: '2', label: getTranslateMessage(translations.textEditor.toolbar.lineHeight.options.double) },
  { value: '1.1', label: '1.1' },
  { value: '1.2', label: '1.2' },
  { value: '1.3', label: '1.3' },
  { value: '1.4', label: '1.4' },
  { value: '1.5', label: '1.5' },
  { value: '1.6', label: '1.6' },
  { value: '1.7', label: '1.7' },
  { value: '1.8', label: '1.8' },
  { value: '1.9', label: '1.9' },
  { value: '2.0', label: '2.0' },
  { value: '2.1', label: '2.1' },
  { value: '2.2', label: '2.2' },
  { value: '2.3', label: '2.3' },
  { value: '2.4', label: '2.4' },
  { value: '2.5', label: '2.5' },
  { value: '2.6', label: '2.6' },
  { value: '2.7', label: '2.7' },
  { value: '2.8', label: '2.8' },
  { value: '2.9', label: '2.9' },
  { value: '3.0', label: '3.0' },
];
const DEFAULT_TEXT_TRANSFORM_CONFIGS: TextTransformConfig[] = ['none', 'capitalize', 'lowercase', 'uppercase'];

const DEFAULT_FONT_FAMILY = { name: 'Montserrat' };
const DEFAULT_FONT_SIZE: FontSizeConfig = { value: '16px', label: '16' };

export const TextEditor = React.forwardRef<any, TextEditorProps>((props, ref) => {
  // Hooks
  const { i18n, t } = useTranslation();

  // Props
  const {
    boundSelector = random(16),
    defaultValue = '',
    disabled = false,
    placeholder = t(translations.textEditor.placeholder),
  } = props;
  const { onChange, onChangeDynamicLink, onChangeDynamicVariable, onChangeSelection } = props;
  // Config Props
  const { config = {}, customToolbar = {}, defaultFormat = {} } = props;

  const { colorProfile = [] } = config;

  const { font = DEFAULT_FONT_CONFIGS } = config;
  const { fontSize = DEFAULT_FONT_SIZE_CONFIGS } = config;
  const { letterSpacing = DEFAULT_LETTER_SPACING_CONFIGS } = config;
  const { lineHeight = DEFAULT_LINE_HEIGHT_CONFIGS } = config;
  const textTransform = DEFAULT_TEXT_TRANSFORM_CONFIGS;

  const { dynamicLink = {}, dynamicVariable = {} } = customToolbar;

  const { enable: enableDynamicLink = true } = dynamicLink;
  const { enable: enableDynamicVariable = true } = dynamicVariable;

  const { fontFamily: defaultFontFamily = DEFAULT_FONT_FAMILY } = defaultFormat;
  const { fontSize: defaultFontSize = DEFAULT_FONT_SIZE } = defaultFormat;

  // Refs
  const editorRef = useRef<any>(null);
  const froalaRef = useRef<any>(null);

  useImperativeHandle(
    ref,
    () => {
      if (froalaRef.current) {
        return froalaRef.current;
      }
    },

    /* eslint-disable react-hooks/exhaustive-deps */
    [froalaRef.current],
  );

  // States
  const [model, setModel] = useState(defaultValue);

  const [selectedFontFamily, setSelectedFontFamily] = useState(
    font.find(config => config.fontFamily.name === defaultFontFamily.name) || font[0],
  );

  // Define Froala Icons
  useEffect(() => {
    Froala.DefineIconTemplate('at-svg', '[SVG]');

    Froala.DefineIcon('backgroundColor', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' enable-background='new 0 0 24 24' height='24px' viewBox='0 0 24 24' width='24px'>
  <g>
    <g>
      <polygon points='15,16 11,20 21,20 21,16'/>
      <path d='M12.06,7.19L3,16.25V20h3.75l9.06-9.06L12.06,7.19z M5.92,18H5v-0.92l7.06-7.06l0.92,0.92L5.92,18z'/>
      <path d='M18.71,8.04c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C16.17,4.09,15.92,4,15.66,4c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83 l3.75,3.75L18.71,8.04z'/>
    </g>
  </g>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('bold', {
      PATH: 'M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z',
    });
    Froala.DefineIcon('clearFormatting', {
      PATH: 'M3.27 5L2 6.27l6.97 6.97L6.5 19h3l1.57-3.66L16.73 21 18 19.73 3.55 5.27 3.27 5zM6 5v.18L8.82 8h2.4l-.72 1.68 2.1 2.1L14.21 8H20V5H6z',
    });
    Froala.DefineIcon('dynamicLink', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' height='24' width='24' viewBox='0 0 48 48'>
  <path d='M22.5 34H14Q9.75 34 6.875 31.125Q4 28.25 4 24Q4 19.75 6.875 16.875Q9.75 14 14 14H22.5V17H14Q11 17 9 19Q7 21 7 24Q7 27 9 29Q11 31 14 31H22.5ZM16.2 25.5V22.5H31.7V25.5ZM44 24H41Q41 21 39 19Q37 17 34 17H25.5V14H34Q38.25 14 41.125 16.875Q44 19.75 44 24ZM34.95 40V34H28.95V31H34.95V25H37.95V31H43.95V34H37.95V40Z'/>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('dynamicVariable', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'>
  <g transform='translate(-729.516 -264)'>
    <g transform='translate(15.648 -16.948)'>
      <path d='M4.939,3.164A3.9,3.9,0,0,1,1.775-.923v-1.74q0-1.775-1.424-1.775v-2q1.424,0,1.424-1.784v-1.846A4.032,4.032,0,0,1,2.6-12.621a4.505,4.505,0,0,1,2.342-1.415l.554,1.547q-1.107.422-1.151,2.364v1.907A2.85,2.85,0,0,1,2.716-5.44,2.857,2.857,0,0,1,4.342-2.654v1.9q.044,1.942,1.151,2.364Zm5.748-1.556q1.107-.422,1.151-2.364V-2.663A2.837,2.837,0,0,1,13.474-5.44a2.837,2.837,0,0,1-1.635-2.777v-1.907q-.044-1.942-1.151-2.364l.554-1.547A4.425,4.425,0,0,1,13.605-12.6a4.122,4.122,0,0,1,.8,2.615v1.767q0,1.784,1.424,1.784v2q-1.424,0-1.424,1.767V-.8a3.887,3.887,0,0,1-3.164,3.964Z' transform='translate(718.516 297.984)' stroke='rgba(0,0,0,0)' stroke-width='1'/>
    </g>
  </g>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('fontWeight', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' width='22.5' height='17.5' viewBox='0 0 22.5 17.5'>
  <g transform='translate(-713.5 -281.484)'>
    <path d='M5.016,3.984v3H10.5V18.969h3V6.981h5.484v-3Z' transform='translate(708.984 278)' stroke='rgba(0,0,0,0)' stroke-width='1'/>
    <path d='M6,0,7.8,3.693,12,4.2,8.912,6.993,9.708,11,6,9.032,2.292,11l.8-4.007L0,4.2l4.2-.509Z' transform='translate(724 287.984)'/>
  </g>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('indent', {
      PATH: 'M3 21h18v-2H3v2zM3 8v8l4-4-4-4zm8 9h10v-2H11v2zM3 3v2h18V3H3zm8 6h10V7H11v2zm0 4h10v-2H11v2z',
    });
    Froala.DefineIcon('italic', {
      PATH: 'M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4h-8z',
    });
    Froala.DefineIcon('letterSpacing', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' width='27' height='23' viewBox='0 0 27 23'>
    <text transform='translate(1 18)' stroke='rgba(0,0,0,0)' stroke-width='1' font-size='16' font-family='Roboto-Bold, Roboto' font-weight='700' letter-spacing='-0.1em'>
        <tspan x='0' y='0'>V/A</tspan>
    </text>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('outdent', {
      PATH: 'M11 17h10v-2H11v2zm-8-5l4 4V8l-4 4zm0 9h18v-2H3v2zM3 3v2h18V3H3zm8 6h10V7H11v2zm0 4h10v-2H11v2z',
    });
    Froala.DefineIcon('selectAll', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px'>   
    <path d='M3 5h2V3c-1.1 0-2 .9-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2c0-1.1-.9-2-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2zM7 17h10V7H7v10zm2-8h6v6H9V9z'/>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('strikeThrough', {
      PATH: 'M10 19h4v-3h-4v3zM5 4v3h5v3h4V7h5V4H5zM3 14h18v-2H3v2z',
    });
    Froala.DefineIcon('subscript', {
      PATH: 'M22,18h-2v1h3v1h-4v-2c0-0.55,0.45-1,1-1h2v-1h-3v-1h3c0.55,0,1,0.45,1,1v1C23,17.55,22.55,18,22,18z M5.88,18h2.66 l3.4-5.42h0.12l3.4,5.42h2.66l-4.65-7.27L17.81,4h-2.68l-3.07,4.99h-0.12L8.85,4H6.19l4.32,6.73L5.88,18z',
    });
    Froala.DefineIcon('superscript', {
      PATH: 'M22,7h-2v1h3v1h-4V7c0-0.55,0.45-1,1-1h2V5h-3V4h3c0.55,0,1,0.45,1,1v1C23,6.55,22.55,7,22,7z M5.88,20h2.66l3.4-5.42h0.12 l3.4,5.42h2.66l-4.65-7.27L17.81,6h-2.68l-3.07,4.99h-0.12L8.85,6H6.19l4.32,6.73L5.88,20z',
    });
    Froala.DefineIcon('underline', {
      PATH: 'M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z',
    });
    Froala.DefineIcon('textColor', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' width='21.275' height='17.5' viewBox='0 0 21.275 17.5'>
  <g transform='translate(-713.5 -281.484)'>
    <path d='M5.016,3.984v3H10.5V18.969h3V6.981h5.484v-3Z' transform='translate(708.984 278)' stroke='rgba(0,0,0,0)' stroke-width='1'/>
    <path d='M8.879,13.674a4.356,4.356,0,0,0,4.4-4.5c0-1.791-1.479-3.976-4.4-6.5-2.917,2.52-4.4,4.7-4.4,6.5A4.356,4.356,0,0,0,8.879,13.674Z' transform='translate(721.5 285.31)'/>
  </g>
</svg>`,
      template: 'at-svg',
    });
    Froala.DefineIcon('textTransform', {
      SVG: `<svg class='fr-svg' xmlns='http://www.w3.org/2000/svg' width='18.5' height='23' viewBox='0 0 18.5 23'>
  <g transform='translate(-713.5 -279)'>
    <text transform='translate(725 297)' stroke='rgba(0,0,0,0)' stroke-width='1' font-size='16' font-family='Roboto-Bold, Roboto' font-weight='700'>
        <tspan x='0' y='0'>t</tspan>
    </text>
    <path d='M5.016,3.984v3H10.5v12h3v-12h5.484v-3Z' transform='translate(708.984 278)' stroke='rgba(0,0,0,0)' stroke-width='1'/>
  </g>
</svg>`,
      template: 'at-svg',
    });
  }, []);

  // Register Froala Commands
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:backgroundColor`, {
      icon: 'backgroundColor',
      title: getTranslateMessage(translations.textEditor.toolbar.backgroundColor.tooltip),
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      callback: function (cmd, val) {
        this.commands.exec('backgroundColor');
      },
    });
  }, [boundSelector]); // Background Color
  useEffect(() => {
    Froala.RegisterCommand('fontWeight', {
      title: getTranslateMessage(translations.textEditor.toolbar.fontWeight.tooltip),
      type: 'dropdown',
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      options: selectedFontFamily?.fontWeight.reduce((acc, curr) => ({ ...acc, [curr]: curr }), {}) || {},
      callback: function (cmd, val) {
        this.format.applyStyle('font-weight', val);
      },
      refreshOnShow: function ($btn, $dropdown) {
        const list = $dropdown.find('ul.fr-dropdown-list');

        if (selectedFontFamily?.fontWeight.length) {
          const _this = this as any;
          const element = _this.selection.element();
          const style = element.style;
          const currentFontWeight = +style.fontWeight;

          list[0].innerHTML = selectedFontFamily.fontWeight
            .map(
              value => `<li role='presentation'>
  <a 
    class='fr-command${currentFontWeight === +value ? ' fr-active' : ''}' 
    tabindex='-1' 
    role='option' 
    data-cmd='fontWeight' 
    data-param1='${value}'
    title='${value}'
    aria-selected='${currentFontWeight === +value}'
  >
    ${value}
  </a>
</li>`,
            )
            .join('');
        }
      },
    });
  }, [selectedFontFamily]); // Font Weight
  useEffect(() => {
    Froala.RegisterCommand('letterSpacing', {
      title: getTranslateMessage(translations.textEditor.toolbar.letterSpacing.tooltip),
      type: 'dropdown',
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      options: letterSpacing.reduce((acc, curr) => ({ ...acc, [curr.value]: curr.label || curr.value }), {}),
      callback: function (cmd, val) {
        this.format.applyStyle('letter-spacing', val);
      },
      refreshOnShow: function ($btn, $dropdown) {
        const list = $dropdown.find('ul.fr-dropdown-list');

        const _this = this as any;
        const element = _this.selection.element();
        const style = element.style;
        const currentLetterSpacing = style.letterSpacing;

        list[0].innerHTML = letterSpacing
          .map(
            config => `<li role='presentation'>
  <a 
    class='fr-command${currentLetterSpacing === config.value ? ' fr-active' : ''}' 
    tabindex='-1' 
    role='option' 
    data-cmd='letterSpacing' 
    data-param1='${config.value}'
    title='${config.label || config.value}'
    aria-selected='${currentLetterSpacing === config.value}'
  >
    ${config.label || config.value}
  </a>
</li>`,
          )
          .join('');
      },
    });
  }, [letterSpacing]); // Letter Spacing
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:lineHeight`, {
      icon: 'lineHeight',
      title: getTranslateMessage(translations.textEditor.toolbar.lineHeight.tooltip),
      type: 'dropdown',
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      options: lineHeight.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.label || curr.value]: curr.value,
        }),
        {},
      ),
      callback: function (cmd, val) {
        this.lineHeight.apply(val);
      },
      refreshOnShow: function ($btn, $dropdown) {
        const list = $dropdown.find('ul.fr-dropdown-list');

        const _this = this as any;

        let element = _this.selection.element();

        const blocks = _this.selection.blocks();
        if (blocks?.length) {
          element = blocks[0];
        }

        const style = element.style;
        const currentLineHeight = style.lineHeight;

        let hasSelected = false;

        list[0].innerHTML = lineHeight
          .map(config => {
            let selected = false;

            if (Number(currentLineHeight) === Number(config.value) && !hasSelected) {
              selected = true;
              hasSelected = true;
            }

            return `<li role='presentation'>
  <a 
    class='fr-command${selected ? ' fr-active' : ''}' 
    tabindex='-1' 
    role='option' 
    data-cmd='${boundSelector}:lineHeight' 
    data-param1='${config.value}'   
    aria-selected='${selected}'
  >
    ${config.label || config.value}
  </a>
</li>`;
          })
          .join('');
      },
    });
  }, [boundSelector, lineHeight]); // Line Heights
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:dynamicLink`, {
      icon: 'dynamicLink',
      title: getTranslateMessage(translations.textEditor.toolbar.link.tooltip),
      focus: false,
      undo: true,
      callback: function () {
        let dataLinkId = `${random(8)}:create`;

        // Retrieve the first link ID (if exists any)
        const selectedLink = this.link.get();
        if (selectedLink) {
          dataLinkId = `${selectedLink.dataset.linkId}:edit` || dataLinkId;
        }

        const selectionText = this.selection.text();
        this.selection.save();

        if (onChangeDynamicLink) {
          // Using Promise here to make sure the toolbar is hidden before calling callback
          new Promise<void>(resolve => {
            // Hide the toolbar
            setTimeout(() => {
              this.toolbar?.hide();

              resolve();
            }, 100);
          }).then(() => {
            onChangeDynamicLink(dataLinkId, selectionText);
          });
        }
      },
    });

    Froala.RegisterShortcut(75, `${boundSelector}:dynamicLink`, '', 'K');
  }, [boundSelector, onChangeDynamicLink]); // Dynamic Link
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:dynamicLinkEdit`, {
      icon: 'linkEdit',
      title: getTranslateMessage(translations.textEditor.toolbar.linkEdit.tooltip),
      focus: false,
      undo: true,
      callback: function () {
        // Retrieve the first link ID (if exists any)
        const selectedLink = this.link.get();

        if (selectedLink) {
          const dataLinkId = `${selectedLink.dataset.linkId}:edit`;

          if (dataLinkId) {
            const selectionText = this.selection.text();
            this.selection.save();

            if (onChangeDynamicLink) {
              // Using Promise here to make sure the toolbar is hidden before calling callback
              new Promise<void>(resolve => {
                // Hide the toolbar
                setTimeout(() => {
                  this.toolbar?.hide();
                  this.popups?.hide('link.edit');

                  resolve();
                }, 100);
              }).then(() => {
                onChangeDynamicLink(dataLinkId, selectionText);
              });
            }
          }
        }
      },
    });
  }, [boundSelector, onChangeDynamicLink]); // Dynamic Link Edit
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:linkOpen`, {
      icon: 'linkOpen',
      title: getTranslateMessage(translations.textEditor.toolbar.linkOpen.tooltip),
      undo: false,
      refresh: function (e) {
        this.link.get() ? e.removeClass('fr-hidden') : e.addClass('fr-hidden');
      },
      callback: function () {
        this.commands.exec('linkOpen');
      },
    });
  }, [boundSelector]); // Dynamic Link Open
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:linkRemove`, {
      icon: 'linkRemove',
      title: getTranslateMessage(translations.textEditor.toolbar.linkRemove.tooltip),
      undo: false,
      refresh: function (e) {
        this.link.get() ? e.removeClass('fr-hidden') : e.addClass('fr-hidden');
      },
      callback: function () {
        this.commands.exec('linkRemove');
      },
    });
  }, [boundSelector]); // Dynamic Link Remove
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:dynamicVariable`, {
      icon: 'dynamicVariable',
      title: getTranslateMessage(translations.textEditor.toolbar.mergeTags.tooltip),
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      callback: function () {
        this.selection.save();

        let dataDynamicId = `${random(8)}:create`;

        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          let currentNode: Element | null | undefined;

          const range = selection.getRangeAt(0);
          const selectedNode = range.commonAncestorContainer as HTMLElement;

          // Check if selected node is dynamic container or not
          if (selectedNode?.tagName === 'SPAN' && selectedNode?.dataset?.dynamic === 'true') {
            currentNode = selectedNode;
          }
          // Check ancestors/descendants is dynamic container
          else if (selectedNode?.parentElement) {
            // Check node ancestors
            currentNode = findAncestor(
              selectedNode.parentElement,
              node => node?.tagName === 'SPAN' && node?.dataset?.dynamic === 'true',
              node => !!node?.classList?.contains('fr-element'),
              { breakOnFirstMatch: true },
            );

            // Check node descendants (if not found from ancestors)
            if (!currentNode) {
              // We create a temp node to contains selected elements within range only,
              // since we can't get selection combined by text nodes, HTML nodes directly
              const descendantNode = document.createElement('div');
              descendantNode.appendChild(range.cloneContents());

              currentNode = descendantNode?.querySelector('span[data-dynamic="true"]');
            }
          }

          // If there's a dynamic data container, action case is edit
          if (currentNode) {
            if ((currentNode as HTMLElement)?.dataset?.dynamicId) {
              dataDynamicId = `${(currentNode as HTMLElement).dataset.dynamicId}:edit` || dataDynamicId;
            }
          }
        }

        if (onChangeDynamicVariable) {
          // Using Promise here to make sure the toolbar is hidden before calling callback
          new Promise<void>(resolve => {
            // Hide the toolbar
            setTimeout(() => {
              this.toolbar?.hide();

              resolve();
            }, 100);
          }).then(() => {
            onChangeDynamicVariable(dataDynamicId);
          });
        }
      },
    });
  }, [boundSelector, onChangeDynamicVariable]); // Dynamic Variable
  useEffect(() => {
    Froala.RegisterCommand('subscript', {
      title: getTranslateMessage(translations.textEditor.toolbar.subscript.tooltip),
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      callback: function (cmd, val) {
        this.commands.subscript();
      },
    });
  }, []); // Subscript
  useEffect(() => {
    Froala.RegisterCommand('superscript', {
      title: getTranslateMessage(translations.textEditor.toolbar.superscript.tooltip),
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      callback: function (cmd, val) {
        this.commands.superscript();
      },
    });
  }, []); // Superscript
  useEffect(() => {
    Froala.RegisterCommand(`${boundSelector}:textColor`, {
      icon: 'textColor',
      title: getTranslateMessage(translations.textEditor.toolbar.textColor.tooltip),
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      callback: function (cmd, val) {
        this.commands.exec('textColor');
      },
    });
  }, [boundSelector]); // Text Color
  useEffect(() => {
    Froala.RegisterCommand('textTransform', {
      title: getTranslateMessage(translations.textEditor.toolbar.textTransform.tooltip),
      type: 'dropdown',
      focus: false,
      undo: true,
      refreshAfterCallback: true,
      options: textTransform.reduce(
        (acc, curr) => ({
          ...acc,
          [curr]: getTranslateMessage(translations.textEditor.toolbar.textTransform.options[curr]),
        }),
        {},
      ),
      callback: function (cmd, val) {
        this.format.applyStyle('text-transform', val);
      },
      refreshOnShow: function ($btn, $dropdown) {
        const list = $dropdown.find('ul.fr-dropdown-list');

        const _this = this as any;
        const element = _this.selection.element();
        const style = element.style;
        const currentTextTransform = style.textTransform;

        list[0].innerHTML = textTransform
          .map(
            config => `<li role='presentation'>
  <a 
    class='fr-command${currentTextTransform === config ? ' fr-active' : ''}' 
    tabindex='-1' 
    role='option' 
    data-cmd='textTransform' 
    data-param1='${config}'
    title='${getTranslateMessage(translations.textEditor.toolbar.textTransform.options[config])}'
    aria-selected='${currentTextTransform === config}'
  >
    ${getTranslateMessage(translations.textEditor.toolbar.textTransform.options[config])}
  </a>
</li>`,
          )
          .join('');
      },
    });
  }, [textTransform]); // Text Transform
  useEffect(() => {
    Froala.RegisterCommand('searchFontFamily', {
      title: 'Font Family',
      type: 'dropdown',
      focus: true,
      options: font.reduce((acc, curr) => ({ ...acc, [curr.fontFamily.name]: curr.fontFamily.name }), {}),
      refreshAfterCallback: true,
      displaySelection: true,
      callback: function (cmd, val) {
        this.fontFamily.apply(val);
      },
      refresh: function ($btn) {
        // const currentFontFamily = this.selection.element().style.fontFamily?.replace(/"/g, '').trim();
        const currentFontFamily = getEffectiveFontFamily(this.selection.element());

        // Replace the font to btn
        if (currentFontFamily) {
          $btn.html(
            `<span style="font-family: ${currentFontFamily}; width: 120px;">${
              `${currentFontFamily}`.split(',')[0]
            }</span>`,
          );
        }
      },
      refreshOnShow: function ($btn, $dropdown) {
        const _this = this as any;
        const searchInputEls = $dropdown.find(`#font-search`);
        const fontListEls = $dropdown.find(`.fr-front-dropdown-list li a`);

        // Save the current selection
        _this.selection.save();

        // Get current font family
        const element = _this.selection.element();
        const currentFontFamily = `${getEffectiveFontFamily(element) || ''}`.split(',')[0].replace(/"/g, '').trim();
        // const currentFontFamily = element.style.fontFamily?.replace(/"/g, '').trim() || defaultFontFamily.name;

        // Set the font list items to be visible
        fontListEls.each((index, el) => {
          el.parentElement.style.display = 'block';
          const fontName = el.getAttribute('data-param1');

          if (fontName === currentFontFamily) {
            el.classList.add('fr-active');
          } else {
            el.classList.remove('fr-active');
          }

          el.click(() => {
            // Set the font family to the selected item
            _this.fontFamily.apply(fontName);
          });
        });

        if (searchInputEls[0]) {
          const searchInputEl = searchInputEls[0];
          // Reset search input value
          searchInputEl.value = '';

          searchInputEl.addEventListener('click', e => e.stopPropagation());
          searchInputEl.addEventListener('mousedown', e => e.stopPropagation());
          searchInputEl.addEventListener('keydown', (e: any) => {
            e.stopPropagation();
          });
          searchInputEl.addEventListener('input', (e: any) => {
            fontListEls.each((index, el: any) => {
              const text = el.innerText.toLowerCase();
              const keyword = e.target?.value?.toLowerCase();

              el.parentElement.style.display = text.includes(keyword) ? 'block' : 'none';
            });
          });
        }
      },
      html: function () {
        const fontList = font.map(config => config.fontFamily.name);

        const optionsHtml = fontList
          .map(
            font => `
          <li role="presentation"><a class="fr-command font-item" tabindex="-1" role="option" data-cmd="searchFontFamily" data-param1="${font}" title="${font}" style="font-family:${font};" aria-selected="false">${font}</a></li>
        `,
          )
          .join('');

        return `
                  <input type="text" class="fr-font-search" id="font-search" placeholder="${getTranslateMessage(
                    translations.search.title,
                  )}" style="width: 100%; box-sizing: border-box;">
                  <ul class="fr-front-dropdown-list fr-dropdown-list" role="presentation">${optionsHtml}</ul>
          `;
      },
    });
  }, []);

  // Turn off editor when disabled
  useEffect(() => {
    if (editorRef.current) {
      const { editor } = editorRef.current;

      if (editor) {
        editor.opts.spellCheck = !disabled;

        disabled ? editor.edit?.off() : editor.edit?.on();
      }
    }
  }, [disabled]);

  // Update Pop up color picker when color profile changes
  useEffect(() => {
    if (editorRef.current) {
      const { editor } = editorRef.current;

      if (editor) {
        editor.opts.colorsBackground = [...DEFAULT_COLOR_CONFIGS, ...colorProfile];
        editor.opts.colorsText = [...DEFAULT_COLOR_CONFIGS, ...colorProfile];

        if (editor.popups?.get('backgroundColor.picker')) {
          editor.popups?.refresh('backgroundColor.picker');
        }

        if (editor.popups?.get('textColor.picker')) {
          editor.popups?.refresh('textColor.picker');
        }
      }
    }
  }, [colorProfile]);

  useEffect(() => {
    if (isEmpty(defaultValue)) {
      setModel('');
    }
  }, [defaultValue]);

  // Handlers
  const handleModelChange = (newModel: string) => {
    setModel(newModel);

    if (onChange) {
      onChange(newModel);
    }
  };

  return (
    <TextEditorWrapper id={boundSelector}>
      <FroalaEditor
        config={{
          attribution: false,
          autofocus: true,
          charCounterCount: false,
          codeBeautifierOptions: {
            brace_style: 'compact',
            end_with_newline: false,
            extra_liners: '[]',
            indent_char: '',
            indent_inner_html: false,
            indent_size: 0,
            wrap_line_length: 0,
          },
          colorsBackground: [...DEFAULT_COLOR_CONFIGS, ...colorProfile],
          colorsText: [...DEFAULT_COLOR_CONFIGS, ...colorProfile],
          emoticonsUseImage: false,
          events: {
            click: function (event) {
              const _this = this as any;

              // Show toolbar in case selecting hyperlink
              const selectionInfo = _this.selection.get();

              setTimeout(() => {
                const { baseOffset, extentOffset } = selectionInfo;

                if (+baseOffset !== +extentOffset) {
                  _this.popups.hideAll();
                  _this.toolbar.show();
                }
              });
            },
            'commands.before': function (cmd: string, param1, param2) {
              const _this = this as any;

              // Add new span tag before any emphasize tags
              if (['bold', 'italic', 'subscript', 'superscript', 'strikeThrough', 'underline'].includes(cmd)) {
                _this.html.cleanEmptyTags();
                _this.selection.save();

                // Using RegExp since Froala API function sometime not working properly
                let currentHtml: string = _this.html.get(true).toString();
                let currentSelected: any = currentHtml.match(
                  RegExp('<span class="fr-marker"[^>]*>​</span>.*<span class="fr-marker"[^>]*>​</span>'),
                );
                currentSelected = currentSelected?.length ? currentSelected[0] : '';

                if (currentSelected) {
                  let tagName = '';
                  let removedTagName = ''; // The tag should be removed before insert new tag, i.e. sub tag when toggling superscript and vice versa

                  switch (cmd) {
                    case 'bold': {
                      tagName = 'strong';

                      break;
                    }
                    case 'italic': {
                      tagName = 'em';

                      break;
                    }
                    case 'strikeThrough': {
                      tagName = 's';

                      break;
                    }
                    case 'subscript': {
                      tagName = 'sub';
                      removedTagName = 'sup';

                      break;
                    }
                    case 'superscript': {
                      tagName = 'sup';
                      removedTagName = 'sub';

                      break;
                    }
                    case 'underline': {
                      tagName = 'u';

                      break;
                    }
                  }

                  if (tagName) {
                    let currentSelectedElements: string[] = currentSelected
                      .replace(RegExp('<span class="fr-marker"[^>]*>​</span>', 'g'), '')
                      .split(RegExp('</p>|</li>|</span>'))
                      .map(text => text.replace(RegExp('<.[^>]*>', 'g'), ''))
                      .filter(text => text.length);

                    if (currentSelectedElements.length !== 0 && currentSelectedElements[0] !== ' ') {
                      let currentText: string = currentSelectedElements.map(text => `(${text})`).join('|');
                      let currentTextWithTagName: string = currentSelectedElements
                        .map(text => `(<${tagName}>.*${text}.*</${tagName}>)`)
                        .join('|');

                      let newText;
                      if (RegExp(currentTextWithTagName, 'g').test(currentSelected) || _this.format.is(tagName)) {
                        _this.format.toggle(tagName);

                        newText = currentSelected
                          .replace(RegExp(`<${tagName}>`, 'g'), '')
                          .replace(RegExp(`</${tagName}>`, 'g'), '');
                      } else {
                        newText = currentSelected
                          .replace(RegExp(`<${tagName}>`, 'g'), '')
                          .replace(RegExp(`</${tagName}>`, 'g'), '')
                          .replace(
                            RegExp(currentText, 'g'),
                            match => `<${tagName} data-temp="true">${match}</${tagName}>`,
                          );

                        if (removedTagName) {
                          newText = newText
                            .replace(RegExp(`<${removedTagName}>`, 'g'), '')
                            .replace(RegExp(`</${removedTagName}>`, 'g'), '');
                        }
                      }

                      if (newText !== currentSelected) {
                        currentHtml = currentHtml.replace(currentSelected, newText);
                        currentHtml = _this.clean.html(currentHtml);

                        _this.html.set(currentHtml);

                        const editorEl = _this.el;
                        const tempTag = editorEl.querySelector(`${tagName}[data-temp="true"]`);
                        if (tempTag) {
                          const range = document.createRange();
                          range.selectNodeContents(tempTag);

                          const sel = window.getSelection();
                          if (sel) {
                            sel.removeAllRanges();
                            sel.addRange(range);
                          }

                          tempTag.removeAttribute('data-temp');
                        }
                      }
                    }
                  }

                  _this.selection.restore();

                  return false;
                }
              }

              // Check current selection is the link or not, remove its color default
              if (cmd === 'textColor') {
                const listLinks = _this.link.allSelected();

                if (listLinks?.length) {
                  for (const link of listLinks) {
                    link.style.color = 'inherit';
                  }
                }
              }

              return true;
            },
            'commands.after': function (cmd, param1, param2) {
              const _this = this as any;

              switch (cmd) {
                case 'backgroundColor':
                case 'emoticons':
                case 'textColor': {
                  _this.toolbar.show();

                  break;
                }
                case 'clearFormatting': {
                  _this.selection.save();

                  // Remove empty tags
                  let currentHtml = _this.html
                    .get(true)
                    .toString()
                    .replace(RegExp('<p>', 'g'), '<p><span>')
                    .replace(RegExp('</p>', 'g'), '</span></p>');

                  currentHtml = _this.clean.html(currentHtml);
                  _this.html.set(currentHtml);
                  _this.selection.restore();

                  // Set editor area style to default
                  const element = document.querySelector(`#${boundSelector} .fr-element.fr-view`) as HTMLElement;

                  if (element) {
                    element.style.fontFamily = defaultFontFamily.name;
                    element.style.fontSize = defaultFontSize.value;
                  }

                  break;
                }
              }

              // Cleanup safety: remove any leftover temp tags (just in case)
              const leftoverTemp = _this.el.querySelector('[data-temp="true"]');
              if (leftoverTemp) leftoverTemp.removeAttribute('data-temp');

              return true;
            },
            initialized: function () {
              if (!froalaRef?.current) {
                froalaRef.current = this;
              }
            },
            input: function (event) {
              event.target.querySelectorAll('[data-dynamic="true"][contenteditable="false"]').forEach(el => {
                el.removeAttribute('contenteditable');
              });

              if (onChange) {
                onChange((this as any).html.get(), 'user');
              }
            },
            keydown: function (event) {
              if (
                (event.keyCode !== KEY_IME && event.key.length === 1) ||
                (event.keyCode === KEY_IME && event.originalEvent.composed && isPrintableKey(event.originalEvent.code))
              ) {
                event.target.querySelectorAll('[data-dynamic="true"]:not([contenteditable="false"])').forEach(el => {
                  el.setAttribute('contenteditable', 'false');
                });

                (this as any).events.focus();
              }
            },
            'link.beforeRemove': function (link, text, attrs) {
              const { linkId } = link.dataset;

              if (onChangeDynamicLink) {
                onChangeDynamicLink(`${linkId}:unlink`, '');
              }

              return true;
            },
            'paste.after': function (event) {
              if (onChange) {
                onChange((this as any).html.get(), 'user');
              }
            },
            'paste.before': function (event) {
              if (event.target) {
                const selection = window.getSelection();

                // Selection collapsed, i.e. caret
                if (selection?.isCollapsed) {
                  const container = event.target as HTMLElement;
                  const range = selection.getRangeAt(0);

                  // Check ancestors/descendants is dynamic container
                  let currentNode: Element | null | undefined;
                  if (container) {
                    // Check node ancestors (get the outermost ancestor)
                    currentNode = findAncestor(
                      container,
                      node => node?.dataset?.dynamic === 'true',
                      node => !!node?.classList?.contains('fr-element'),
                    );

                    // Check node descendants, if not found from ancestors (get the outermost descendants)
                    if (!currentNode) {
                      // We create a temp node to contains selected elements within range only,
                      // since we can't get selection combined by text nodes, HTML nodes directly
                      const descendantNode = document.createElement('div');
                      descendantNode.appendChild(range.cloneContents());

                      currentNode = descendantNode?.querySelector('[data-dynamic="true"]');
                    }
                  }

                  // Insert adjacent text if current caret node is at rear of the dynamic container
                  const currentText = (currentNode as HTMLElement)?.innerText || '';
                  if (currentNode && (range.startOffset === 0 || range.endOffset === currentText.length)) {
                    if (range.startOffset === 0) {
                      currentNode.insertAdjacentText('beforebegin', event.clipboardData.getData('text'));

                      range.selectNode(currentNode);
                      range.collapse(true);
                    } else {
                      currentNode.insertAdjacentText('afterend', event.clipboardData.getData('text'));

                      range.selectNode(currentNode.nextSibling as Node);
                      range.collapse(false);
                    }

                    // Update caret to the end of the new pasted text
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Return false to cancel current event
                    return false;
                  }
                }
              }
            },
            'paste.beforeCleanup': function (clipboard_html) {
              return clipboard_html
                .toString()
                .replace(RegExp('<p>', 'g'), '<p><span>')
                .replace(RegExp('</p>', 'g'), '</span></p>');
            },
            'popups.show.backgroundColor.picker': function () {
              const popUps = document.querySelector<HTMLElement>('.fr-color-set.fr-background-color.fr-selected-set');
              const btnRemove = popUps?.querySelector<HTMLElement>('span[data-param1="REMOVE"]');
              const next = btnRemove?.nextElementSibling?.nextElementSibling as HTMLElement;

              if (next) {
                next.style.marginTop = '5px';
              }
            },
            'popups.show.textColor.picker': function () {
              const popUps = document.querySelector<HTMLElement>('.fr-color-set.fr-text-color.fr-selected-set');
              const btnRemove = popUps?.querySelector<HTMLElement>('span[data-param1="REMOVE"]');
              const next = btnRemove?.nextElementSibling?.nextElementSibling as HTMLElement;

              if (next) {
                next.style.marginTop = '5px';
              }
            },
            'toolbar.show': function () {
              const _this = this as any;
              const element = _this.selection.element();
              const style = element.style;

              const _selectedFontFamily = style.fontFamily.replace(/"/g, '').trim();
              setSelectedFontFamily(font.find(config => config.fontFamily.name === _selectedFontFamily) || font[0]);

              const selectionInfo = _this.selection.get();

              if (onChangeSelection) {
                const { baseOffset, extentOffset } = selectionInfo;

                onChangeSelection({ index: baseOffset, length: Math.abs(baseOffset - extentOffset) });
              }
            },
          },
          fontFamily: font
            .map(config => config.fontFamily)
            .reduce((acc, curr) => ({ ...acc, [curr.name]: curr.label || curr.name }), {}),
          fontFamilyDefaultSelection: defaultFontFamily.name,
          fontFamilySelection: true,
          fontSize: fontSize.map(config => config.label),
          fontSizeDefaultSelection: defaultFontSize.label,
          fontSizeSelection: true,
          htmlUntouched: true,
          initOnClick: true,
          keepFormatOnDelete: true,
          key: APP_CONFIG.FROALA_API_KEY,
          language: i18n.language.substring(0, 2),
          linkEditButtons: Boolean(enableDynamicLink)
            ? [`${boundSelector}:linkOpen`, `${boundSelector}:dynamicLinkEdit`, `${boundSelector}:linkRemove`]
            : [`${boundSelector}:linkOpen`, `linkEdit`, `linkRemove`],
          pasteAllowedStyleProps: [
            'background-color',
            'color',
            'font-family',
            'font-size',
            'font-weight',
            'letter-spacing',
            'line-height',
            'text-align',
            'text-transform',
          ],
          pasteDeniedTags: ['img', 'video', 'table', 'iframe', 'script'],
          placeholderText: (!disabled && placeholder) || '',
          spellCheck: !disabled,
          toolbarButtons: {
            moreText: {
              buttons: [
                // 'fontFamily',
                `searchFontFamily`,
                'fontSize',
                'bold',
                'italic',
                'underline',
                Boolean(enableDynamicLink) ? `${boundSelector}:dynamicLink` : 'insertLink',
                '|',
              ],
              buttonsVisible: 6,
            },
            moreRich: {
              buttons: ['strikeThrough', 'subscript', 'superscript', '|'],
              buttonsVisible: 3,
            },
            font: {
              buttons: [
                'fontWeight',
                'textTransform',
                `${boundSelector}:textColor`,
                `${boundSelector}:backgroundColor`,
                'emoticons',
                Boolean(enableDynamicVariable) && `${boundSelector}:dynamicVariable`,
              ],
              buttonsVisible: 6,
            },
            moreParagraph: {
              buttons: [
                'formatOL',
                'formatUL',
                'align',
                `${boundSelector}:lineHeight`,
                'letterSpacing',
                'indent',
                'outdent',
                '|',
              ],
              buttonsVisible: 7,
            },
            moreMisc: { buttons: ['undo', 'redo', 'selectAll', 'clearFormatting'], buttonsVisible: 4 },
          },
          toolbarInline: true,
          toolbarSticky: false,
        }}
        model={model}
        ref={editorRef}
        //@ts-ignore
        skipReset={true}
        onModelChange={handleModelChange}
      />
    </TextEditorWrapper>
  );
});

TextEditor.displayName = 'TextEditor';
