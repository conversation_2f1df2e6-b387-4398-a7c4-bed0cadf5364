// Libraries
import React, { useEffect, useState } from 'react';
import { Draggable, DraggableProvided } from 'react-beautiful-dnd';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Checkbox, Icon, Input, Radio, Text } from 'app/components/atoms';

// Styles
import { OptionSettings } from './styled';

// Molecules
import { Select } from 'app/components/molecules/Select';

// Selectors
import {
  selectSidePanel,
  selectViewPageSelected,
  selectViewPages,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Hooks
import { useDebounce, useUpdateEffect } from 'app/hooks';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { getObjSafely } from 'app/utils/common';

// Constant
import { ERROR_MESSAGES } from '../../constant';

export type Tfield = {
  id?: string;
  value?: string | number;
  label?: string;
  isPreselect?: boolean;
  goToView?: string;
  displayAlias?: string;
  order?: number;
  isActive?: boolean;
  score?: number;
};

type TDropOptionProps = {
  type?: string;
  idOption?: string;
  idField?: string;
  settings: Tfield;
  fields: Tfield[];
  index: number;
  memoIdViewPage?: string;
  onChange: (value: Tfield[]) => void;
  handleUpdateDropdown?: (data: Tfield[]) => void;
  customRenderer?: { [K in keyof Tfield]: React.ReactNode };
};

const DragOption: React.FC<TDropOptionProps> = props => {
  const {
    index,
    type: typeField,
    settings,
    fields,
    idOption,
    handleUpdateDropdown,
    customRenderer,
    memoIdViewPage,
    idField,
  } = props;

  const [blockSetting, setBlockSetting] = useState(settings || {});

  const debounceValue = useDebounce(blockSetting.value, 300);
  const debounceLabel = useDebounce(blockSetting.label, 300);
  const debounceDisplayAlias = useDebounce(blockSetting.displayAlias, 300);

  // Actions
  const { setSidePanelSettings } = mediaTemplateDesignActions;

  // Dispatch
  const dispatch = useDispatch();

  // Selectors
  const viewPages = useSelector(selectViewPages);
  const sidePanel = useSelector(selectSidePanel);
  const viewPageSelected = useSelector(selectViewPageSelected);
  const optinFields = getObjSafely(() => sidePanel.settings.optinFields);

  useEffect(() => {
    setBlockSetting(settings);
  }, [settings]);

  useUpdateEffect(() => {
    onChangeSetting('value', debounceValue);

    if (!blockSetting.value) {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              value: { ...optinFields.errors.value, [idOption || '']: ERROR_MESSAGES.IS_REQUIRE.label },
            },
          },
        }),
      );
    } else {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              value: { ...optinFields.errors.value, [idOption || '']: '' },
            },
          },
        }),
      );
    }
  }, [debounceValue]);

  useUpdateEffect(() => {
    onChangeSetting('label', debounceLabel);

    if (!blockSetting.label) {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              label: { ...optinFields.errors.label, [idOption || '']: ERROR_MESSAGES.IS_REQUIRE.label },
            },
          },
        }),
      );
    } else {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              label: { ...optinFields.errors.label, [idOption || '']: '' },
            },
          },
        }),
      );
    }
  }, [debounceLabel]);

  useUpdateEffect(() => {
    onChangeSetting('displayAlias', debounceDisplayAlias);
  }, [debounceDisplayAlias]);

  const onDeleteOption = (id: string) => {
    handleUpdateDropdown?.(fields.filter(data => data.id !== id));
  };

  const onChangeSetting = (key: string, value: boolean) => {
    if (!handleUpdateDropdown || !fields.length) return;

    if (key === 'isPreselect' && ['radioButton', 'dropdownSelect', 'rating'].some(t => t === typeField)) {
      handleUpdateDropdown(
        produce(fields, draft => {
          draft.forEach(field => {
            field[key] = field.id === idOption ? value : false;
          });
        }),
      );

      return;
    }

    handleUpdateDropdown(
      produce(fields, draft => {
        draft.forEach(field => {
          if (field.id && field.id === idOption) {
            field[key] = value;
          }
        });
      }),
    );
  };

  const customPropertyRender = (propertyName: keyof Tfield, defaultNode: React.ReactNode): React.ReactNode => {
    if (customRenderer && Object.hasOwn(customRenderer, propertyName)) {
      return customRenderer[propertyName] || defaultNode;
    }

    return defaultNode;
  };

  const renderDragBlock = (provided: DraggableProvided, field: Tfield): React.ReactElement => {
    let element: React.ReactNode = null;

    switch (typeField) {
      case 'datetime': {
        element = (
          <>
            <Icon type="icon-ants-double-three-dots" size={16} className="icon" />

            <Text size={18} className="ants-flex-grow block-title">
              {field.label}
            </Text>

            <Input
              value={field.displayAlias}
              disabled={!field.isActive}
              placeholder={field.label}
              onChange={e => setBlockSetting({ ...blockSetting, displayAlias: e.target.value })}
              maxLength={255}
              style={{ height: '22px' }}
            />

            <Checkbox checked={field.isActive} onChange={e => onChangeSetting('isActive', e.target.checked)} />
          </>
        );
        break;
      }

      case 'dropdownSelect':
      case 'radioButton':
      case 'rating': {
        const listGoToView = [{ title: 'None', id: 'none' }, ...viewPages]
          .filter(each => each.id !== viewPageSelected)
          .map(page => ({
            label: page.title,
            value: page.id,
          }));

        element = (
          <>
            <Icon type="icon-ants-double-three-dots" size={16} className="icon" />

            <Input
              value={field.value}
              width={30}
              onChange={e => setBlockSetting({ ...blockSetting, value: e.target.value })}
            />

            <Input
              value={field.label}
              width={60}
              debounce={500}
              onChange={e => setBlockSetting({ ...blockSetting, label: e.target.value })}
            />

            {customPropertyRender(
              'isPreselect',
              <Radio
                checked={field.isPreselect}
                onChange={e => onChangeSetting('isPreselect', e.target.checked)}
                onClick={() => {
                  if (field.isPreselect) {
                    onChangeSetting('isPreselect', false);
                  }
                }}
              />,
            )}

            <Select
              options={listGoToView}
              value={field.goToView}
              onChange={value => onChangeSetting('goToView', value)}
              disabled={Boolean(memoIdViewPage && idField && memoIdViewPage !== idField)}
            />

            <Icon
              customClassName="icon-close"
              type="icon-ants-remove-slim"
              onClick={() => {
                if (field.id) {
                  onDeleteOption(field.id);
                }
              }}
            />
          </>
        );
        break;
      }

      case 'checkbox': {
        element = (
          <>
            <Icon type="icon-ants-double-three-dots" size={16} className="icon" />

            <Input
              value={field.value}
              width={30}
              onChange={e => setBlockSetting({ ...blockSetting, value: e.target.value })}
            />

            <Input
              value={field.label}
              style={{ width: 75 }}
              onChange={e => setBlockSetting({ ...blockSetting, label: e.target.value })}
            />

            <Checkbox
              checked={field.isPreselect}
              style={{ marginRight: 25 }}
              onChange={e => onChangeSetting('isPreselect', e.target.checked)}
            />

            <Icon
              customClassName="icon-close"
              type="icon-ants-remove-slim"
              onClick={() => {
                if (field.id) {
                  onDeleteOption(field.id);
                }
              }}
            />
          </>
        );
        break;
      }

      default:
        break;
    }

    return (
      <div
        ref={provided?.innerRef}
        {...provided?.draggableProps}
        {...provided?.dragHandleProps}
        className="ants-flex ants-flex-col ants-w-100 ants-items-stretch ants-mb-2"
      >
        <OptionSettings className="ants-grid ants-items-center">{element}</OptionSettings>

        {(optinFields.errors?.label || optinFields.errors?.value) && (
          <div className="ants-mt-[2px] ants-text-cus-error ants-text-[11px]">
            {idOption ? optinFields.errors.label[idOption] || optinFields.errors.value[idOption] : ''}
          </div>
        )}
      </div>
    );
  };

  if (!idOption) {
    return null;
  }

  return (
    <Draggable key={idOption} draggableId={idOption} index={index}>
      {provide => renderDragBlock(provide, settings)}
    </Draggable>
  );
};

export default DragOption;
