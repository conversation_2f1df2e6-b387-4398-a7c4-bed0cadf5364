// Libraries
import React from 'react';
import classNames from 'classnames';

// Components
import { Button, Flex, Text } from '@antscorp/antsomi-ui';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Translations
import { translations } from 'locales/translations';

// Styled
import styled from 'styled-components';
import { RATING_TYPE, RATING_LABELS } from 'app/components/atoms/Rating/constants';
import {
  EmotionFeedbackIcon,
  RatingStarHalfIcon,
  ThumbDownHandIcon,
  ThumbUpHandIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import { THEME } from '@antscorp/antsomi-ui/es/constants';

const RatingTypeSelectorWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  .rating-type-options {
    display: flex;
    gap: 15px;
  }

  .rating-type-option {
    .antsomi-btn {
      padding: 0;
      width: 36px;
      height: 30px;
      color: ${THEME.token?.bw8};

      svg {
        flex-shrink: 0;
      }
    }

    &:hover {
      border-color: #1890ff;
      color: ${THEME.token?.colorPrimary};
    }

    &.selected {
      .antsomi-btn {
        border: 1px solid ${THEME.token?.colorPrimary};
        background: ${THEME.token?.blue1_1};
        color: ${THEME.token?.colorPrimary};
      }
    }

    &.selected .rating-type-label {
      color: ${THEME.token?.colorPrimary};
      font-weight: 500;
    }
  }
`;

interface RatingTypeSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

export const RatingTypeSelector: React.FC<RatingTypeSelectorProps> = props => {
  const { value, onChange } = props;

  const handleSelect = (ratingType: string) => {
    onChange(ratingType);
  };

  const renderIcon = (type: string) => {
    switch (type) {
      case RATING_TYPE.STAR:
        return <RatingStarHalfIcon size={22} />;
      case RATING_TYPE.EMOTION:
        return <EmotionFeedbackIcon size={22} />;
      case RATING_TYPE.YESNO:
        return (
          <Flex gap={3}>
            <ThumbUpHandIcon size={12} />
            <ThumbDownHandIcon size={12} />
          </Flex>
        );
    }
  };

  return (
    <RatingTypeSelectorWrapper>
      <Text>{getTranslateMessage(translations.rating.ratingType.title)}</Text>

      <div className="rating-type-options">
        {Object.values(RATING_TYPE).map(type => {
          const selected = value === type;

          return (
            <Flex
              key={type}
              className={classNames('rating-type-option', {
                selected,
              })}
              vertical
              gap={10}
              align="center"
            >
              <Button key={type} type="text" onClick={() => handleSelect(type)}>
                {renderIcon(type)}
              </Button>

              <Text className="rating-type-label">{RATING_LABELS[type]}</Text>
            </Flex>
          );
        })}
      </div>
    </RatingTypeSelectorWrapper>
  );
};
