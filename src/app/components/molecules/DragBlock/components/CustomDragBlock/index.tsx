// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { DragDropContext, DropResult, Droppable } from 'react-beautiful-dnd';
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import styles from './styles.module.scss';

// Atoms
import { Button, Popover, Text, TextArea } from 'app/components/atoms';

// Utils
import { random, reorder } from 'app/utils/common';
import { formatStringToOptions, validateExtendValue } from './utils';

// Styled
import { CustomDragBlockRoot, WrapperHeader } from './styled';

// Components
import DragOption, { Tfield } from '../DragOption';
import { THEME } from '@antscorp/antsomi-ui/es/constants';
import { mapValues } from 'lodash';
import classNames from 'classnames';

export type TFieldArrays = {
  [key in 'fieldOptions' | 'listDropdownSelect' | 'ratingOptions']?: Tfield[];
} & {
  id: string;
  type: string;
};

type CustomDragBlockProps = {
  value: Tfield[];
  id: string;
  titles: string[];
  type: string;
  idField?: string;
  extensible?: boolean;
  addNewable?: boolean;
  fieldsArray?: TFieldArrays[];
  onChange: (value: Tfield[]) => void;
  newOptionCreating?: (defaultOpt: Tfield) => Tfield | null;
  customRenderer?: { [K in keyof Tfield]?: (option: Tfield) => React.ReactNode };
};

const CustomDragBlock: React.FC<CustomDragBlockProps> = props => {
  const {
    id,
    value: listData,
    titles,
    newOptionCreating,
    type,
    idField,
    fieldsArray,
    customRenderer,
    onChange,
    extensible = true,
    addNewable = true,
  } = props;

  // State
  const [visiblePopoverExtends, setVisiblePopoverExtends] = useState(false);
  const [nextNumOptions, setNextNumOptions] = useState(1);
  const [valueExtends, setValueExtends] = useState('');

  // I18n
  const { t } = useTranslation();

  // Actions
  const isDisplayHeader = ['dropdownSelect', 'checkbox', 'radioButton', 'rating'].includes(type);
  const isDisplayFooter = ['dropdownSelect', 'checkbox', 'radioButton', 'rating'].includes(type);

  const errorFormatExtendValue = useMemo(() => {
    try {
      if (visiblePopoverExtends) {
        return validateExtendValue(valueExtends, type);
      }
    } catch (error) {
      return false;
    }
  }, [type, valueExtends, visiblePopoverExtends]);

  const memoIdViewPage = useMemo(() => {
    let result = '';

    (fieldsArray || []).forEach(field => {
      const fieldOptions = field.fieldOptions || field.listDropdownSelect || field.ratingOptions;

      if (fieldOptions?.length) {
        const itemGoToView = fieldOptions.find(item => item.goToView && item.goToView !== 'none');

        if (itemGoToView) {
          result = field.id;
        }
      }
    });

    return result;
  }, [fieldsArray]);

  useEffect(() => {
    let result = '';

    if (listData && visiblePopoverExtends) {
      let filterArray: Tfield[] = [];

      if (['radioButton', 'dropdownSelect', 'ratingOptions'].includes(type)) {
        filterArray = listData.map(item => ({
          value: item.value,
          label: item.label,
          isPreselect: item.isPreselect,
          goToView: item.goToView,
        }));
      } else {
        filterArray = listData.map(item => ({
          value: item.value,
          label: item.label,
          isPreselect: item.isPreselect,
        }));
      }

      filterArray.forEach(field => {
        result += Object.values(field).join(', ') + '\n';
      });

      setValueExtends(result);
    }
  }, [listData, type, visiblePopoverExtends]);

  const handleUpdateDropdown = (data: Tfield[]) => {
    onChange(data);
  };

  const handleApplyExtendValue = () => {
    const newOptionFields = formatStringToOptions(valueExtends, type);

    if (newOptionFields) {
      onChange([...newOptionFields]);
    }

    setVisiblePopoverExtends(false);
  };

  const handleExtendsValue = () => {
    setVisiblePopoverExtends(true);
  };

  const content = (
    <div className="ants-h-[260px] ants-w-[484px]">
      <Text className="ants-mt-[5px] ants-text-black">{t(translations.extendValues.title)}</Text>

      <TextArea
        value={valueExtends}
        placeholder={t(translations.extendValues.placeholder)}
        rows={10}
        onChange={e => {
          setValueExtends(e.target.value);
        }}
      />

      <div className="ants-flex ants-items-center ants-gap-[15px] ants-mt-[15px]">
        <Button
          type="primary"
          className="ants-font-normal"
          onClick={handleApplyExtendValue}
          disabled={!!errorFormatExtendValue}
        >
          {t(translations.apply.title)}
        </Button>

        <Button
          type="text"
          style={{ border: `1px solid ${THEME.token?.blue1}` }}
          className="ants-font-normal"
          onClick={() => setVisiblePopoverExtends(false)}
        >
          {t(translations.cancel.title)}
        </Button>
      </div>
    </div>
  );

  const onDragEnd = (result: DropResult) => {
    // dropped outside the list || dropped itself
    if (!result.destination || result.destination.index === result.source.index) {
      return;
    }

    const reorderFields = reorder(listData, result.source?.index, result.destination?.index)
      // change order based on index of new array
      .map((item, index) => ({ ...item, order: index }));

    onChange(reorderFields);
  };

  const onAddAnOption = () => {
    setNextNumOptions(nextNumOptions + 1);

    let option: Tfield | null = {
      id: random(6),
      order: nextNumOptions,
      value: nextNumOptions + 1,
      label: `Option ${nextNumOptions + 1}`,
      isPreselect: false,
      goToView: 'none',
    };

    if (newOptionCreating) {
      option = newOptionCreating(option);
    }

    if (option) {
      onChange([...listData, option]);
    }
  };

  const onRemoveAll = () => {
    onChange([]);
  };

  return (
    <CustomDragBlockRoot
      style={{ '--field-type': type } as React.CSSProperties}
      $type={type}
      className={classNames('ants-flex ants-flex-col', styles.root)}
    >
      <div className="ants-w-[150px] ants-flex ants-justify-between ants-mb-[10px]">
        {titles.map((title, index) => (
          <Text key={`${title}-${index}`}>{title}</Text>
        ))}
      </div>

      {isDisplayHeader && (
        <WrapperHeader>
          <div className="value-header">{t(translations.fieldOptionHead.value.title)}</div>
          <div className="label-header">{t(translations.fieldOptionHead.label.title)}</div>
          <div className="preselect-header">{t(translations.fieldOptionHead.preselect.title)}</div>

          {type !== 'checkbox' && (
            <div className="go-to-view-header">{t(translations.fieldOptionHead.goToView.title)}</div>
          )}

          <Button className="remove-all-header" color={THEME.token?.colorPrimary} type="link" onClick={onRemoveAll}>
            {t(translations.fieldOptionHead.removeAll.title)}
          </Button>
        </WrapperHeader>
      )}

      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId={id}>
          {provided => (
            <div ref={provided.innerRef} {...provided.droppableProps}>
              {listData.map((field, index) => (
                <DragOption
                  fields={listData}
                  idOption={field.id}
                  index={index}
                  key={field.id}
                  idField={idField}
                  type={type}
                  settings={field}
                  onChange={onChange}
                  handleUpdateDropdown={handleUpdateDropdown}
                  memoIdViewPage={memoIdViewPage}
                  customRenderer={mapValues(customRenderer, renderer => renderer?.(field))}
                />
              ))}

              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {isDisplayFooter && (
        <div className="ants-flex ants-justify-between ants-items-center">
          {addNewable && (
            <Button type="link" color={THEME.token?.colorPrimary} onClick={onAddAnOption}>
              + {t(translations.addAnOption.title)}
            </Button>
          )}

          {extensible && (
            <Popover
              placement="bottomRight"
              trigger={['click']}
              content={content}
              visible={visiblePopoverExtends}
              destroyTooltipOnHide
            >
              <Button color={THEME.token?.colorPrimary} type="link" onClick={handleExtendsValue}>
                {t(translations.extendValues.title)}
              </Button>
            </Popover>
          )}
        </div>
      )}
    </CustomDragBlockRoot>
  );
};

export default CustomDragBlock;
