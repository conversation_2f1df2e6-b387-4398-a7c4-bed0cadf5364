// Libraries
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Layout, LayoutProps } from '@antscorp/antsomi-ui';

interface LayoutV2Props extends LayoutProps {
  children: React.ReactNode;
  helmet?: {
    title: string;
    description: string;
  };
  [key: string]: any;
}

export const LayoutV2: React.FC<LayoutV2Props> = props => {
  const { children, helmet, ...restProps } = props;

  const { i18n } = useTranslation();

  return (
    <Layout {...restProps} headerProps={{ ...restProps.headerProps, accountSelection: { show: false } }}>
      <Helmet titleTemplate="%s - Antsomi" defaultTitle="Media template" htmlAttributes={{ lang: i18n.language }}>
        <title>{helmet?.title}</title>
        <meta name="description" content={helmet?.description} />
      </Helmet>
      {children}
    </Layout>
  );
};
