import { iconBgVariableName } from './constants';

declare module 'react' {
  interface CSSProperties {
    [iconBgVariableName]?: string;
  }
}

type IconProps = {
  className?: string;
  width?: number;
  height?: number;
  backgroundColor?: string;
  borderColor?: string;
  style?: React.CSSProperties;
};

export const YesIcon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <path
      d="M9.2766 22C8.29788 22 7.44682 21.1101 7.44682 19.986V10.0094C7.44682 9.44731 7.65958 8.97892 8.00001 8.60422L13.9574 2L14.9362 3.07728C15.1915 3.31148 15.3191 3.73302 15.3191 4.10773V4.4356L14.4256 9.02576H20.1702C21.1489 9.02576 22 9.91569 22 10.993V13.007C22 13.288 21.9575 13.5222 21.8724 13.7564L19.1064 20.7822C18.8511 21.4848 18.2128 22 17.4468 22H9.2766ZM9.2766 10.0094V19.986H17.4468L20.1702 13.007V10.993H12L13.2341 5.65339L9.2766 10.0094ZM2 10.0094V22H5.65957V10.0094H2Z"
      fill={borderColor}
    />
    <path d="M9 10L13.5 5L12 11H20.5V13L17.5 20H9V10Z" fill={`var(${iconBgVariableName})`} />
  </svg>
);

export const NoIcon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <path
      d="M9.2766 2C8.29788 2 7.44682 2.88991 7.44682 4.01403V13.9906C7.44682 14.5527 7.65958 15.0211 8.00001 15.3958L13.9574 22L14.9362 20.9227C15.1915 20.6885 15.3191 20.267 15.3191 19.8923V19.5644L14.4256 14.9742H20.1702C21.1489 14.9742 22 14.0843 22 13.007V10.993C22 10.712 21.9575 10.4778 21.8724 10.2436L19.1064 3.21785C18.8511 2.5152 18.2128 2 17.4468 2H9.2766ZM9.2766 13.9906V4.01403H17.4468L20.1702 10.993V13.007H12L13.2341 18.3466L9.2766 13.9906ZM2 13.9906V2H5.65957V13.9906H2Z"
      fill={borderColor}
    />
    <path d="M9 14L13.5 19L12 13H20.5V11L17.5 4H9V14Z" fill={`var(${iconBgVariableName})`} />
  </svg>
);

export const StarIcon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <path
      d="M14.2607 9.1875L14.3984 9.52832L14.7637 9.56055L20.5908 10.0811L16.1484 14.1504L15.8936 14.3838L15.9678 14.7227L17.2969 20.7148L12.3223 17.543L12 17.3379L11.6777 17.543L6.70215 20.7148L8.03223 14.7227L8.10645 14.3838L7.85156 14.1504L3.4082 10.0811L9.23633 9.56055L9.60156 9.52832L9.73926 9.1875L12 3.59961L14.2607 9.1875Z"
      fill={`var(${iconBgVariableName})`}
      stroke={borderColor}
      strokeWidth="1.2"
    />
  </svg>
);

export const EmotionLevel1Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM8.89453 14.5117C9.66016 13.8008 10.7422 13.25 12 13.25C13.2578 13.25 14.3398 13.8008 15.1055 14.5117C15.8477 15.207 16.375 16.1406 16.375 17C16.375 17.2109 16.2695 17.4062 16.0938 17.5234C15.918 17.6406 15.6953 17.6562 15.5 17.5742L14.8281 17.2812C13.9375 16.8906 12.9727 16.6914 12 16.6914C11.0273 16.6914 10.0625 16.8945 9.17188 17.2812L8.5 17.5742C8.30859 17.6602 8.08203 17.6406 7.90625 17.5234C7.73047 17.4062 7.625 17.2109 7.625 17C7.625 16.1406 8.15234 15.207 8.89453 14.5117ZM7.21484 7.73047L10.7266 9.60156C11.1445 9.82422 11.1445 10.4258 10.7266 10.6484L7.21484 12.5195C6.90625 12.6836 6.53125 12.4609 6.53125 12.1094C6.53125 12 6.57031 11.8945 6.64062 11.8125L8.04688 10.125L6.64062 8.4375C6.57031 8.35547 6.53125 8.25 6.53125 8.14062C6.53125 7.78906 6.90625 7.56641 7.21484 7.73047ZM17.4688 8.13672C17.4688 8.24609 17.4297 8.35156 17.3594 8.43359L15.9531 10.1211L17.3594 11.8086C17.4297 11.8906 17.4688 11.9961 17.4688 12.1055C17.4688 12.457 17.0938 12.6797 16.7852 12.5156L13.2734 10.6445C12.8555 10.4219 12.8555 9.82031 13.2734 9.59766L16.7852 7.72656C17.0938 7.5625 17.4688 7.78516 17.4688 8.13672Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel2Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M12 3.875C14.1549 3.875 16.2215 4.73102 17.7452 6.25476C19.269 7.77849 20.125 9.84512 20.125 12C20.125 14.1549 19.269 16.2215 17.7452 17.7452C16.2215 19.269 14.1549 20.125 12 20.125C9.84512 20.125 7.77849 19.269 6.25476 17.7452C4.73102 16.2215 3.875 14.1549 3.875 12C3.875 9.84512 4.73102 7.77849 6.25476 6.25476C7.77849 4.73102 9.84512 3.875 12 3.875ZM12 22C14.6522 22 17.1957 20.9464 19.0711 19.0711C20.9464 17.1957 22 14.6522 22 12C22 9.34784 20.9464 6.8043 19.0711 4.92893C17.1957 3.05357 14.6522 2 12 2C9.34784 2 6.8043 3.05357 4.92893 4.92893C3.05357 6.8043 2 9.34784 2 12C2 14.6522 3.05357 17.1957 4.92893 19.0711C6.8043 20.9464 9.34784 22 12 22ZM14.8281 17.3711C15.207 17.0195 15.2266 16.4258 14.875 16.0469C14.3164 15.4492 13.3516 14.8125 12 14.8125C10.6484 14.8125 9.68359 15.4492 9.12891 16.0469C8.77734 16.4258 8.79688 17.0195 9.17578 17.3711C9.55469 17.7227 10.1484 17.7031 10.5 17.3242C10.7891 17.0156 11.2812 16.6836 12.0039 16.6836C12.7266 16.6836 13.2188 17.0156 13.5078 17.3242C13.8594 17.7031 14.4531 17.7227 14.832 17.3711H14.8281ZM8.89062 12.625C9.58203 12.625 10.1406 12.0664 10.1406 11.375C10.1406 11.3164 10.1367 11.2578 10.1289 11.2031L10.5547 11.3438C10.8828 11.4531 11.2344 11.2773 11.3438 10.9492C11.4531 10.6211 11.2773 10.2695 10.9492 10.1602L7.19922 8.91016C6.87109 8.80078 6.51953 8.97656 6.41016 9.30469C6.30078 9.63281 6.47656 9.98438 6.80469 10.0938L8.00391 10.4922C7.77734 10.7188 7.64062 11.0312 7.64062 11.375C7.64062 12.0664 8.19922 12.625 8.89062 12.625ZM16.3906 11.375C16.3906 11.0273 16.25 10.7109 16.0195 10.4844L17.1992 10.0898C17.5273 9.98047 17.7031 9.625 17.5938 9.30078C17.4844 8.97656 17.1289 8.79688 16.8047 8.90625L13.0547 10.1562C12.7266 10.2656 12.5508 10.6211 12.6602 10.9453C12.7695 11.2695 13.125 11.4492 13.4492 11.3398L13.9062 11.1875C13.8984 11.2461 13.8945 11.3086 13.8945 11.3711C13.8945 12.0625 14.4531 12.6211 15.1445 12.6211C15.8359 12.6211 16.3945 12.0625 16.3945 11.3711L16.3906 11.375Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel3Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM8.82031 17.0039C8.64453 17.4922 8.10938 17.7422 7.62109 17.5664C7.13281 17.3906 6.88281 16.8555 7.05859 16.3672C7.73828 14.4766 9.76953 13.25 12 13.25C14.2305 13.25 16.2617 14.4766 16.9453 16.3711C17.1211 16.8594 16.8672 17.3945 16.3828 17.5703C15.8984 17.7461 15.3594 17.4922 15.1836 17.0078C14.8203 16.0039 13.6094 15.125 12 15.125C10.3906 15.125 9.17969 16.0039 8.82031 17.0039ZM7.64062 10.125C7.64062 9.79348 7.77232 9.47554 8.00674 9.24112C8.24116 9.0067 8.5591 8.875 8.89062 8.875C9.22215 8.875 9.54009 9.0067 9.77451 9.24112C10.0089 9.47554 10.1406 9.79348 10.1406 10.125C10.1406 10.4565 10.0089 10.7745 9.77451 11.0089C9.54009 11.2433 9.22215 11.375 8.89062 11.375C8.5591 11.375 8.24116 11.2433 8.00674 11.0089C7.77232 10.7745 7.64062 10.4565 7.64062 10.125ZM15.1406 8.875C15.4721 8.875 15.7901 9.0067 16.0245 9.24112C16.2589 9.47554 16.3906 9.79348 16.3906 10.125C16.3906 10.4565 16.2589 10.7745 16.0245 11.0089C15.7901 11.2433 15.4721 11.375 15.1406 11.375C14.8091 11.375 14.4912 11.2433 14.2567 11.0089C14.0223 10.7745 13.8906 10.4565 13.8906 10.125C13.8906 9.79348 14.0223 9.47554 14.2567 9.24112C14.4912 9.0067 14.8091 8.875 15.1406 8.875Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel4Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM9.125 16.9414C8.64062 17.1445 8.08984 16.7812 8.30078 16.3008C8.92578 14.8711 10.3477 13.875 12.0039 13.875C13.6602 13.875 15.082 14.875 15.707 16.3008C15.918 16.7812 15.3672 17.1445 14.8828 16.9414C14.0078 16.5703 13.0312 16.3633 12.0039 16.3633C10.9766 16.3633 10 16.5703 9.125 16.9414ZM7.64062 10.125C7.64062 9.79348 7.77232 9.47554 8.00674 9.24112C8.24116 9.0067 8.5591 8.875 8.89062 8.875C9.22215 8.875 9.54009 9.0067 9.77451 9.24112C10.0089 9.47554 10.1406 9.79348 10.1406 10.125C10.1406 10.4565 10.0089 10.7745 9.77451 11.0089C9.54009 11.2433 9.22215 11.375 8.89062 11.375C8.5591 11.375 8.24116 11.2433 8.00674 11.0089C7.77232 10.7745 7.64062 10.4565 7.64062 10.125ZM15.1406 8.875C15.4721 8.875 15.7901 9.0067 16.0245 9.24112C16.2589 9.47554 16.3906 9.79348 16.3906 10.125C16.3906 10.4565 16.2589 10.7745 16.0245 11.0089C15.7901 11.2433 15.4721 11.375 15.1406 11.375C14.8091 11.375 14.4912 11.2433 14.2567 11.0089C14.0223 10.7745 13.8906 10.4565 13.8906 10.125C13.8906 9.79348 14.0223 9.47554 14.2567 9.24112C14.4912 9.0067 14.8091 8.875 15.1406 8.875Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel5Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 14.1549 19.269 16.2215 17.7452 17.7452C16.2215 19.269 14.1549 20.125 12 20.125C9.84512 20.125 7.77849 19.269 6.25476 17.7452C4.73102 16.2215 3.875 14.1549 3.875 12C3.875 9.84512 4.73102 7.77849 6.25476 6.25476C7.77849 4.73102 9.84512 3.875 12 3.875C14.1549 3.875 16.2215 4.73102 17.7452 6.25476C19.269 7.77849 20.125 9.84512 20.125 12ZM12 2C9.34784 2 6.8043 3.05357 4.92893 4.92893C3.05357 6.8043 2 9.34784 2 12C2 14.6522 3.05357 17.1957 4.92893 19.0711C6.8043 20.9464 9.34784 22 12 22C14.6522 22 17.1957 20.9464 19.0711 19.0711C20.9464 17.1957 22 14.6522 22 12C22 9.34784 20.9464 6.8043 19.0711 4.92893C17.1957 3.05357 14.6522 2 12 2ZM8.89062 11.375C9.22215 11.375 9.54009 11.2433 9.77451 11.0089C10.0089 10.7745 10.1406 10.4565 10.1406 10.125C10.1406 9.79348 10.0089 9.47554 9.77451 9.24112C9.54009 9.0067 9.22215 8.875 8.89062 8.875C8.5591 8.875 8.24116 9.0067 8.00674 9.24112C7.77232 9.47554 7.64062 9.79348 7.64062 10.125C7.64062 10.4565 7.77232 10.7745 8.00674 11.0089C8.24116 11.2433 8.5591 11.375 8.89062 11.375ZM16.3906 10.125C16.3906 9.79348 16.2589 9.47554 16.0245 9.24112C15.7901 9.0067 15.4721 8.875 15.1406 8.875C14.8091 8.875 14.4912 9.0067 14.2567 9.24112C14.0223 9.47554 13.8906 9.79348 13.8906 10.125C13.8906 10.4565 14.0223 10.7745 14.2567 11.0089C14.4912 11.2433 14.8091 11.375 15.1406 11.375C15.4721 11.375 15.7901 11.2433 16.0245 11.0089C16.2589 10.7745 16.3906 10.4565 16.3906 10.125ZM9.1875 14.8125C8.66797 14.8125 8.25 15.2305 8.25 15.75C8.25 16.2695 8.66797 16.6875 9.1875 16.6875H14.8125C15.332 16.6875 15.75 16.2695 15.75 15.75C15.75 15.2305 15.332 14.8125 14.8125 14.8125H9.1875Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel6Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM8.9375 14.4258C9.53125 15.0664 10.5469 15.75 12 15.75C13.4531 15.75 14.4688 15.0664 15.0625 14.4258C15.4141 14.0469 16.0078 14.0195 16.3867 14.3711C16.7656 14.7227 16.793 15.3164 16.4414 15.6953C15.582 16.625 14.0977 17.625 12.0039 17.625C9.91016 17.625 8.42188 16.6289 7.56641 15.6953C7.21484 15.3164 7.23828 14.7227 7.62109 14.3711C8.00391 14.0195 8.59375 14.043 8.94531 14.4258H8.9375ZM7.64062 10.125C7.64062 9.79348 7.77232 9.47554 8.00674 9.24112C8.24116 9.0067 8.5591 8.875 8.89062 8.875C9.22215 8.875 9.54009 9.0067 9.77451 9.24112C10.0089 9.47554 10.1406 9.79348 10.1406 10.125C10.1406 10.4565 10.0089 10.7745 9.77451 11.0089C9.54009 11.2433 9.22215 11.375 8.89062 11.375C8.5591 11.375 8.24116 11.2433 8.00674 11.0089C7.77232 10.7745 7.64062 10.4565 7.64062 10.125ZM15.1406 8.875C15.4721 8.875 15.7901 9.0067 16.0245 9.24112C16.2589 9.47554 16.3906 9.79348 16.3906 10.125C16.3906 10.4565 16.2589 10.7745 16.0245 11.0089C15.7901 11.2433 15.4721 11.375 15.1406 11.375C14.8091 11.375 14.4912 11.2433 14.2567 11.0089C14.0223 10.7745 13.8906 10.4565 13.8906 10.125C13.8906 9.79348 14.0223 9.47554 14.2567 9.24112C14.4912 9.0067 14.8091 8.875 15.1406 8.875Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel7Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM15.6523 14.0469C16.3828 13.875 17.0547 14.5156 16.6484 15.1445C15.6875 16.6328 13.9648 17.625 11.9961 17.625C10.0273 17.625 8.30469 16.6328 7.34375 15.1445C6.9375 14.5156 7.60938 13.875 8.33984 14.0469C9.46875 14.3125 10.7031 14.457 11.9961 14.457C13.2891 14.457 14.5234 14.3125 15.6523 14.0469ZM7.64062 10.125C7.64062 9.79348 7.77232 9.47554 8.00674 9.24112C8.24116 9.0067 8.5591 8.875 8.89062 8.875C9.22215 8.875 9.54009 9.0067 9.77451 9.24112C10.0089 9.47554 10.1406 9.79348 10.1406 10.125C10.1406 10.4565 10.0089 10.7745 9.77451 11.0089C9.54009 11.2433 9.22215 11.375 8.89062 11.375C8.5591 11.375 8.24116 11.2433 8.00674 11.0089C7.77232 10.7745 7.64062 10.4565 7.64062 10.125ZM15.1406 8.875C15.4721 8.875 15.7901 9.0067 16.0245 9.24112C16.2589 9.47554 16.3906 9.79348 16.3906 10.125C16.3906 10.4565 16.2589 10.7745 16.0245 11.0089C15.7901 11.2433 15.4721 11.375 15.1406 11.375C14.8091 11.375 14.4912 11.2433 14.2567 11.0089C14.0223 10.7745 13.8906 10.4565 13.8906 10.125C13.8906 9.79348 14.0223 9.47554 14.2567 9.24112C14.4912 9.0067 14.8091 8.875 15.1406 8.875Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel8Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM7.10547 14.2617C6.94141 13.7305 7.38281 13.25 7.9375 13.25H16.2383C16.793 13.25 17.2344 13.7344 17.0703 14.2617C16.4141 16.3906 14.4297 17.9375 12.0859 17.9375C9.74219 17.9375 7.75781 16.3906 7.10547 14.2617ZM7.64062 9.5C7.64062 9.16848 7.77232 8.85054 8.00674 8.61612C8.24116 8.3817 8.5591 8.25 8.89062 8.25C9.22215 8.25 9.54009 8.3817 9.77451 8.61612C10.0089 8.85054 10.1406 9.16848 10.1406 9.5C10.1406 9.83152 10.0089 10.1495 9.77451 10.3839C9.54009 10.6183 9.22215 10.75 8.89062 10.75C8.5591 10.75 8.24116 10.6183 8.00674 10.3839C7.77232 10.1495 7.64062 9.83152 7.64062 9.5ZM15.1406 8.25C15.4721 8.25 15.7901 8.3817 16.0245 8.61612C16.2589 8.85054 16.3906 9.16848 16.3906 9.5C16.3906 9.83152 16.2589 10.1495 16.0245 10.3839C15.7901 10.6183 15.4721 10.75 15.1406 10.75C14.8091 10.75 14.4912 10.6183 14.2567 10.3839C14.0223 10.1495 13.8906 9.83152 13.8906 9.5C13.8906 9.16848 14.0223 8.85054 14.2567 8.61612C14.4912 8.3817 14.8091 8.25 15.1406 8.25Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel9Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M20.125 12C20.125 9.84512 19.269 7.77849 17.7452 6.25476C16.2215 4.73102 14.1549 3.875 12 3.875C9.84512 3.875 7.77849 4.73102 6.25476 6.25476C4.73102 7.77849 3.875 9.84512 3.875 12C3.875 14.1549 4.73102 16.2215 6.25476 17.7452C7.77849 19.269 9.84512 20.125 12 20.125C14.1549 20.125 16.2215 19.269 17.7452 17.7452C19.269 16.2215 20.125 14.1549 20.125 12ZM2 12C2 9.34784 3.05357 6.8043 4.92893 4.92893C6.8043 3.05357 9.34784 2 12 2C14.6522 2 17.1957 3.05357 19.0711 4.92893C20.9464 6.8043 22 9.34784 22 12C22 14.6522 20.9464 17.1957 19.0711 19.0711C17.1957 20.9464 14.6522 22 12 22C9.34784 22 6.8043 20.9464 4.92893 19.0711C3.05357 17.1957 2 14.6522 2 12ZM7.10547 14.2617C6.94141 13.7305 7.38281 13.25 7.9375 13.25H16.2383C16.793 13.25 17.2344 13.7344 17.0703 14.2617C16.4141 16.3906 14.4297 17.9375 12.0859 17.9375C9.74219 17.9375 7.75781 16.3906 7.10547 14.2617ZM10.5 10.9375L10.4922 10.9297C10.4844 10.9219 10.4766 10.9102 10.4648 10.8945C10.4414 10.8633 10.4023 10.8164 10.3555 10.7617C10.2578 10.6523 10.1211 10.5039 9.95703 10.3594C9.61328 10.0547 9.22266 9.8125 8.875 9.8125C8.52734 9.8125 8.13672 10.0547 7.79297 10.3594C7.62891 10.5039 7.49219 10.6523 7.39453 10.7617C7.34766 10.8164 7.30859 10.8633 7.28516 10.8945C7.27344 10.9102 7.26172 10.9219 7.25781 10.9297L7.25 10.9375C7.16797 11.0469 7.02734 11.0898 6.90234 11.0469C6.77734 11.0039 6.6875 10.8867 6.6875 10.75C6.6875 10.0508 6.94922 9.35938 7.33594 8.84375C7.71875 8.33594 8.26953 7.9375 8.875 7.9375C9.48047 7.9375 10.0312 8.33594 10.4141 8.84375C10.8008 9.35938 11.0625 10.0508 11.0625 10.75C11.0625 10.8828 10.9766 11.0039 10.8477 11.0469C10.7188 11.0898 10.5781 11.0469 10.5 10.9375ZM16.75 10.9375L16.7422 10.9297C16.7344 10.9219 16.7266 10.9102 16.7148 10.8945C16.6914 10.8633 16.6523 10.8164 16.6055 10.7617C16.5078 10.6523 16.3711 10.5039 16.207 10.3594C15.8633 10.0547 15.4727 9.8125 15.125 9.8125C14.7773 9.8125 14.3867 10.0547 14.043 10.3594C13.8789 10.5039 13.7422 10.6523 13.6445 10.7617C13.5977 10.8164 13.5586 10.8633 13.5352 10.8945C13.5234 10.9102 13.5117 10.9219 13.5078 10.9297L13.5 10.9375C13.418 11.0469 13.2773 11.0898 13.1523 11.0469C13.0273 11.0039 12.9375 10.8867 12.9375 10.75C12.9375 10.0508 13.1992 9.35938 13.5859 8.84375C13.9688 8.33594 14.5195 7.9375 15.125 7.9375C15.7305 7.9375 16.2812 8.33594 16.6641 8.84375C17.0508 9.35938 17.3125 10.0508 17.3125 10.75C17.3125 10.8828 17.2266 11.0039 17.0977 11.0469C16.9688 11.0898 16.8281 11.0469 16.75 10.9375Z"
      fill={borderColor}
    />
  </svg>
);

export const EmotionLevel10Icon = ({ style, className, width, height, backgroundColor, borderColor }: IconProps) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    style={{
      ...style,
      [iconBgVariableName]: backgroundColor,
    }}
  >
    <circle cx="12" cy="12" r="10" fill={`var(${iconBgVariableName})`} />
    <path
      d="M12 3.875C14.1549 3.875 16.2215 4.73102 17.7452 6.25476C19.269 7.77849 20.125 9.84512 20.125 12C20.125 14.1549 19.269 16.2215 17.7452 17.7452C16.2215 19.269 14.1549 20.125 12 20.125C9.84512 20.125 7.77849 19.269 6.25476 17.7452C4.73102 16.2215 3.875 14.1549 3.875 12C3.875 9.84512 4.73102 7.77849 6.25476 6.25476C7.77849 4.73102 9.84512 3.875 12 3.875ZM12 22C14.6522 22 17.1957 20.9464 19.0711 19.0711C20.9464 17.1957 22 14.6522 22 12C22 9.34784 20.9464 6.8043 19.0711 4.92893C17.1957 3.05357 14.6522 2 12 2C9.34784 2 6.8043 3.05357 4.92893 4.92893C3.05357 6.8043 2 9.34784 2 12C2 14.6522 3.05357 17.1957 4.92893 19.0711C6.8043 20.9464 9.34784 22 12 22ZM9.15625 7.17969C9.10547 7.07031 8.99609 7 8.875 7C8.75391 7 8.64453 7.07031 8.59375 7.17969L7.94531 8.53516L6.45703 8.73047C6.33594 8.74609 6.23828 8.82812 6.19922 8.94531C6.16016 9.0625 6.19531 9.1875 6.28125 9.26953L7.37109 10.3047L7.09766 11.7812C7.07422 11.8984 7.125 12.0195 7.22266 12.0898C7.32031 12.1602 7.44922 12.168 7.55469 12.1133L8.875 11.3945L10.1953 12.1094C10.3008 12.168 10.4297 12.1602 10.5273 12.0859C10.625 12.0117 10.6719 11.8945 10.6523 11.7773L10.3789 10.3008L11.4688 9.26562C11.5547 9.18359 11.5898 9.05859 11.5508 8.94141C11.5117 8.82422 11.4141 8.74219 11.293 8.72656L9.80469 8.53125L9.15625 7.17578V7.17969ZM15.4062 7.17969C15.3555 7.07031 15.2461 7 15.125 7C15.0039 7 14.8945 7.07031 14.8438 7.17969L14.1953 8.53516L12.707 8.73047C12.5859 8.74609 12.4883 8.82812 12.4492 8.94531C12.4102 9.0625 12.4453 9.1875 12.5312 9.26953L13.6211 10.3047L13.3477 11.7812C13.3242 11.8984 13.375 12.0195 13.4727 12.0898C13.5703 12.1602 13.6992 12.168 13.8047 12.1133L15.125 11.3945L16.4453 12.1094C16.5508 12.168 16.6797 12.1602 16.7773 12.0859C16.875 12.0117 16.9219 11.8945 16.9023 11.7773L16.6289 10.3008L17.7188 9.26562C17.8047 9.18359 17.8398 9.05859 17.8008 8.94141C17.7617 8.82422 17.6641 8.74219 17.543 8.72656L16.0547 8.53125L15.4062 7.17578V7.17969ZM15.6523 14.0469C14.5234 14.3125 13.2891 14.457 11.9961 14.457C10.7031 14.457 9.46875 14.3125 8.33984 14.0469C7.60938 13.875 6.9375 14.5156 7.34375 15.1445C8.30469 16.6328 10.0273 17.625 11.9961 17.625C13.9648 17.625 15.6875 16.6328 16.6484 15.1445C17.0547 14.5156 16.3828 13.875 15.6523 14.0469Z"
      fill={borderColor}
    />
  </svg>
);
