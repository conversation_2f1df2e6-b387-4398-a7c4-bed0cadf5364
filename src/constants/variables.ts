// Locales
import { GET_LIST_TYPE, PUBLIC_LEVEL } from '@antscorp/antsomi-ui/es/constants';
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const BREAK_POINT_KEYS = {
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl',
  XXL: 'xxl',
} as const;
export const LIMIT_PER_PAGE_DEFAULT = 10;
export const TEMPLATE_PREVIEW_ID = 'template-preview';

export const UNIT = {
  PX: {
    value: 'px',
    label: 'PX',
  },
  PERCENT: {
    value: '%',
    label: '%',
  },
};

export const RESPONSIVE = {
  [BREAK_POINT_KEYS.SM]: '640px',
  [BREAK_POINT_KEYS.MD]: '768px',
  [BREAK_POINT_KEYS.LG]: '1024px',
  [BREAK_POINT_KEYS.XL]: '1280px',
  [BREAK_POINT_KEYS.XXL]: '1536px',
};

export const DEVICE_TYPE = {
  DESKTOP: {
    value: 1,
    label: getTranslateMessage(translations.desktop.title),
  },
  MOBILE: {
    value: 2,
    label: getTranslateMessage(translations.mobile.title),
  },
};

export const DEVICE_PREVIEW = {
  DESKTOP: {
    value: 'desktop',
    label: 'Desktop',
    width: 1280,
    height: 720,
    device: [DEVICE_TYPE.DESKTOP.value],
  },
  GALAXY_S5: {
    value: 'galaxy_s5',
    label: 'Galaxy S5',
    width: 360,
    height: 640,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  IPHONE_5_SE: {
    value: 'iphone_5_se',
    label: 'Iphone 5/SE',
    width: 320,
    height: 568,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  IPHONE_6_7_8: {
    value: 'IPHONE_6_7_8',
    label: 'Iphone 6/7/8',
    width: 375,
    height: 667,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  IPHONE_6_7_8_PLUS: {
    value: 'iphone_6_7_8_plus',
    label: 'Iphone 6/7/8 Plus',
    width: 414,
    height: 736,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  IPHONE_X: {
    value: 'iphone_x',
    label: 'Iphone X',
    width: 375,
    height: 812,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  IPAD: {
    value: 'ipad',
    label: 'Ipad',
    width: 768,
    height: 1024,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  IPAD_PRO: {
    value: 'ipad_pro',
    label: 'Ipad Pro',
    width: 1024,
    height: 1366,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },

  SURFACE_DUO: {
    value: 'surface_duo',
    label: 'Surface Duo',
    width: 540,
    height: 720,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  GALAXY_FOLD: {
    value: 'galaxy_fold',
    label: 'Galaxy Fold',
    width: 280,
    height: 653,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
  CUSTOM: {
    value: 'custom',
    label: getTranslateMessage(translations.custom.title),
    width: 1024,
    height: 768,
    device: [DEVICE_TYPE.MOBILE.value, DEVICE_TYPE.DESKTOP.value],
  },
};

export const SCREEN_RATIO = 16 / 9;

export const ATTRIBUTE_TYPE = {
  ITEM: 2,
};

export const HASH = {
  NONE: {
    value: 'none',
    label: 'None',
  },
  MD5: {
    value: 'md5',
    label: 'MD5',
  },
  sha256: {
    value: 'sha256',
    label: 'SHA256',
  },
};

export const OPERATORS_OPTION = {
  GREATER_THAN: {
    label: 'is greater than',
    value: 'greater_than',
  },
  GREATER_THAN_EQUAL: {
    label: 'is greater than or equal',
    value: 'greater_than_equal',
  },
  LESS_THAN: {
    label: 'is less than',
    value: 'less_than',
  },
  LESS_THAN_EQUAL: {
    label: 'is less than or equal',
    value: 'less_than_equal',
  },
  NOT_EQUALS: {
    label: 'is not equal',
    value: 'not_equals',
  },
  EQUALS: {
    label: 'is equal',
    value: 'equals',
  },
  BETWEEN: {
    label: 'is between',
    value: 'between',
  },
  EXISTS: {
    label: 'exists',
    value: 'exists',
  },
  NOT_EXISTS: {
    label: 'does not exist',
    value: 'not_exists',
  },
  CONTAINS: {
    label: 'contains',
    value: 'contains',
  },
  DOESNT_CONTAIN: {
    label: 'does not contain',
    value: 'doesnt_contain',
  },
  START_WITH: {
    label: 'starts with',
    value: 'start_with',
  },
  NOT_START_WITH: {
    label: "doesn't start with",
    value: 'not_start_with',
  },
  END_WITH: {
    label: 'ends with',
    value: 'end_with',
  },
  NOT_END_WITH: {
    label: "doesn't end with",
    value: 'not_end_with',
  },
  BEFORE_DATE: {
    label: 'is before date',
    value: 'before_date',
  },
  AFTER_DATE: {
    label: 'is after date',
    value: 'after_date',
  },
  EQUAL_TIME_AGO: {
    label: 'is equal time ago',
    value: 'equal_time_ago',
  },
  NOT_EQUAL_TIME_AGO: {
    label: "isn't equal time ago",
    value: 'not_equal_time_ago',
  },
  BEFORE_TIME_AGO: {
    label: 'is before time ago',
    value: 'before_time_ago',
  },
  AFTER_TIME_AGO: {
    label: 'is after time ago',
    value: 'after_time_ago',
  },
  BETWEEN_TIME_AGO: {
    label: 'is between time ago',
    value: 'between_time_ago',
  },
  MATCHES: {
    label: 'matches any of',
    value: 'matches',
  },
  NOT_MATCHES: {
    label: "doesn't match any of",
    value: 'not_matches',
  },
  INCLUDES: {
    label: 'includes',
    value: 'includes',
  },
  DOESNT_INCLUDE: {
    label: 'does not include',
    value: 'doesnt_include',
  },
};

export const OBJECTIVE_VALUES = {
  PERSONALIZATION_CONTENT: 1,
  RECOMMENDATION: 2,
  GAMIFICATION: 3,
  LEAD_FORM: 4,
};

export const OBJECTIVE_TYPE_CONTENT = {
  [OBJECTIVE_VALUES.PERSONALIZATION_CONTENT]: 'Personalization Content',
  [OBJECTIVE_VALUES.RECOMMENDATION]: 'Recommendation',
  [OBJECTIVE_VALUES.GAMIFICATION]: 'Gamification',
  [OBJECTIVE_VALUES.LEAD_FORM]: 'Lead Form',
};

export const IMAGE_SIZE = {
  UNSET: {
    value: 'unset',
    label: getTranslateMessage(translations.unset.title),
  },
  CONTAIN: {
    value: 'contain',
    label: getTranslateMessage(translations.contain.title),
  },
  COVER: {
    value: 'cover',
    label: getTranslateMessage(translations.cover.title),
  },
};

export const POSITION = {
  LEFT_TOP: {
    value: 'left top',
    label: getTranslateMessage(translations.leftTop.title),
  },
  LEFT_CENTER: {
    value: 'left center',
    label: getTranslateMessage(translations.leftCenter.title),
  },
  LEFT_BOTTOM: {
    value: 'left bottom',
    label: getTranslateMessage(translations.leftBottom.title),
  },
  RIGHT_TOP: {
    value: 'right top',
    label: getTranslateMessage(translations.rightTop.title),
  },
  RIGHT_CENTER: {
    value: 'right center',
    label: getTranslateMessage(translations.rightCenter.title),
  },
  RIGHT_BOTTOM: {
    value: 'right bottom',
    label: getTranslateMessage(translations.rightBottom.title),
  },
  CENTER_TOP: {
    value: 'center top',
    label: getTranslateMessage(translations.centerTop.title),
  },
  CENTER_CENTER: {
    value: 'center center',
    label: getTranslateMessage(translations.centerCenter.title),
  },
  CENTER_BOTTOM: {
    value: 'center bottom',
    label: getTranslateMessage(translations.centerBottom.title),
  },
};

export const SIDE_PANEL_TABS = {
  ADVANCED: {
    name: 'advanced',
    label: getTranslateMessage(translations.advanced.title),
  },
  CONTENT: {
    name: 'content',
    label: getTranslateMessage(translations.content.title),
  },
  BASIC: {
    name: 'basic',
    label: getTranslateMessage(translations.basic.title),
  },
};

export const POST_MESSAGE_EVENT = {
  SET_TEMPLATE_DETAIL: 'setTemplateDetail',
  SET_TEMPLATE_DETAIL_SUCCESS: 'setTemplateDetailSuccess',
  STARTING_SAVE_TEMPLATE: 'startingSaveTemplate',
  SAVE_TEMPLATE: 'saveTemplate',
  CANCEL_TEMPLATE: 'cancelTemplate',
  FINISH_CAPTURE: 'finishCapture',
  TOGGLE_EXPAND_DRAWER: 'toggleExpandDrawer',
} as const;

export const MAP_DEVICE_TYPE_TO_BREAKPOINT = {
  [DEVICE_TYPE.MOBILE.value]: BREAK_POINT_KEYS.SM,
};
export const TEMPLATE_TAB_MENU_KEYS = {
  templateGallery: 'templateGallery',
  myTemplate: 'myTemplate',
  sharedWithMe: 'sharedWithMe',
} as const;
const { templateGallery, myTemplate, sharedWithMe } = TEMPLATE_TAB_MENU_KEYS;

export const TEMPLATE_TAB_MENU_ITEMS = {
  [templateGallery]: {
    key: templateGallery,
    label: 'Template Gallery',
    publicLevel: PUBLIC_LEVEL.PUBLIC,
    getListType: GET_LIST_TYPE.OWNER,
  },
  [myTemplate]: {
    key: myTemplate,
    label: 'My Template',
    publicLevel: PUBLIC_LEVEL.RESTRICTED,
    getListType: GET_LIST_TYPE.OWNER,
  },
  [sharedWithMe]: {
    key: sharedWithMe,
    label: 'Shared With Me',
    publicLevel: PUBLIC_LEVEL.RESTRICTED,
    getListType: GET_LIST_TYPE.SHARE_WITH_ME,
  },
};

export const ITEM_TYPE_ID = {
  PRODUCT: 1,
  ARTICLE: -1036,
};

// This is a list of item type IDs that are used for tracking item.
export const TRACKING_ITEM_TYPE_IDS = [ITEM_TYPE_ID.PRODUCT, ITEM_TYPE_ID.ARTICLE];
