{"messageError": {"createColorProfile": {"message": "<PERSON><PERSON><PERSON> hồ sơ màu không thành công", "description": ""}, "maxLength": {"message": "{{name}} qu<PERSON> dài, độ dài tối đa là {{maxLength}} ký tự", "description": ""}, "promotionPoolDeactivated": {"message": "<PERSON><PERSON><PERSON><PERSON> khu<PERSON>ến mãi này đã bị vô hiệu hóa", "description": ""}, "BOArchive": {"message": "BO này đang lưu trữ", "description": ""}, "BODelete": {"message": "BO này không tồn tại", "description": ""}, "attributeArchive": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h này không có sẵn", "description": ""}, "attributeDelete": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h này không tồn tại", "description": ""}, "collectionArchive": {"message": "Collection này không có sẵn", "description": ""}, "collectionDisable": {"message": "Collection này bị vô hiệu hóa", "description": ""}, "collectionDelete": {"message": "Collection này không tồn tại", "description": ""}, "blockError": {"message": "<PERSON><PERSON> một số block đang bị lỗi. <PERSON><PERSON> lòng kiểm tra lại", "description": ""}, "boError": {"message": "Content sources đang bị lỗi. <PERSON><PERSON> lòng kiểm tra lại", "description": ""}, "fieldIsRequired": {"message": "This field is required", "description": ""}, "completeSetupInContentSources": {"message": "Please complete the setup in Content sources", "description": ""}, "fieldEmpty": {"message": "Trư<PERSON><PERSON> này không được để trống"}, "nameExisted": {"message": "Tên này đã tồn tại"}}, "excluded": {"title": "Lo<PERSON><PERSON> trừ", "description": ""}, "useTemplate": {"title": "Sử dụng mẫu", "description": ""}, "all": {"title": "<PERSON><PERSON><PERSON> c<PERSON>", "description": ""}, "noTemplatesFound": {"title": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mẫu", "description": ""}, "switchMobileMode": {"title": "<PERSON><PERSON><PERSON><PERSON> sang chế độ di động", "description": ""}, "switchDesktopMode": {"title": "<PERSON><PERSON><PERSON><PERSON> sang chế độ máy tính", "description": ""}, "deviceType": {"title": "<PERSON><PERSON><PERSON> bị", "description": ""}, "saveAs": {"title": "Save as...", "fullTitle": "Save as my template", "myTemplate": "My template", "galleryTemplate": "Template gallery", "newRichMenu": "Save as a new rich menu Template", "existingRichMenu": "Save as an existing rich menu Template", "description": "Describe your rich menu template"}, "saveAsGallery": {"title": "<PERSON><PERSON><PERSON> dạng", "saveAsNewGallery": {"title": "<PERSON><PERSON><PERSON> dưới dạng Mẫu Thư viện mới", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên mẫu thư viện", "success": "<PERSON><PERSON><PERSON> làm thư viện mẫu thành công", "error": "<PERSON><PERSON><PERSON> làm thư viện mẫu thất bại"}, "saveAsExistingGallery": {"title": "<PERSON><PERSON><PERSON> dư<PERSON> dạng Mẫu Thư viện hiện có", "placeholder": "<PERSON><PERSON><PERSON> mẫu thư viện", "success": "<PERSON><PERSON><PERSON> dư<PERSON> dạng Mẫu Thư viện hiện có thành công", "error": "<PERSON><PERSON><PERSON> dư<PERSON>i dạng Mẫu Thư viện hiện có thất bại"}}, "templateSave": {"templateGallery": "Template Gallery", "saveTemplateGallery": "Save as template gallery"}, "cloneMediaTemplate": {"saveAs": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "saveAsNewTemplate": {"title": "Save as new Media Template", "placeholder": "Enter template name", "description": ""}, "saveTemplateSuccess": {"title": "Save as Media template success", "description": ""}, "saveTemplateFail": {"title": "Save as Media template fail", "description": ""}, "cloneTemplate": {"title": "<PERSON><PERSON> Template", "description": ""}, "cloneTemplateSuccess": {"title": "Clone Template success", "description": ""}, "cloneTemplateFailed": {"title": "Clone Template fail", "description": ""}}, "cloneMediaJson": {"saveAs": {"title": "<PERSON><PERSON><PERSON> dạng", "description": ""}, "saveAsNewTemplate": {"title": "<PERSON><PERSON><PERSON> dưới dạng mẫu Media json mới", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên mẫu", "description": ""}, "saveTemplateSuccess": {"title": "<PERSON><PERSON><PERSON> d<PERSON> dạng mẫu media json thành công", "description": ""}, "saveTemplateFail": {"title": "<PERSON><PERSON><PERSON> d<PERSON> dạng mẫu media json thất bại", "description": ""}, "cloneTemplate": {"title": "Sao chép mẫu", "description": ""}, "cloneTemplateSuccess": {"title": "<PERSON>o chép mẫu thành công", "description": ""}, "cloneTemplateFailed": {"title": "<PERSON>o chép mẫu thất bại", "description": ""}}, "desktop": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "mobile": {"title": "<PERSON>", "description": "", "warning": "Bạn hiện đang chỉnh sửa cho thiết bị Di động. <PERSON><PERSON><PERSON><PERSON> sang <PERSON><PERSON><PERSON> tính cho tất cả các tùy chọn cài đặt."}, "table": {"pagination": {"jumper": "<PERSON><PERSON> đ<PERSON>n trang", "sizeChanger": "<PERSON><PERSON><PERSON> thị các dòng"}, "rowSelected": {"title": "{{numRows}} <PERSON><PERSON><PERSON><PERSON>"}}, "selectDate": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "searchForIcon": {"title": "<PERSON><PERSON><PERSON> kiếm biểu tư<PERSON>", "description": ""}, "standards": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "ok": {"title": "OK", "description": ""}, "height": {"title": "<PERSON><PERSON><PERSON> cao", "description": ""}, "width": {"title": "<PERSON><PERSON><PERSON> r<PERSON>"}, "numberStyling": {"title": "<PERSON><PERSON><PERSON> k<PERSON> số", "description": ""}, "unitStyling": {"title": "<PERSON><PERSON><PERSON> ki<PERSON>u đơn vị", "description": ""}, "minimumWidth": {"title": "<PERSON><PERSON><PERSON> rộng tối thiểu", "description": ""}, "countdownStyling": {"title": "<PERSON><PERSON><PERSON> ki<PERSON>u đếm <PERSON>", "description": ""}, "endDate": {"title": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "description": ""}, "endTime": {"title": "<PERSON><PERSON><PERSON> kết thúc", "description": ""}, "noAction": {"title": "<PERSON><PERSON><PERSON><PERSON> có hành động nào", "description": ""}, "restartCountdown": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>u lại", "description": ""}, "unitDisplayType": {"title": "<PERSON><PERSON><PERSON> hi<PERSON>n thị", "description": ""}, "unitDisplayPosition": {"title": "<PERSON><PERSON> trí hiển thị", "description": ""}, "displayDays": {"title": "<PERSON><PERSON><PERSON> thị ng<PERSON>y", "description": ""}, "displayHours": {"title": "<PERSON><PERSON><PERSON> thị giờ", "description": ""}, "displayMinutes": {"title": "<PERSON><PERSON><PERSON> thị ph<PERSON>t", "description": ""}, "displaySeconds": {"title": "<PERSON><PERSON><PERSON> thị gi<PERSON>y", "description": ""}, "unitDisplaySettings": {"title": "Cài đặt hiển thị", "description": ""}, "hours": {"title": "Giờ", "label": "<PERSON>hãn giờ", "description": ""}, "minutes": {"title": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "description": ""}, "seconds": {"title": "Giây", "label": "<PERSON><PERSON>ã<PERSON> gi<PERSON>y", "unit": "giây", "description": ""}, "overline": {"title": "<PERSON><PERSON><PERSON> trên", "description": ""}, "lineThrough": {"title": "<PERSON><PERSON><PERSON> ng<PERSON>", "description": ""}, "underline": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "capitalize": {"title": "In hoa tiền tố"}, "uppercase": {"title": "In hoa", "description": ""}, "lowercase": {"title": "In thường", "description": ""}, "fontFamily": {"title": "Phông chữ", "description": ""}, "fontSettings": {"title": "Cài đặt chữ", "description": ""}, "opacity": {"title": "<PERSON><PERSON> trong suốt", "description": ""}, "imageStyling": {"title": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>", "description": ""}, "altText": {"title": "<PERSON><PERSON><PERSON> bản thay thế", "description": ""}, "uploadedAt": {"title": "<PERSON><PERSON> tải lên: {{date}} lúc {{time}}", "description": ""}, "searchImage": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "clickToAddColumn": {"title": "<PERSON><PERSON>ấn để bắt đầu thêm các Block Cột", "description": ""}, "searchBlock": {"title": "Tìm kiếm block...", "description": ""}, "column": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "row": {"title": "Dòng", "description": ""}, "text": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "image": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "imageURL": {"title": "URL <PERSON>nh", "placeholder": "<PERSON>hập <PERSON>"}, "clickToAddImage": {"title": "<PERSON><PERSON><PERSON><PERSON> để thêm <PERSON>nh", "description": ""}, "deleteImage": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa ảnh \"{{name}}\" này không"}, "button": {"title": "<PERSON><PERSON><PERSON>", "size": {"xSmall": "X nhỏ", "small": "Nhỏ", "medium": "<PERSON>rung bình", "large": "Lớn", "xLarge": "X lớn", "x2Large": "X2 lớn"}, "description": ""}, "optinFields": {"title": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "description": "", "fields": {"firstNameField": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tên riêng", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên riêng của bạn ở đây..."}, "lastNameField": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tên họ", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên họ của bạn ở đây..."}, "nameField": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tên", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên của bạn ở đây..."}, "emailField": {"title": "Trường email", "placeholder": "Nhập email của bạn ở đây..."}, "phoneField": {"title": "<PERSON>r<PERSON><PERSON><PERSON> số điện thoại", "placeholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại của bạn ở đây..."}, "privacyNotice": {"title": "<PERSON><PERSON><PERSON><PERSON> báo về quyền riêng tư"}, "inputField": {"title": "Input", "placeholder": "Enter your text"}, "inputAreaField": {"title": "Text Area", "placeholder": "Enter your text area"}, "numberField": {"title": "Number", "placeholder": "Enter your number"}, "radioButton": {"title": "Radio Button", "placeholder": "Enter your number"}, "checkbox": {"title": "Checkbox", "placeholder": ""}, "rating": {"title": "Đánh giá", "name": "Đánh giá", "label": "Đánh giá", "placeholder": "", "emotion": {"extremelyDissatisfied": "Cực kỳ không hài lòng", "veryDissatisfied": "<PERSON><PERSON><PERSON> kh<PERSON>ng hài lòng", "dissatisfied": "<PERSON><PERSON><PERSON><PERSON> hài lòng", "slightlyDissatisfied": "<PERSON><PERSON><PERSON> không hài lòng", "neutral": "<PERSON><PERSON><PERSON>", "slightlySatisfied": "<PERSON><PERSON><PERSON> hài lòng", "satisfied": "<PERSON><PERSON><PERSON> l<PERSON>", "verySatisfied": "<PERSON><PERSON><PERSON> hài lòng", "extremelySatisfied": "<PERSON><PERSON><PERSON> kỳ hài lòng", "delighted": "<PERSON><PERSON> v<PERSON> mong đợi"}, "star": {"poor": "<PERSON>ệ", "fair": "<PERSON>rung bình", "good": "<PERSON><PERSON><PERSON>", "veryGood": "<PERSON><PERSON><PERSON> t<PERSON>", "excellent": "<PERSON><PERSON><PERSON>"}, "yesno": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>"}}}}, "cappingLevel": {"title": "Capping level", "description": "", "level": {"variant": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "campaign": {"title": "Campaign", "description": ""}, "journey": {"title": "Journey", "description": ""}}}, "hour": {"title": "giờ", "description": ""}, "day": {"title": "ng<PERSON>y", "description": ""}, "week": {"title": "this week", "description": ""}, "month": {"title": "th<PERSON>g", "description": ""}, "lifeTime": {"title": "lifetime", "description": ""}, "yesNo": {"title": "Yes/No", "description": ""}, "countDown": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "video": {"title": "Video", "description": ""}, "couponWheel": {"title": "Coupon wheel", "description": ""}, "couponWheelSectionError": {"title": "Vòng quay phiếu thưởng có phần cài đặt không hợp lệ", "internalCode": {"invalid": "<PERSON><PERSON> n<PERSON>i bộ không hợp lệ", "same": "Cùng một mã nội bộ tồn tại"}, "limitSpinning": {"invalid": "G<PERSON><PERSON> trị giới hạn quay không hợp lệ"}, "couponCode": {"required": "<PERSON><PERSON> phiếu gi<PERSON>m là bắ<PERSON> bu<PERSON>c"}, "couponCodeAttr": {"required": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h khu<PERSON>ến mãi là bắt buộc"}}, "limitSpinning": {"title": "Giới hạn quay", "description": ""}, "limitRandom": {"title": "<PERSON><PERSON>", "description": ""}, "frequency": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "spacer": {"title": "Spacer", "info": "<PERSON><PERSON>u cao khối sẽ luôn ở mức tối thiểu là 30px. Bạn có thể sử dụng lề hoặc đệm trong Tạo kiểu vùng chứa của một khối để có khoảng cách giữa các Cột hoặc Khối nhỏ hơn.", "description": ""}, "divider": {"title": "Đường phân cách", "description": ""}, "group": {"title": "Group", "description": ""}, "addGroup": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "groupName": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "icon": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "textEditor": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> văn bản của bạn ở đây", "toolbar": {"backgroundColor": {"tooltip": "<PERSON><PERSON><PERSON>"}, "bold": {"tooltip": "In đậm"}, "clean": {"tooltip": "<PERSON><PERSON><PERSON> đ<PERSON> dạng"}, "fontFamily": {"tooltip": "<PERSON><PERSON><PERSON> chữ"}, "fontSize": {"tooltip": "Cỡ chữ"}, "fontWeight": {"tooltip": "<PERSON><PERSON> đậm"}, "indentDecrease": {"tooltip": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "indentIncrease": {"tooltip": "<PERSON><PERSON><PERSON> th<PERSON>t l<PERSON>"}, "italic": {"tooltip": "In nghiêng"}, "letterSpacing": {"tooltip": "<PERSON><PERSON><PERSON><PERSON> cách gi<PERSON>a các chữ cái"}, "lineHeight": {"tooltip": "<PERSON><PERSON><PERSON> cao gi<PERSON>a các dòng", "options": {"default": "Mặc định", "double": "<PERSON><PERSON><PERSON>", "single": "Đơn"}}, "link": {"tooltip": "<PERSON><PERSON><PERSON> đ<PERSON>ờng dẫn"}, "linkEdit": {"tooltip": "<PERSON><PERSON>a đường dẫn"}, "linkOpen": {"tooltip": "Mở đường dẫn"}, "linkRemove": {"tooltip": "Gỡ đường dẫn"}, "listBullet": {"tooltip": "<PERSON><PERSON> s<PERSON>ch không theo thứ tự"}, "listOrdered": {"tooltip": "<PERSON><PERSON> s<PERSON>ch theo thứ tự"}, "mergeTags": {"tooltip": "Thẻ thông minh"}, "redo": {"tooltip": "Redo"}, "selectAll": {"tooltip": "<PERSON><PERSON><PERSON> tất cả"}, "strike": {"tooltip": "<PERSON><PERSON><PERSON> ng<PERSON>"}, "subscript": {"tooltip": "Chỉ số dưới"}, "superscript": {"tooltip": "Chỉ số trên"}, "textAlign": {"tooltip": "Canh chỉnh", "options": {"left": "<PERSON>h trái", "center": "<PERSON><PERSON> g<PERSON>", "right": "<PERSON><PERSON> ph<PERSON>i", "justify": "<PERSON><PERSON> đ<PERSON>u"}}, "textColor": {"tooltip": "<PERSON><PERSON><PERSON> chữ"}, "textTransform": {"tooltip": "<PERSON>y<PERSON>n đổi văn bản", "options": {"capitalize": "<PERSON><PERSON><PERSON><PERSON> hoa", "lowercase": "chữ thường", "none": "kh<PERSON>ng có", "uppercase": "CHỮ VIẾT HOA"}}, "underline": {"tooltip": "<PERSON><PERSON><PERSON>"}, "undo": {"tooltip": "Undo"}}}, "html": {"title": "HTML", "description": ""}, "settings": {"title": "Cài đặt", "description": ""}, "blocks": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "allBlocks": {"title": "Tất cả block", "description": ""}, "page": {"title": "<PERSON><PERSON>"}, "pages": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "cancel": {"title": "Hủy bỏ"}, "confirm": {"title": "<PERSON><PERSON><PERSON>"}, "save": {"title": "<PERSON><PERSON><PERSON>"}, "popUp": {"title": "Popup", "description": ""}, "floatingBar": {"title": "Floating Bar", "description": ""}, "fullscreen": {"title": "Fullscreen", "description": ""}, "inline": {"title": "Inline", "description": ""}, "slideIn": {"title": "Slide-in", "description": ""}, "gamified": {"title": "Gamified", "description": ""}, "dragBlockHere": {"title": "<PERSON><PERSON><PERSON> khối vào đây", "description": ""}, "basic": {"title": "<PERSON><PERSON> bản"}, "advanced": {"title": "<PERSON><PERSON><PERSON> cao", "description": ""}, "optinViewStyling": {"title": "Optin View Styling", "description": ""}, "displaySettings": {"title": "Cài đặt hiển thị", "description": ""}, "mode": {"title": "<PERSON><PERSON> độ", "description": ""}, "trackingModule": {"title": "Tracking Module", "description": "", "inner": {"source": {"title": "{{prefix}}_Source", "placeholder": "Enter text here..."}, "medium": {"title": "{{prefix}}_Medium", "placeholder": "Enter URL here..."}, "campaign": {"title": "{{prefix}}_Campaign", "placeholder": "Enter text here..."}, "term": {"title": "{{prefix}}_Term", "placeholder": "Enter URL here..."}, "content": {"title": "{{prefix}}_Content", "placeholder": "Enter text here..."}}}, "templateSettings": {"title": "Cài đặt {{template}}", "description": ""}, "displayStyling": {"title": "Cài đặt hiển thị", "description": ""}, "fullscreenSettings": {"title": "Cài đặt Fullscreen", "description": ""}, "expandEditor": {"title": "Mở rộng trình chỉnh sửa", "description": ""}, "color": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "boxShadow": {"title": "Đỗ bóng", "description": ""}, "shadowStyle": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> bóng", "description": ""}, "position": {"title": "<PERSON><PERSON> trí", "description": ""}, "content": {"title": "<PERSON><PERSON>i dung", "description": ""}, "sectionStyling": {"title": "<PERSON><PERSON><PERSON> ki<PERSON>u vùng", "description": ""}, "columnStyling": {"title": "<PERSON><PERSON><PERSON> c<PERSON> {{index}}", "description": ""}, "zIndex": {"title": "Z-Index", "description": "", "warning": "<strong><PERSON><PERSON><PERSON> báo:</strong> V<PERSON><PERSON><PERSON> thay đổi các giá trị z-index có thể gây ra sự cố khi tương tác với các công cụ của trình tạo."}, "customIdAttribute": {"title": "T<PERSON>y chỉnh ID thuộc tính", "description": "", "placeholder": "T<PERSON>y chỉnh ID thuộc tính"}, "customClassAttribute": {"title": "Tùy chỉnh Class thuộc tính", "description": "", "placeholder": "Tùy chỉnh Class thuộc tính"}, "toUrl": {"title": "Chuyển hướng đến một đường dẫn"}, "none": {"title": "<PERSON><PERSON><PERSON><PERSON> có"}, "light": {"title": "Nhẹ"}, "custom": {"title": "<PERSON><PERSON><PERSON> chỉnh"}, "outside": {"title": "Ở ngoài"}, "hidden": {"title": "Ẩn"}, "dotted": {"title": "Dotted"}, "dashed": {"title": "Dashed"}, "solid": {"title": "Solid"}, "double": {"title": "Double"}, "groove": {"title": "Groove"}, "ridge": {"title": "Ridge"}, "inset": {"title": "Inset"}, "outset": {"title": "Outset"}, "inside": {"title": "<PERSON><PERSON> trong"}, "borderStyle": {"title": "<PERSON><PERSON><PERSON> viền"}, "border": {"title": "<PERSON><PERSON><PERSON><PERSON> viền"}, "borderColor": {"title": "<PERSON><PERSON><PERSON> v<PERSON>"}, "borderWidth": {"title": "<PERSON><PERSON><PERSON> rộng đườ<PERSON> viền"}, "stretch": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>", "description": ""}, "start": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>u", "description": ""}, "spaceAround": {"title": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>ng xung quanh", "description": ""}, "spaceBetween": {"title": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>ng g<PERSON>a", "description": ""}, "end": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "baseline": {"title": "Đ<PERSON><PERSON><PERSON> gốc", "description": ""}, "top": {"title": "<PERSON><PERSON> trên"}, "right": {"title": "<PERSON><PERSON><PERSON>"}, "center": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "bottom": {"title": "<PERSON><PERSON><PERSON>"}, "left": {"title": "<PERSON><PERSON><PERSON> t<PERSON>"}, "byField": {"title": "<PERSON> t<PERSON>"}, "cornerStyle": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "roundedCorners": {"title": "<PERSON><PERSON><PERSON> tròn", "description": ""}, "roundCorner": {"title": "<PERSON><PERSON><PERSON> tròn", "description": ""}, "topLeft": {"title": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "description": ""}, "topRight": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "bottomRight": {"title": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "description": ""}, "bottomLeft": {"title": "Dư<PERSON>i trái", "description": ""}, "slightlyRound": {"title": "Slightly round", "description": ""}, "capsuleRound": {"title": "Capsule round", "description": ""}, "spacing": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "description": ""}, "innerSpacing": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch bên trong (Padding)", "description": ""}, "outerSpacing": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch bên <PERSON> (Margin)", "description": "", "warning": "<strong><PERSON><PERSON><PERSON> báo:</strong> V<PERSON><PERSON><PERSON> thay đổi các giá trị z-index có thể gây ra sự cố khi tương tác với các công cụ của trình tạo."}, "gradient": {"title": "<PERSON><PERSON><PERSON><PERSON> màu", "description": ""}, "backgroundColor": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "background": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "linear": {"title": "Linear", "description": ""}, "radial": {"title": "Radial", "description": ""}, "gradientStyle": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> màu", "description": ""}, "angle": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "gradientColor": {"title": "<PERSON><PERSON><PERSON> {{name}}", "description": ""}, "gradientLocation": {"title": "<PERSON><PERSON> trí màu {{name}}", "description": ""}, "firstColor": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên", "description": ""}, "secondColor": {"title": "<PERSON><PERSON><PERSON> thứ hai", "description": ""}, "firstColorLocation": {"title": "<PERSON><PERSON> trí màu đầu tiên", "description": ""}, "secondColorLocation": {"title": "<PERSON><PERSON> trí màu thứ hai", "description": ""}, "leftTop": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "leftCenter": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "leftBottom": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "rightTop": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "rightCenter": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "rightBottom": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "centerTop": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "centerCenter": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "centerBottom": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "style": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "iconSelection": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "imageIconSelection": {"title": "Image/Icon Selection", "description": ""}, "change": {"title": "<PERSON><PERSON> đ<PERSON>i", "description": ""}, "USE": {"title": "SỬ DỤNG", "description": ""}, "browseImage": {"title": "<PERSON><PERSON><PERSON>", "description": "", "warning": "<PERSON>hông có ảnh được chọn trong thẻ ảnh. <PERSON><PERSON>u bạn không muốn hiện ảnh ở đây bạn có thể", "asWell": ""}, "deleteImageElement": {"title": "xóa thẻ ảnh này.", "description": ""}, "imageSelection": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "selectImageFromComputer": {"title": "<PERSON><PERSON><PERSON>nh từ máy tính", "description": ""}, "dragDropFileHere": {"title": "Kéo và thả tập tin vào đây", "description": ""}, "or": {"title": "hoặc", "description": ""}, "columns": {"title": "<PERSON><PERSON><PERSON>", "description": "", "validate": "Tổng chiều rộng cột (bao gồm cả khoảng cách cột) là <strong> {{totalColumnWidth}}% </strong>, lớn hơn 100%. Đi<PERSON>u này có thể dẫn đến khoảng cách cột biến mất hoặc các kết quả không mong muốn khác."}, "columnWidth": {"title": "<PERSON><PERSON><PERSON> rộng cột {{index}} (%)", "description": ""}, "columnGap": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch cột (%)", "description": ""}, "buttonText": {"title": "<PERSON>ữ trên n<PERSON>t", "description": "", "placeholder": "<PERSON><PERSON><PERSON><PERSON> chữ ở đây..."}, "buttonClickAction": {"title": "<PERSON><PERSON><PERSON> động khi nhấn nút", "description": ""}, "goView": {"title": "<PERSON><PERSON> đến màn hình"}, "with": {"title": "V<PERSON><PERSON>"}, "buttonSize": {"title": "<PERSON><PERSON><PERSON>"}, "defaultHover": {"title": "Mặc định kiểu hover"}, "hoverStyles": {"title": "<PERSON><PERSON><PERSON> khi hover"}, "defaultFormErrorStyles": {"title": "Mặc định kiểu lỗi form"}, "formErrorStyling": {"title": "Ki<PERSON>u Lỗi Form"}, "errorPosition": {"title": "<PERSON>ị trí lỗi"}, "fontColor": {"title": "<PERSON><PERSON><PERSON> chữ"}, "globalViewStyling": {"title": "Global View Styling", "description": ""}, "campaignWidth": {"title": "<PERSON><PERSON><PERSON> rộng chi<PERSON>n d<PERSON>ch", "description": ""}, "setImagesLayerFirst": {"title": "<PERSON><PERSON><PERSON> thị lớp <PERSON>nh trư<PERSON>c tiên", "description": ""}, "setImageLazyLoad": {"title": "Lazy load hình <PERSON>nh", "description": ""}, "hideContainerOverflow": {"title": "Ẩn phần tràn của vùng", "description": ""}, "displayYesNoView": {"title": "<PERSON><PERSON><PERSON> thị màn hình Yes/No", "description": ""}, "campaignHasBeenClosed": {"title": "If this campaign <strong>has been closed</strong> but not converted, show it again", "description": ""}, "campaignHasBeenConverted": {"title": "If this campaign <strong>has been converted</strong>, show it again", "description": ""}, "howLowBrowserWillRemember": {"title": "How long the browser will remember a visitor who has seen this campaign.", "description": ""}, "crossSubdomainCookie": {"title": "Cross Subdomain Cookie", "description": ""}, "days": {"title": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>", "description": ""}, "displayPageSlide": {"title": "Display a page slide", "description": ""}, "closeBackgroundClick": {"title": "<PERSON><PERSON><PERSON> màn hình khi nhấn vào nền ", "description": ""}, "automaticallyClose": {"title": "Automatically close", "description": ""}, "delayShowing": {"title": "Delay showing", "description": ""}, "conditions": {"title": "Conditions", "description": ""}, "percentScroll": {"title": "Percented scroll", "description": ""}, "viewPage": {"title": "After viewing page", "description": ""}, "percent": {"title": "%", "description": ""}, "second": {"title": "second", "description": ""}, "timeToClose": {"title": "Time to close (second)", "description": ""}, "closeTemplate": {"title": "Đóng template", "description": ""}, "buttonStyling": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "xColor": {"title": "Màu X", "description": ""}, "circle": {"title": "Tròn", "description": ""}, "square": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "buttonStyle": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "displayCloseButton": {"title": "<PERSON><PERSON><PERSON> thị nút đóng", "description": ""}, "openViewStyling": {"title": "Open View Styling", "description": ""}, "customColors": {"title": "<PERSON><PERSON><PERSON> s<PERSON>c tùy chỉnh", "description": ""}, "customCSS": {"title": "CSS tùy chỉnh", "description": "Mỗi câu lệnh CSS tùy chỉnh của bạn phải nằm trên một dòng riêng và có tiền tố là"}, "hoverXColor": {"title": "Màu của X khi di chuột", "description": ""}, "hoverBackgroundColor": {"title": "<PERSON><PERSON><PERSON> khi di chu<PERSON>t", "description": ""}, "small": {"title": "Nhỏ", "description": ""}, "medium": {"title": "Vừa", "description": ""}, "large": {"title": "Lớn", "description": ""}, "extraLarge": {"title": "<PERSON><PERSON><PERSON> l<PERSON>n", "description": ""}, "buttonPosition": {"title": "<PERSON><PERSON> trí n<PERSON>t", "description": ""}, "xSize": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "xThickness": {"title": "Độ dày X", "description": ""}, "thin": {"title": "Mỏng", "description": ""}, "thick": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "campaignNamespace": {"title": "Campaign Namespace", "description": ""}, "displayMediaTemplateBadge": {"title": "Display MediaTemplate badge", "description": ""}, "enableWebFonts": {"title": "Enable web fonts", "description": ""}, "setImageLayerFirst": {"title": "<PERSON><PERSON><PERSON> thị lớp <PERSON>nh trư<PERSON>c tiên", "description": ""}, "sortByUploadDate": {"title": "<PERSON><PERSON><PERSON> xế<PERSON> theo ng<PERSON>y tả<PERSON> lên", "description": ""}, "sortBySize": {"title": "<PERSON><PERSON><PERSON> xếp theo k<PERSON>ch th<PERSON>", "description": ""}, "size": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "icons": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "iconColor": {"title": "<PERSON><PERSON><PERSON> bi<PERSON> t<PERSON>", "description": ""}, "displayCondition": {"title": "Display condition", "condition": {"title": "Condition"}, "field": {"title": "Field"}, "addField": {"title": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>"}, "index": {"title": "Index"}, "operator": {"title": "Operator"}, "value": {"title": "Value"}, "options": {"showWhen": "Show when", "hiddenWhen": "Hidden when", "showAllColumnsWhen": "Show All Columns when", "hideAllColumnsWhen": "Hide All Columns when", "showSlideshowWhen": "Show Slideshow when", "hiddenSlideshowWhen": "Hidden Slideshow when", "showColumnWhen": "Show Column when", "hiddenColumnWhen": "Hidden Column when", "showSlideWhen": "Show Slide when", "hiddenSlideWhen": "Hidden Slide when"}, "description": ""}, "align": {"title": "Canh chỉnh", "description": ""}, "link": {"title": "Đường dẫn", "description": ""}, "openInNewWindow": {"title": "Mở trong cửa sổ mới", "description": ""}, "addNoFollow": {"title": "<PERSON><PERSON><PERSON><PERSON> \"No Follow\"", "description": ""}, "trackClicks": {"title": "Track Clicks", "description": ""}, "containerStyling": {"title": "<PERSON><PERSON><PERSON> v<PERSON>ng", "description": ""}, "iconStyling": {"title": "<PERSON><PERSON><PERSON> kiểu biểu tư<PERSON>", "description": ""}, "backgroundSpacing": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch với n<PERSON>n", "description": ""}, "iconSize": {"title": "<PERSON><PERSON><PERSON><PERSON> (px)", "description": ""}, "spacingPx": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> (px)", "description": ""}, "displayIconAfterText": {"title": "Hiển thị biểu tượng sau văn bản?", "description": ""}, "enterUrlHere": {"title": "<PERSON><PERSON><PERSON><PERSON> đường dẫn ở đây...", "description": ""}, "selectAnIcon": {"title": "<PERSON><PERSON><PERSON> bi<PERSON> t<PERSON>", "description": ""}, "SelectAnIconOrAnImage": {"title": "Select an icon/image", "description": ""}, "containerStyle": {"title": "<PERSON><PERSON><PERSON> v<PERSON>", "description": ""}, "groupStyle": {"title": "Group Styling", "description": ""}, "titleCountDown": {"title": "Countdown", "description": ""}, "static": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "dynamic": {"title": "Động", "description": ""}, "timezone": {"title": "<PERSON><PERSON><PERSON> giờ", "description": ""}, "countdownEndAction": {"title": "<PERSON><PERSON><PERSON> động khi kết thúc countdown", "description": ""}, "goToView": {"title": "<PERSON><PERSON> đến màn hình", "description": ""}, "titleSuccessScript": {"title": "<PERSON><PERSON><PERSON> l<PERSON>nh khi thành công", "description": ""}, "titleLimitedSubmit": {"title": "Giới hạn số lần Submit", "description": "", "type": {"unlimited": {"title": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "description": ""}, "limited": {"title": "<PERSON><PERSON><PERSON><PERSON> hạn", "description": ""}}}, "limMessPosition": {"title": "<PERSON><PERSON> trí tin nh<PERSON>n", "description": ""}, "time": {"title": "<PERSON><PERSON><PERSON> trên", "description": ""}, "per": {"title": "trên", "description": ""}, "descSuccessScript": {"title": "<PERSON><PERSON><PERSON> tập lệnh sẽ tải sau khi kết thúc countdown.", "description": ""}, "conversionTracking": {"title": "Conversion tracking?", "description": ""}, "closeCampaign": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "description": ""}, "widthPercent": {"title": "<PERSON><PERSON><PERSON> r<PERSON> (%)", "description": ""}, "inputWidthPercent": {"title": "Input Width (%)", "description": ""}, "Placeholder": {"title": "Placeholder", "description": ""}, "fieldName": {"title": "Field Name", "description": "", "placeholder": "Enter field name"}, "fieldLabel": {"title": "Field Label", "description": "", "placeholder": "Enter field label"}, "useCountryCode": {"title": "Use Country Code?", "description": ""}, "countryCodeDefault": {"title": "Country Code Default", "description": ""}, "privacyContent": {"title": "Privacy Content", "description": "", "placeholder": "Enter privacy content"}, "indentation": {"title": "Indentation (px)", "description": ""}, "displayLabel": {"title": "Display Label?", "description": ""}, "rangeNumber": {"title": "Range Number?", "description": ""}, "displayLabels": {"title": "Display Labels?", "description": ""}, "fieldAlignment": {"title": "Field Alignment", "description": ""}, "isNameAlready": {"title": "This name already exist", "description": ""}, "enterYourEmailAddress": {"title": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email của bạn", "description": ""}, "fieldID": {"title": "ID của trường", "description": ""}, "minValue": {"title": "Min Value", "description": ""}, "maxValue": {"title": "Max Value", "description": ""}, "optionColumn": {"title": "Option Column", "description": ""}, "fieldOptions": {"title": "Field Options", "description": ""}, "fieldOptionHead": {"value": {"title": "<PERSON><PERSON><PERSON> trị", "description": ""}, "label": {"title": "<PERSON><PERSON>ã<PERSON>", "description": ""}, "preselect": {"title": "<PERSON><PERSON><PERSON> t<PERSON>", "description": ""}, "goToView": {"title": "<PERSON><PERSON><PERSON><PERSON> đến chế độ xem", "description": ""}, "removeAll": {"title": "<PERSON><PERSON><PERSON> tất cả", "description": ""}}, "addAnOption": {"title": "Add an option"}, "extendValues": {"title": "Extend values", "placeholder": "You can extend values by input or paste values, separated by enter"}, "optionLabelGap": {"title": "Option Label Gap (px)"}, "lineHeightGap": {"title": "Line Height Gap (px)"}, "columnSpacing": {"title": "Column Spacing (px)"}, "optionPosition": {"title": "Option Position", "top": {"title": "Top"}, "right": {"title": "Right"}, "left": {"title": "Left"}, "bottom": {"title": "Bottom"}}, "errorMessage": {"title": "<PERSON>h<PERSON>ng báo lỗi", "description": ""}, "theFieldIsRequired": {"title": "Tr<PERSON><PERSON><PERSON> {{name}} b<PERSON><PERSON> b<PERSON>.", "description": ""}, "privacyCheckbox": {"title": "Privacy Checkbox?", "description": ""}, "required": {"title": "<PERSON><PERSON><PERSON> bu<PERSON>?", "description": ""}, "enablePhoneValidation": {"title": "<PERSON><PERSON>t x<PERSON>c thực điện thoại?", "description": ""}, "formFieldStyling": {"title": "Tạo kiểu trường biểu mẫu", "description": ""}, "fieldLabelStyling": {"title": "Field Label Styling", "description": ""}, "checkboxRadioButtonStyling": {"title": "Checkbox/ Radio Button/ Rating Styling", "description": ""}, "submitButtonStyling": {"title": "<PERSON><PERSON><PERSON> k<PERSON> submit", "description": ""}, "reCaptcha": {"title": "reCAPTCHA", "description": "reCAPTCHA là một dịch vụ miễn phí bảo vệ trang web của bạn khỏi spam và lạm dụng. reCAPTCHA sử dụng công cụ phân tích rủi ro nâng cao và các thách thức thích ứng để giữ cho phần mềm tự động không tham gia vào các hoạt động lạm dụng trên trang web của bạn. N<PERSON> thực hiện điều này trong khi cho phép người dùng hợp lệ của bạn vượt qua một cách dễ dàng."}, "formFields": {"title": "Trường biểu mẫu", "description": ""}, "submitButton": {"title": "Nút Submit", "description": ""}, "textInput": {"title": "Text", "description": ""}, "successScript": {"title": "<PERSON><PERSON><PERSON> l<PERSON>nh khi thành công", "description": "<PERSON><PERSON><PERSON> tập lệnh sẽ tải sau khi bấm vào nút."}, "addNewField": {"title": "<PERSON>h<PERSON><PERSON> trư<PERSON><PERSON> mới", "description": ""}, "allowDisplayInline": {"title": "Cho phép các trường hiển thị cùng dòng?", "description": ""}, "margin": {"title": "<PERSON><PERSON>", "description": ""}, "enableReCaptcha": {"title": "Bật Google reCAPTCHA", "description": ""}, "note": {"title": "<PERSON><PERSON> chú: ", "description": ""}, "isRequired": {"title": "b<PERSON><PERSON> b<PERSON>"}, "sectionColors": {"title": "<PERSON><PERSON><PERSON> s<PERSON> từng ph<PERSON>n", "description": ""}, "wheelColors": {"title": "<PERSON><PERSON><PERSON> b<PERSON>h xe", "description": ""}, "customizeWheelSections": {"title": "<PERSON><PERSON><PERSON> chỉnh các phần bánh xe", "description": ""}, "customizeGiftBox": {"title": "Customize Gift Box", "description": ""}, "addSection": {"title": "<PERSON><PERSON><PERSON><PERSON>n", "description": ""}, "addReward": {"title": "<PERSON>h<PERSON><PERSON> phần thưởng", "description": ""}, "couponWheelStyling": {"title": "<PERSON><PERSON><PERSON> kiểu b<PERSON>h xe phiếu giảm giá", "description": ""}, "outOfCode": {"title": "<PERSON><PERSON><PERSON> mã", "description": ""}, "setValue": {"title": "Đặt giá trị", "description": ""}, "unlimited": {"title": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "description": ""}, "wheelWidthPercent": {"title": "<PERSON><PERSON><PERSON> rộng b<PERSON>h xe (%)", "description": ""}, "pullDirectionPx": {"title": "Pull Direction (px)", "description": ""}, "themeColors": {"title": "<PERSON><PERSON><PERSON> sắc chủ đề", "description": ""}, "colorProfileLibrary": {"title": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> hồ sơ màu", "description": ""}, "saveColorProfile": {"title": "<PERSON><PERSON><PERSON> hồ sơ màu", "description": ""}, "colorProfileSelection": {"title": "L<PERSON><PERSON> ch<PERSON><PERSON> hồ sơ màu", "description": ""}, "searchColorProfile": {"title": "<PERSON><PERSON><PERSON> ki<PERSON><PERSON> hồ sơ màu", "description": ""}, "noData": {"title": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "description": ""}, "saveNewColorProfile": {"title": "<PERSON><PERSON><PERSON> cấu hình màu mới", "description": ""}, "enterNameColorProfile": {"title": "<PERSON><PERSON><PERSON><PERSON> tên bên dưới để xác định cấu hình màu mới của bạn", "description": ""}, "colorProfileName": {"title": "<PERSON><PERSON><PERSON> hồ sơ màu ...", "description": ""}, "saveProfile": {"title": "<PERSON><PERSON><PERSON> s<PERSON>", "description": ""}, "autoApplyCSSPrefixes": {"title": "Tự động áp dụng tiền tố CSS", "description": ""}, "customJavascript": {"title": "Javascript tùy chỉnh", "description": "JavaScript tùy chỉnh sẽ chạy sau khi chiến dịch đã được thêm vào DOM (om.Html.append. after)."}, "templateDetails": {"title": "<PERSON> ti<PERSON>", "description": ""}, "templateType": {"title": "Lo<PERSON>i Template", "description": ""}, "objectiveType": {"title": "Objective", "description": ""}, "enableSlideToggle": {"title": "Bật Slide Toggle", "description": ""}, "collapsedText": {"title": "<PERSON>ữ thu gọn", "description": ""}, "loadToggleOpen": {"title": "Hiển thị toggle", "description": ""}, "automaticOpenDelay": {"title": "Tự động mở sau (s)", "description": ""}, "loadFloatingBarAtTop": {"title": "Đ<PERSON>a Floating Bar lên đầu của trang", "description": ""}, "enableSmartSuccess": {"title": "Enable Smart Success", "description": ""}, "lockContentBelowCampaign": {"title": "Lock content below campaign", "description": ""}, "enableAttentionActivation": {"title": "Enable Attention Activation", "description": ""}, "contentLockingStyle": {"title": "Content Locking Style", "description": ""}, "remove": {"title": "Xóa", "description": ""}, "blur": {"title": "Mờ", "description": ""}, "backgroundOverlayColor": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "yesNoMutipleStepsCampaigns": {"title": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> n<PERSON> b<PERSON><PERSON>c Yes / No", "description": "<PERSON>ến dịch nhiều bước là một trong những loại chiến dịch chuyển đổi cao nhất và nó <strong> đã đư<PERSON>c chứng minh là có thể tăng chuyển đổi lên tới 18%. </strong>", "button": "Bật Yes/No cho chiến dịch này"}, "importFromOptinView": {"title": "<PERSON><PERSON><PERSON><PERSON> từ Optin view", "description": ""}, "importFromSuccessView": {"title": "<PERSON><PERSON><PERSON><PERSON> từ Success view", "description": ""}, "importFromYesNoView": {"title": "<PERSON><PERSON><PERSON><PERSON> từ Yes/No view", "description": ""}, "yesButton": {"title": "Nút Yes", "description": "", "successScript": "<PERSON><PERSON><PERSON> tập lệnh sẽ tải sau khi nhấn vào <strong> nút Yes </strong>."}, "noButton": {"title": "Nút No", "description": "", "successScript": "<PERSON><PERSON><PERSON> tập lệnh sẽ tải sau khi nhấn vào <strong> nút No </strong>."}, "yesButtonStyling": {"title": "<PERSON><PERSON><PERSON> k<PERSON> n<PERSON>", "description": ""}, "noButtonStyling": {"title": "<PERSON><PERSON>o kiểu nút No", "description": ""}, "reverseButtonOrder": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> thứ tự nút", "description": ""}, "displayButtonsInline": {"title": "<PERSON><PERSON><PERSON> thị các nút trên một dòng", "description": ""}, "css": {"title": "Css"}, "htmlPanel": {"title": "Html", "description": "HTML tùy chỉnh có thể bao gồm CSS và JavaScript được thêm vào chiến dịch."}, "formShouldBeAdded": {"title": "Form phải đư<PERSON><PERSON> thêm vào", "description": ""}, "customHtmlIntegration": {"title": "<PERSON><PERSON><PERSON> hợp HTML tùy chỉnh", "description": ""}, "openEmail": {"title": "Mở email của khách"}, "clickToCall": {"title": "<PERSON><PERSON><PERSON><PERSON> vào để gọi"}, "clickCopyText": {"title": "<PERSON><PERSON><PERSON><PERSON> vào để sao chép văn bản"}, "closeTheCampaign": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch"}, "reLoadPage": {"title": "<PERSON><PERSON><PERSON> lại trang"}, "openWindow": {"title": "Mở trong cửa sổ mới"}, "sendOtpAndGoToView": {"title": "Gửi OTP & chuyển đến màn hình"}, "serviceProviderManagement": {"title": "<PERSON><PERSON><PERSON><PERSON> lý nhà cung cấp dịch vụ", "description": ""}, "selectProvider": {"title": "<PERSON><PERSON><PERSON> nhà cung cấp"}, "addNewProvider": {"title": "<PERSON><PERSON><PERSON><PERSON> mới", "description": ""}, "addNewServiceProvider": {"title": "<PERSON><PERSON><PERSON><PERSON> mới nhà cung cấp", "description": ""}, "otpForm": {"title": "Biểu mẫu OTP", "description": ""}, "timerLabel": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "description": ""}, "labelPosition": {"title": "<PERSON><PERSON> trí nh<PERSON>n", "description": ""}, "expiredMessage": {"title": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON><PERSON> hế<PERSON> hạn", "description": ""}, "resendButton": {"title": "<PERSON><PERSON><PERSON> lại", "description": ""}, "description": {"title": "<PERSON><PERSON>", "description": ""}, "verifyButton": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "label": {"title": "<PERSON><PERSON>ã<PERSON>", "description": ""}, "messages": {"title": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON><PERSON>", "description": ""}, "message": {"title": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON><PERSON>", "description": ""}, "failedVerification": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>n thất bại", "description": ""}, "resentOTP": {"title": "<PERSON><PERSON><PERSON> lại mã", "description": ""}, "triggerEvent": {"title": "<PERSON><PERSON><PERSON> sinh sự kiện", "description": ""}, "otpFormStyling": {"title": "OTP Form Styling", "description": ""}, "inputFieldStyling": {"title": "Input Field Styling ", "description": ""}, "resendButtonStyling": {"title": "<PERSON>send <PERSON>ton Styling ", "description": ""}, "verifyButtonStyling": {"title": "Verify <PERSON><PERSON> ", "description": ""}, "messageStyling": {"title": "Message Styling ", "description": ""}, "displayIn": {"title": "Display In ", "description": ""}, "countDownColor": {"title": "<PERSON><PERSON><PERSON> chữ đếm <PERSON>", "description": ""}, "warnDeleteProvider": {"title": "Are you sure you want to delete this vendor ?", "Description": ""}, "successView": {"title": "<PERSON><PERSON><PERSON>"}, "yesnoView": {"title": "<PERSON><PERSON><PERSON> hình Yes/No"}, "optinView": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "redirectUrl": {"title": "URL chuyển hướng", "warning": "URL bạn đã nhập không xác thực. Th<PERSON> thêm <strong> http: // </strong> hoặc <strong> https: // </strong> vào URL. Nếu bạn không chắc chắn tại sao bạn nhìn thấy thông báo này, hãy liên hệ với bộ phận hỗ trợ để được trợ giúp."}, "textBlock": {"defaultValue": "<span style=\"font-family: Montserrat; font-size: 22px; letter-spacing: 0px;\">Ưu đãi tiền thưởng đặc biệt của bạn đã được mở khóa</span>"}, "emaiTo": {"title": "<PERSON><PERSON>", "error": "<PERSON>ail bạn đã nhập không xác thực. <PERSON><PERSON><PERSON> bạn không chắc chắn tại sao bạn nhìn thấy thông báo này, hãy liên hệ với bộ phận hỗ trợ để được trợ giúp."}, "phoneNumber": {"title": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "clipBoard": {"title": "<PERSON><PERSON><PERSON> bản đ<PERSON><PERSON><PERSON> sao chép vào khay nhớ tạm"}, "newWindow": {"title": "URL cửa sổ mới"}, "imagePosition": {"title": "<PERSON><PERSON> trí h<PERSON>nh <PERSON>nh", "description": ""}, "imageRepeat": {"title": "Lặp hình ảnh", "description": ""}, "imageSize": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": ""}, "repeatVertical": {"title": "Lặp theo chi<PERSON>u dọc", "description": ""}, "noRepeat": {"title": "Không lặp", "description": ""}, "repeat": {"title": "Lặp", "description": ""}, "repeatHorizontal": {"title": "Lặp theo chiều ngang", "description": ""}, "cover": {"title": "Che phủ", "description": ""}, "contain": {"title": "<PERSON><PERSON>", "description": ""}, "auto": {"title": "<PERSON><PERSON> động", "description": ""}, "unset": {"title": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> lập", "description": ""}, "preview": {"title": "<PERSON><PERSON>", "description": ""}, "devicePreview": {"title": "<PERSON><PERSON><PERSON><PERSON> bị xem trước", "description": ""}, "manage": {"title": "<PERSON><PERSON><PERSON><PERSON> lý", "description": ""}, "savedBlocks": {"title": "Các block đ<PERSON><PERSON><PERSON> lưu", "description": ""}, "saveNewBlock": {"title": "Lưu một block {{name}} mới", "description": "<PERSON><PERSON> <PERSON>hi đư<PERSON><PERSON> lư<PERSON>, bạn sẽ có thể sử dụng block này trong tương lai. Nó sẽ giữ lại tất cả các cài đặt mà bạn đã áp dụng."}, "replaceExistingBlock": {"title": "Thay thế block {{name}} đã tồn tại", "description": "", "warning": "<PERSON>ếu lưu block này sẽ thay thế block đã lưu trước đó."}, "enterNameBlock": {"title": "<PERSON><PERSON><PERSON><PERSON> tên bên dưới để xác định block mới của bạn", "placeholder": "Đặt tên duy nhất cho block của bạn..."}, "addNote": {"title": "<PERSON><PERSON><PERSON><PERSON> một ghi chú", "placeholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú ở đây..."}, "selectValidateRequire": {"title": "{{name}} ph<PERSON><PERSON><PERSON><PERSON><PERSON>n", "description": ""}, "validateKeyCode": {"title": "Chỉ chấp nhận chữ, số và dấu gạch dưới", "description": ""}, "validateRequire": {"title": "{{name}} không thể để trống", "description": ""}, "validateMax": {"title": "{{name}} ch<PERSON><PERSON> không quá {{max}} ký tự", "description": ""}, "validateDuplicate": {"title": "<PERSON>ên này đã đư<PERSON><PERSON> sử dụng, vui lòng thử một tên kh<PERSON>c.", "description": ""}, "replaceOrCreateNewBlock": {"title": "Thay thế hoặc tạo một block mới", "description": "<PERSON><PERSON><PERSON> cách bạn muốn lưu block này bên dư<PERSON>.", "warning": "Block này đã đư<PERSON>c lưu trước đó!"}, "saveAsNew": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "replaceExisting": {"title": "<PERSON>hay thế hiện có", "description": ""}, "notes": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "updateBlockDetails": {"title": "C<PERSON><PERSON> nhật chi tiết block", "description": ""}, "saveBlock": {"title": "Lưu block", "description": ""}, "deleteSavedBlock": {"title": "Bạn có chắc muốn xóa block đã lưu?", "buttonConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a"}, "sortBy": {"title": "<PERSON><PERSON><PERSON> xếp bởi", "description": ""}, "searchSavedBlocks": {"title": "Tìm kiếm block đã lưu", "placeholder": "Tìm kiếm block đã lưu..."}, "deleteColorProfile": {"title": "<PERSON><PERSON><PERSON> hồ sơ màu", "description": "<PERSON>ạn có chắc muốn xóa hồ sơ màu \"{{name}}\"?"}, "confirmDeletionBlock": {"title": "Xác nhận xóa block này", "description": "Bạn có chắc muốn xóa block này không?"}, "blockSelectedNotFound": {"title": "<PERSON><PERSON><PERSON><PERSON> tìm thấy block đ<PERSON><PERSON><PERSON> chọn", "description": ""}, "fieldsBlockAlreadyUsed": {"title": "Block Optin Field đã đư<PERSON>c sử dụng", "description": "Mỗi chế độ xem chỉ được sử dụng một block Optin Field. Để thêm block Optin field mới, vui lòng xóa block hiện có."}, "viewStylingPanel": {"title": "{{viewName}} View Styling", "description": ""}, "switchGlobalStylingModal": {"enableTitle": "Bật Global View Styling?", "disableTitle": "Tắt Global View Styling?", "disableDescription": "Tạo kiểu duy nhất cho từng Chế độ xem chiến dịch của bạn bằng cách tắt Global View Styling. <PERSON><PERSON> <PERSON><PERSON> tắ<PERSON>, <PERSON><PERSON><PERSON> <PERSON>uy<PERSON><PERSON> sang bất kỳ chế độ xem nào để tùy chỉnh chế độ xem đó ngay tại đây.", "enableDescription": "Bạn đang bật Global View Styling. <PERSON>u khi đượ<PERSON> bậ<PERSON> lạ<PERSON>, bạn có thể áp dụng kiểu cho tất cả các Chế độ xem cùng một lúc. <PERSON><PERSON><PERSON> kiểu hiện đang được sử dụng cho mỗi Chế độ xem sẽ tiếp tục được sử dụng cho đến khi bạn thực hiện và lưu thay đổi."}, "fileType": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "fileSize": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "under": {"title": "Dư<PERSON><PERSON>", "description": ""}, "backgroundImage": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "fileSizeTooBig": {"title": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tệp quá lớn", "description": ""}, "invalidFileExtension": {"title": "Phần mở rộng của tệp không hợp lệ", "description": ""}, "notAllowUploadMultipleFile": {"title": "<PERSON><PERSON><PERSON><PERSON> cho phép tải lên nhi<PERSON>u tệp", "description": ""}, "cannotUploadImage": {"title": "<PERSON><PERSON><PERSON>ng thể tải lên hình <PERSON>nh!", "description": ""}, "itemAlign": {"title": "<PERSON><PERSON><PERSON> chỉnh mục", "description": ""}, "templateNameExisted": {"title": "Mẫu đã tồn tại!", "description": ""}, "galleryTemplate": {"startTemplate": "Start with a Template", "templateType": "Template type", "moreTemplate": "More Template", "lessTemplate": "Less Template"}, "createTemplate": {"blank": "<PERSON><PERSON><PERSON><PERSON>", "formData": {"templateName": {"label": "<PERSON><PERSON>n mẫu", "errorMessage": {"duplicated": "Mẫu đã tồn tại", "required": "Tên mẫu không được để trống"}}, "templateType": {"label": "Loại mẫu", "errorMessage": {}}}, "notification": {"createFailed": "Lỗi khi tạo mẫu mới", "createSuccess": "Tạo mẫu mới thành công"}, "startGallery": "B<PERSON>t đầu với một bảng mẫu", "title": "Tạo mẫu mới"}, "editTemplate": {"remove": {"message": "Bạn có chắc chắn xóa (các) mẫu đã chọn không?", "notification": {"failed": "<PERSON><PERSON><PERSON> thất bại", "success": "Xoá {{numRows}} mẫu thành công"}, "title": "Xóa {{numRows}} mẫu"}}, "updateTemplate": {"formData": {"templateName": {"label": "<PERSON><PERSON>n mẫu", "errorMessage": {"duplicated": "Mẫu đã tồn tại", "required": "Tên mẫu không được để trống"}}}, "notification": {"updateFailed": "Lỗi khi cập nhật mẫu", "updateSuccess": "<PERSON><PERSON><PERSON> nhật mẫu thành công"}, "title": "Mẫu"}, "spread": {"title": "Lan", "description": ""}, "countDownType": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>", "description": "Count down Type"}, "inputFieldSize": {"title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch th<PERSON> trườ<PERSON>", "description": ""}, "placeholderColor": {"title": "Màu placeholder", "description": ""}, "fontSize": {"title": "Cỡ chữ", "description": ""}, "fontWeight": {"title": "<PERSON><PERSON> đậm", "black": "<PERSON><PERSON>", "extraBold": "<PERSON><PERSON><PERSON> đ<PERSON>", "bold": "Đậm", "semiBold": "Đậm nhẹ", "medium": "<PERSON>rung bình", "regular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "Nhẹ", "extraLight": "Rất nhẹ", "thin": "Mỏng", "description": ""}, "lineHeight": {"title": "<PERSON><PERSON><PERSON> cao dòng", "description": ""}, "leterSpacing": {"title": "<PERSON><PERSON><PERSON><PERSON> cách gi<PERSON>a các chữ cái", "description": ""}, "textTransform": {"title": "<PERSON>y<PERSON>n đổi văn bản", "description": ""}, "textDecoration": {"title": "<PERSON><PERSON> trí v<PERSON>n b<PERSON>n", "description": ""}, "fontStyleItalic": {"title": "<PERSON><PERSON><PERSON> chữ nghiêng", "description": ""}, "close": {"title": "Đ<PERSON><PERSON>", "description": ""}, "successScripts": {"title": "<PERSON><PERSON><PERSON> l<PERSON>nh thành công", "description": "<PERSON><PERSON><PERSON> tập lệnh sẽ chạy sau khi nút được nhấn."}, "hostedVideoUrl": {"title": "Hosted Video URL", "description": ""}, "aspectRatio": {"title": "Tỷ l<PERSON> khung hình", "description": ""}, "clear": {"title": "Xóa", "description": ""}, "colorProfile": {"title": "<PERSON><PERSON> sơ màu", "description": ""}, "edit": {"title": "Chỉnh sửa", "description": ""}, "apply": {"title": "<PERSON><PERSON>", "description": ""}, "displayStyle": {"title": "<PERSON><PERSON><PERSON> hi<PERSON>n thị", "description": ""}, "slideDirection": {"title": "<PERSON><PERSON><PERSON><PERSON> trang trình bày", "description": ""}, "slideUp": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": ""}, "slideLeft": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sang trái", "description": ""}, "slideRight": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sang phải", "description": ""}, "replace": {"title": "<PERSON>hay thế", "description": ""}, "bounce": {"title": "<PERSON><PERSON><PERSON> l<PERSON>", "description": ""}, "flash": {"title": "Flash", "description": ""}, "pulse": {"title": "Pulse", "description": ""}, "shakeX": {"title": "Shake X", "description": ""}, "shakeY": {"title": "Shake <PERSON>", "description": ""}, "rubberBand": {"title": "Rubber band", "description": ""}, "headShake": {"title": "Head Shake", "description": ""}, "swing": {"title": "Swing", "description": ""}, "tada": {"title": "<PERSON><PERSON>", "description": ""}, "wobble": {"title": "Wobble", "description": ""}, "jello": {"title": "<PERSON><PERSON>", "description": ""}, "heartBeat": {"title": "Heart Beat", "description": ""}, "jackInTheBox": {"title": "Jack In The Box", "description": ""}, "infinite": {"title": "Infinite", "description": ""}, "finite": {"title": "Finite", "description": ""}, "animation": {"title": "Animation", "description": ""}, "animationType": {"title": "Loại Animation", "description": ""}, "contentAnimation": {"title": "Animation nội dung", "description": ""}, "animationDuration": {"title": "Thời lượng Animation", "description": ""}, "animationIterationStyle": {"title": "<PERSON><PERSON>u lặp lại", "description": ""}, "animationIterationCount": {"title": "Số lần lặp lại", "description": ""}, "animationDelay": {"title": "<PERSON><PERSON> trễ Animation"}, "couponCodeIsRequired": {"title": "Coupon code là bắt buộc", "description": ""}, "modifiedTime": {"title": "<PERSON><PERSON><PERSON>i gian s<PERSON>a đổi", "description": ""}, "enterPlaceholder": {"title": "<PERSON>h<PERSON>p placeholder"}, "maximumFileUpload": {"title": "<PERSON><PERSON> thể tải lên tối đa 10 tệp!", "description": ""}, "noBlocksFound": {"title": "Không tìm thấy block", "description": ""}, "layers": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "components": {"title": "Components", "description": ""}, "addComponent": {"title": "Thêm Component", "description": ""}, "create": {"title": "Tạo", "description": ""}, "displayType": {"title": "<PERSON><PERSON><PERSON> màn hình", "description": ""}, "delete": {"title": "Xóa", "description": ""}, "duplicate": {"title": "Sao chép", "description": ""}, "hide": {"title": "Ẩn", "description": ""}, "show": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "addColumn": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "couponWheelSetting": {"title": "Cài đặt Coupon wheel", "description": ""}, "couponSetting": {"title": "Cài đặt Coupon", "description": ""}, "allAvailableCodeHasBeenAllocated": {"title": "All available codes have been allocated, please try again later.", "description": ""}, "field": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": ""}, "index": {"title": "Index", "description": ""}, "selectField": {"title": "<PERSON><PERSON><PERSON> tr<PERSON>", "description": ""}, "selectIndex": {"title": "Chọn Index", "description": ""}, "businessObject": {"title": "Business Object", "description": ""}, "businessObjectSettings": {"title": "Business Object Settings", "description": "", "useOfTemplate": "Use Business Object of Template?"}, "contentSources": {"title": "<PERSON><PERSON><PERSON> nguồn n<PERSON>i dung", "description": "", "errorMessage": {"groupSameName": "<PERSON><PERSON><PERSON> nhóm nguồn nội dung không thể có cùng tên", "groupNameEmpty": "<PERSON>ên nhóm của nguồn nội dung không thể để trống"}}, "level": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "selectContentSource": {"title": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung", "description": ""}, "filter": {"title": "<PERSON><PERSON> lọc", "description": ""}, "filters": {"title": "Filters", "description": ""}, "ranking": {"title": "Ranking", "description": ""}, "customRanking": {"title": "Custom Ranking", "description": ""}, "algorithms": {"title": "Algorithms", "description": ""}, "addAlgorithms": {"title": "Add Algorithms", "description": ""}, "selectAlgorithms": {"title": "<PERSON><PERSON><PERSON> th<PERSON> to<PERSON>", "description": ""}, "multiSelectAlgorithms": {"title": "<PERSON><PERSON><PERSON> thu<PERSON>t toán", "description": ""}, "and": {"title": "và", "description": ""}, "selectEvent": {"title": "Select event", "description": ""}, "inAnySourceOf": {"title": "In any source of", "description": ""}, "selectEventAttribute": {"title": "Select event attribute", "description": ""}, "selectAttribute": {"title": "Select attribute", "description": ""}, "selectAttributeCapitalize": {"title": "Select Attribute", "description": ""}, "fromBo": {"title": "From BO", "description": ""}, "fromContentSources": {"title": "<PERSON>ừ nguồn nội dung", "description": ""}, "fromEvent": {"title": "From Event", "description": ""}, "selectBo": {"title": "Select BO", "description": ""}, "selectContentSources": {"title": "<PERSON><PERSON><PERSON> nguồn nội dung", "description": ""}, "by": {"title": "by", "description": ""}, "in": {"title": "in", "description": ""}, "sortOrder": {"title": "Sort order", "description": ""}, "last": {"title": "last", "description": ""}, "to": {"title": "to", "description": ""}, "today": {"title": "Today", "description": ""}, "yesterday": {"title": "Yesterday", "description": ""}, "selectAnItem": {"title": "<PERSON><PERSON><PERSON> m<PERSON> mục", "description": ""}, "inputYourValue": {"title": "<PERSON><PERSON><PERSON><PERSON> giá trị", "description": ""}, "confirmDeleteContentSourceGroup": {"title": "Confirm delete content source group", "blocksDescription": "This content source group is being used in the following blocks:", "deleteDescription": "If you delete this content source group, the blocks in use will be deleted"}, "confirmChangeContentSource": {"title": "Confirm change Content source", "blocksDescription": "This Content source is being used in the following blocks:", "deleteDescription": "If you change this Content source, the blocks in use will be deleted"}, "slideShow": {"title": "Slide show", "description": "", "info": "<strong><PERSON><PERSON><PERSON> trượ<PERSON></strong>, <strong>Tự động trượt</strong>, <strong><PERSON><PERSON><PERSON> lặp</strong> chỉ hoạt động khi Xem trước hoặc Delivery"}, "rating": {"title": "Đánh giá", "description": "", "ratingType": {"title": "Loại đ<PERSON>h giá", "options": {"star": "Sao", "heart": "<PERSON>r<PERSON><PERSON> tim"}}, "valueType": "Loại giá trị", "value": "<PERSON><PERSON><PERSON> trị", "colors": {"before": "<PERSON><PERSON><PERSON> n<PERSON>n trư<PERSON><PERSON> khi đánh giá", "after": "<PERSON><PERSON><PERSON> nền sau khi đánh giá"}}, "ratingSize": {"title": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> đ<PERSON> giá", "description": ""}, "maxRating": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>h giá tối đa", "description": ""}, "allowHalfRating": {"title": "<PERSON> phép đánh giá nửa sao", "description": ""}, "showRatingText": {"title": "<PERSON><PERSON><PERSON> thị văn bản đ<PERSON>h giá", "description": ""}, "ratingLabels": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> giá", "description": ""}, "removeAll": {"title": "<PERSON><PERSON><PERSON> tất cả"}, "totalItems": {"title": "Tổng số slide", "description": ""}, "displayItems": {"title": "<PERSON><PERSON> lượng hiển thị", "description": ""}, "slideTransition": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "numberOfNextItem": {"title": "<PERSON><PERSON> lư<PERSON>ng slide tr<PERSON><PERSON><PERSON> tiếp theo", "description": ""}, "autoSlide": {"title": "<PERSON><PERSON> động trư<PERSON>", "description": ""}, "slideDelay": {"title": "<PERSON><PERSON> tr<PERSON> tr<PERSON> (giây)", "description": ""}, "slideTime": {"title": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (giây)", "description": ""}, "slideLoop": {"title": "Vòng lặp", "description": ""}, "horizontal": {"title": "<PERSON><PERSON><PERSON> ngang", "description": ""}, "vertical": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "requireErrorMessage": {"title": "Required Error Message", "description": "", "placeholder": "The {{name}} field is required."}, "invalidMessage": {"title": "Invalid Message", "description": "", "placeholder": "Invalid {{name}} format"}, "invalidMessagePhone": {"title": "A valid phone number is required. Allowed characters: any numeric digit, (), -, +, . and length >= 10."}, "full": {"title": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đủ", "description": ""}, "half": {"title": "<PERSON><PERSON><PERSON> m<PERSON>t ph<PERSON>n", "description": ""}, "columnGapPx": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch c<PERSON>t (px)", "description": ""}, "slide": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": ""}, "fade": {"title": "Mờ", "description": ""}, "cube": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "flip": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "cards": {"title": "Thẻ", "description": ""}, "coverflow": {"title": "Coverflow", "description": ""}, "creative": {"title": "Creative", "description": ""}, "nextPreviousButton": {"title": "<PERSON><PERSON><PERSON> the<PERSON> & <PERSON><PERSON><PERSON><PERSON><PERSON> đó", "description": ""}, "middle": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "topCenter": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "bottomCenter": {"title": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "description": ""}, "buttonIconColor": {"title": "<PERSON><PERSON><PERSON> bi<PERSON>u t<PERSON> n<PERSON>", "description": ""}, "buttonShape": {"title": "<PERSON><PERSON><PERSON> d<PERSON>ng n<PERSON>t", "description": ""}, "bgNormalColor": {"title": "<PERSON><PERSON><PERSON> bình thư<PERSON>", "description": ""}, "bgNormalOpacity": {"title": "", "description": ""}, "bgHoverColor": {"title": "BG hover color", "description": ""}, "bgHoverOpacity": {"title": "<PERSON><PERSON><PERSON> khi di chu<PERSON>t", "description": ""}, "slideStyling": {"title": "Tạo kiểu Slide", "description": ""}, "customCode": {"title": "Custom code", "description": ""}, "selectSource": {"title": "Select Source", "label": "Source", "placeholder": "Select source"}, "thisFieldIsRequired": {"title": "This field is required", "description": ""}, "selectCollection": {"title": "Select collection", "description": ""}, "selectCategory": {"title": "Select category", "description": ""}, "selectAField": {"title": "<PERSON><PERSON><PERSON> một trường", "description": ""}, "selectContent": {"title": "Select content", "description": ""}, "orSelectAField": {"title": "hoặc chọn một trường", "description": ""}, "valueFilterEmpty": {"title": "Value filter empty", "description": ""}, "attrDisabled": {"title": "", "description": "Data-updating of this attribute has been disabled. You still can use the output value of the last time it is processed"}, "dynamicLink": {"button": {"insert": "<PERSON><PERSON><PERSON>"}, "modal": {"error": {"boAttr": "Lỗi tải các thu<PERSON>c t<PERSON>h BO", "customerAttr": "Lỗi tải các thuộc t<PERSON> hàng", "eventAttr": "Lỗi tải các thuộc t<PERSON>h <PERSON> kiện", "eventBySource": "Lỗi tải Sự kiện trong Nguồn đã chọn", "promotionCodeAttr": "Lỗi tải các thuộc t<PERSON>h Promotion Code", "promotionPools": "Lỗi tải Promotion Pools", "source": "Lỗi tải <PERSON>", "visitorAttr": "Lỗi tải các thuộ<PERSON> t<PERSON> Visitor"}, "label": {"index": "<PERSON><PERSON> trí", "openNewTab": "Mở trong trang mới", "contentSource": "Content Source", "attribute": "<PERSON><PERSON><PERSON><PERSON>", "promotionCodeAttr": "Thuộc tính Promotion code", "promotionPools": "Promotion pools", "selectEvent": "<PERSON><PERSON><PERSON> sự kiện", "selectEventAttr": "<PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h sự kiện", "selectSource": "<PERSON>rong b<PERSON>t kỳ nguồn nào của", "text": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "url": "URL"}, "linkType": {"dynamic": "Động", "static": "<PERSON><PERSON><PERSON>"}, "message": {"confirmDeleteLink": "Bạn có chắc muốn xóa liên kết động ?", "selectDisableEventAttr": "<PERSON><PERSON><PERSON><PERSON> tính này đã bị tắt tính năng tự động cập nhật. Bạn vẫn có thể dùng kết quả của lần xử lý cuối cùng"}, "dynamicContentType": {"customerAttr": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "eventAttr": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> kiện", "promotionCode": "Promotion Code", "visitorAttr": "<PERSON><PERSON><PERSON><PERSON> V<PERSON>"}, "placeholder": {"enterValue": "<PERSON><PERSON><PERSON><PERSON> giá trị..."}, "title": {"deleteLink": "<PERSON><PERSON><PERSON> liên kết động", "insertLink": "<PERSON><PERSON><PERSON> đ<PERSON>ờng dẫn"}}}, "dynamicContent": {"title": "Dynamic content", "modal": {"error": {"boAttr": "Lỗi tải các thu<PERSON>c t<PERSON>h BO", "customerAttr": "Lỗi tải các thuộc t<PERSON> hàng", "eventAttr": "Lỗi tải các thuộc t<PERSON>h <PERSON> kiện", "eventBySource": "Lỗi tải Sự kiện trong Nguồn đã chọn", "promotionCodeAttr": "Lỗi tải các thuộc t<PERSON>h Promotion Code", "promotionPools": "Lỗi tải Promotion Pools", "source": "Lỗi tải <PERSON>", "visitorAttr": "Lỗi tải các thuộ<PERSON> t<PERSON> Visitor"}, "label": {"index": "<PERSON><PERSON> trí", "contentSource": "Content Source", "attribute": "<PERSON><PERSON><PERSON><PERSON>", "promotionCodeAttr": "Thuộc tính Promotion code", "promotionPools": "Promotion pools", "selectEvent": "<PERSON><PERSON><PERSON> sự kiện", "selectEventAttr": "<PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h sự kiện", "selectSource": "<PERSON>rong b<PERSON>t kỳ nguồn nào của", "displayFormat": "<PERSON><PERSON><PERSON> dạng hiển thị", "numberFormated": "<PERSON><PERSON><PERSON> d<PERSON>ng", "formNumberDisplayFormat": {"currency": "<PERSON><PERSON><PERSON> tiền tệ", "currencySymbol": "<PERSON><PERSON>", "currencyCode": "Mã", "compact": "<PERSON><PERSON> g<PERSON>n s<PERSON>", "decimalPlace": "Chỉ số thập phân ", "grouping": "<PERSON><PERSON><PERSON><PERSON> hàng ngh<PERSON>n", "groupingIconTooltipTitle": "<PERSON><PERSON><PERSON> dấu phân tách giữa các nhóm hàng nghìn", "decimal": "<PERSON><PERSON><PERSON><PERSON> phân", "decimalIconTooltipTitle": "<PERSON><PERSON><PERSON> dấu phân tách phần nguyên và thập phân"}, "datimeDisplayFormat": {"dateDF": "<PERSON><PERSON>ng hiển thị ng<PERSON>y", "timeDF": "<PERSON><PERSON>ng hiển thị giờ", "short": "<PERSON><PERSON><PERSON>", "medium": "Vừa", "long": "<PERSON><PERSON><PERSON>", "use24hour": "Dùng giờ 24 tiếng"}}, "message": {"confirmDeleteVariable": "Bạn có chắc muốn xóa văn bản động này ?", "selectDisableEventAttr": "<PERSON><PERSON><PERSON><PERSON> tính này đã bị tắt tính năng tự động cập nhật. Bạn vẫn có thể dùng kết quả của lần xử lý cuối cùng"}, "dynamicContentType": {"customerAttr": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "eventAttr": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> kiện", "promotionCode": "Promotion Code", "visitorAttr": "<PERSON><PERSON><PERSON><PERSON> V<PERSON>", "custom": "Tuỳ chỉnh"}, "attrDisplayFormat": {"number": "Dạng số", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "percentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "datetime": "<PERSON><PERSON><PERSON> g<PERSON>"}, "title": {"addDynamicContent": "Add Dynamic Content", "deleteVariable": "<PERSON><PERSON><PERSON> văn bản động"}}, "sidePanel": {"highlightCheckbox": "<PERSON><PERSON><PERSON> dấu nội dung động", "title": "<PERSON><PERSON>i dung động", "addDynamicContent": "<PERSON><PERSON><PERSON><PERSON> nội dung động"}}, "pleaseInputValue": {"title": "Please input value & press Enter", "description": ""}, "search": {"title": "<PERSON><PERSON><PERSON> k<PERSON> ..."}, "selectItems": {"title": "<PERSON><PERSON><PERSON> c<PERSON> mục"}, "showChecked": {"title": "<PERSON><PERSON><PERSON> thị đã chọn"}, "viewAll": {"title": "<PERSON><PERSON> t<PERSON>t cả"}, "uncheckAll": {"title": "Bỏ chọn tất cả"}, "listingPerformance": {"controls": {"columns": {"dropdown": {"savedColumns": "Cột đã lưu", "trigger": "<PERSON><PERSON><PERSON> đ<PERSON>i cột ..."}, "modal": {"add": {"content": {"addAllColumns": "<PERSON><PERSON><PERSON><PERSON> tất cả cột", "dragdrop": "<PERSON><PERSON><PERSON> và thả để sắp xếp", "removeAllColumns": "<PERSON><PERSON><PERSON> tất cả cột", "selectMetric": "<PERSON><PERSON>n chỉ số", "searchColumns": "<PERSON><PERSON><PERSON> c<PERSON>t sửa đổi"}, "footer": {"checkbox": "<PERSON><PERSON><PERSON> bộ cột này", "input": {"placeholder": "<PERSON><PERSON><PERSON> đ<PERSON>i tên cột"}}, "title": "<PERSON><PERSON><PERSON> đ<PERSON> cột"}, "remove": {"message": "Bạn có chắc muốn xóa cột này ?", "title": "<PERSON><PERSON><PERSON> s<PERSON>a đổi cột"}}}, "download": {"options": {"all": "<PERSON><PERSON><PERSON> tất cả dữ liệu", "selected": "<PERSON><PERSON>t dữ liệu đã chọn", "page": "<PERSON><PERSON>t dữ liệu ở trang hiện tại"}, "title": "<PERSON><PERSON><PERSON> dữ liệu"}, "filter": {"dropdown": {"filterValue": {"placeholder": {"text": "<PERSON><PERSON>, 123"}}, "savedFilters": {"empty": "<PERSON><PERSON><PERSON><PERSON> có bộ lọc đã lưu nào", "title": "<PERSON>ộ lọc đã lưu"}, "trigger": "<PERSON><PERSON><PERSON><PERSON> bộ lọc"}, "modal": {"duplicatedFilterSet": {"message": "<PERSON><PERSON> lọc {{filterName}} đã tồn tại. Bạn có muốn ghi đè lên nó không?", "title": "<PERSON><PERSON><PERSON><PERSON> báo"}, "removeFilterSet": {"message": "Bạn có chắc muốn xóa bộ lọc {{filterName}} ?", "title": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> bộ lọc"}, "saveFilterSet": {"label": "<PERSON><PERSON><PERSON> bộ lọc đã lưu", "title": "<PERSON><PERSON> lọc đã lưu mới"}}}, "search": {"addFilter": "<PERSON><PERSON><PERSON>rê<PERSON> ", "redirect": "Hoặc đi đến"}}, "media-template": {"remove": {"message": "Bạn có chắc chắn xóa (các) mẫu đã chọn không?", "notification": {"failed": "<PERSON><PERSON><PERSON> thất bại", "success": "Xoá {{numRows}} mẫu thành công"}, "title": "Xóa {{numRows}} mẫu"}}, "media-json": {"remove": {"message": "Bạn có chắc chắn xóa (các) mẫu đã chọn không?", "notification": {"failed": "<PERSON><PERSON><PERSON> thất bại", "success": "Xoá {{numRows}} mẫu thành công"}, "title": "Xóa {{numRows}} mẫu"}}}, "fallback": {"title": "Fallback", "options": {"none": "None", "hidden": "Hidden", "mostSeen": "Most seen", "mostCart": "Most cart", "mostBought": "Most bought"}}, "noWrap": {"title": "No wrap", "description": ""}, "wrap": {"title": "Wrap", "description": ""}, "gap": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "description": ""}, "display": {"title": "<PERSON><PERSON><PERSON> thị", "description": ""}, "direction": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "justify": {"title": "Justify", "description": ""}, "containerHoverStyling": {"warning": "<strong><PERSON><PERSON><PERSON> báo:</strong> <PERSON>hay đổi giá trị Vị trí, <PERSON><PERSON><PERSON><PERSON> cách quá cao so với giá trị mặc định đôi khi khiến trình duyệt hoạt động không bình thường."}, "errorBoundary": {"button": "<PERSON><PERSON><PERSON> l<PERSON>i", "message": "Xin lỗi .. đã x<PERSON>y ra lỗi, vui lòng tải lại trang. <br /> Hoặc trang sẽ tự động tải lại trong ({{second}}s)"}, "addMore": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "keywords": {"title": "từ khóa", "description": ""}, "sort": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "order": {"title": "<PERSON><PERSON><PERSON> tự", "description": ""}, "mix": {"title": "<PERSON><PERSON> tr<PERSON>n", "description": ""}, "products": {"title": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": ""}, "articles": {"title": "Articles", "description": ""}, "setValues": {"title": "<PERSON><PERSON><PERSON><PERSON> giá trị", "description": ""}, "countDownStyle": {"title": "<PERSON><PERSON>u Countdown", "options": {"grouping": "Nhóm", "separate": "<PERSON><PERSON><PERSON>"}}, "separator": {"title": "<PERSON><PERSON><PERSON> phân cách", "options": {"colon": "<PERSON><PERSON><PERSON> hai chấm", "hyphen": "<PERSON><PERSON><PERSON> g<PERSON> ngang", "slash": "<PERSON><PERSON><PERSON> g<PERSON> chéo"}}, "separatorStyling": {"title": "<PERSON><PERSON>o kiểu dấu phân cách", "description": ""}, "tableBlock": {"title": "Table", "description": ""}, "shakeAndWin": {"title": "Shake and win", "description": ""}, "shakeStyling": {"title": "<PERSON>", "description": ""}, "defaultReminderNotification": {"title": "Default Reminder Notification", "description": " "}, "reminderNotificationStyling": {"title": "Reminder Notification Styling", "description": " "}, "reminderNotificationLabel": {"title": "<PERSON><PERSON><PERSON> ngay để nhận quà", "description": ""}, "shake": {"title": "Shake", "description": ""}, "shakeTriggerSetting": {"title": "<PERSON>gger Setting", "description": ""}, "shakeTrigger": {"title": "<PERSON>", "description": ""}, "imageBeforeShake": {"title": "Image Before Shake", "titleUrl": "Image Before Shake URL", "description": ""}, "imageToShake": {"title": "Image To Shake", "titleUrl": "Image To Shake URL", "description": ""}, "imageAfterShake": {"title": "Image After Shake", "titleUrl": "Image After Shake URL", "description": ""}, "system": {"title": "System", "description": ""}, "userAction": {"title": "User Action", "description": ""}, "shakeBy": {"title": "By", "description": ""}, "referral": {"title": "Referral", "description": ""}, "clickOnThisBlock": {"title": "Click on This block", "description": ""}, "optinField": {"title": "Optin field", "description": ""}, "horizontalShake": {"title": "Horizontal Shake", "description": ""}, "jumpShake": {"title": "Jump & Shake", "description": ""}, "horizontalSkewedShaking": {"title": "Horizontal Skewed Shaking", "description": ""}, "verticalSkewedShaking": {"title": "Vertical Skewed Shaking", "description": ""}, "constantTilt": {"title": "Constant Tilt", "description": ""}, "verticalShake": {"title": "Vertical Shake", "description": ""}, "timeToShake": {"title": "Time To Shake", "description": ""}, "timeToDelay": {"title": "Time To Delay", "description": ""}, "translationTime": {"title": "Transition time", "description": ""}, "customizeRewards": {"title": "Customize Rewards", "description": ""}, "reminderNotification": {"title": "Remider Notification", "description": "", "placeholder": "Remider Notification"}, "shakeType": {"title": "Shake Type", "description": ""}, "dimensions": {"title": "Dimensions", "description": ""}, "addDimension": {"title": "Add dimension", "description": ""}, "metric": {"title": "Metric", "description": ""}, "addMetric": {"title": "Add metric", "description": ""}, "showTop": {"title": "Show top", "description": ""}, "tableStyling": {"title": "Table styling", "description": ""}, "showHeader": {"title": "Show header", "description": ""}, "wrapText": {"title": "Wrap text", "description": ""}, "tableHeader": {"title": "Table header", "description": ""}, "tableColor": {"title": "Table color", "description": ""}, "tableHeaderBackground": {"title": "Header background color", "description": ""}, "tableCellBorder": {"title": "Cell border color", "description": ""}, "tableOddRowColor": {"title": "Odd row color", "description": ""}, "tableEvenRowColor": {"title": "Even row color", "description": ""}, "tableLabels": {"title": "Table labels", "description": ""}, "tableBody": {"title": "Table body", "description": ""}, "tableFooter": {"title": "Table footer", "description": ""}, "rowNumbers": {"title": "Row numbers", "description": ""}, "missingData": {"title": "Missing data", "description": ""}, "dimensionStyling": {"title": "Dimension styling", "description": ""}, "metricStyling": {"title": "Metric styling", "description": ""}, "number": {"title": "Number", "description": ""}, "heatMap": {"title": "Heatmap", "description": ""}, "bar": {"title": "Bar", "description": ""}, "columnStyle": {"title": "Column style", "description": ""}, "noDimensionSelected": {"title": "Don't have dimension selected", "description": ""}, "noMetricSelected": {"title": "Don't have metric selected", "description": ""}, "showNull": {"title": "Show \"null\"", "description": ""}, "showZero": {"title": "Show \"0\"", "description": ""}, "showNoData": {"title": "Show \"No data\"", "description": ""}, "showBlank": {"title": "Show \"\" (blank)"}, "layout": {"title": "Bố cục", "description": ""}, "responsiveDesign": {"title": "Responsive Design", "hideOnMobile": "Hide on Mobile", "hideOnDesktop": "Hide on Desktop", "notStackOnMobile": "Do Not Stack on Mobile"}, "descending": {"title": "Descending", "description": ""}, "ascending": {"title": "Ascending", "description": ""}, "saveLongWarning": {"title": "Tiến trình này cần nhiều thời gian để xử lý", "description": ""}, "saveTemplate": {"success": "<PERSON><PERSON><PERSON> mẫu thành công", "faild": "<PERSON><PERSON><PERSON> mẫu thất bại"}, "interest": {"title": "interest", "description": ""}, "setIndex": {"title": "Set index", "indexFrom": "Index from", "setAutoIndex": "Set auto index", "warningTooltips": "The index of the blocks in the {{name}} is not the same", "setIndexSuccess": {"title": "Set Index Success", "description": "{{childBlockName}} in the {{blockName}} will have index from {{fromIndex}} to {{toIndex}}"}, "maxIndex": "Max index up to {{number}}"}, "thumbnailCapture": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh thumbnail", "tooltip": "<PERSON><PERSON><PERSON> chụp hình thumbnail sẽ làm tiến trình lưu trữ nhanh hơn"}, "object": {"title": "Object", "description": ""}, "array": {"title": "Array", "description": ""}, "boolean": {"title": "Boolean", "description": ""}, "keyCode": {"title": "Key code", "placeholder": "Enter key code here..."}, "filedKeyCode": {"title": "Field Key code", "placeholder": "Enter field key code here..."}, "value": {"title": "Value", "description": "Enter value here..."}, "addingBlocks": {"title": "Adding blocks", "description": "Let's start by adding blocks"}, "ordinalText": {"first": "<PERSON><PERSON><PERSON> tiên", "second": "thứ hai", "third": "thứ ba", "fourth": "thứ tư", "fifth": "thứ năm", "sixth": "thứ sáu", "seventh": "th<PERSON> b<PERSON>y", "eighth": "thứ tám", "ninth": "thứ ch<PERSON>", "tenth": "th<PERSON> m<PERSON>"}, "addColor": {"title": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>u", "description": ""}, "duplicatedKey": {"title": "Key is duplicated in object", "description": ""}, "verifyFormat": {"title": "Verify format", "description": ""}, "dynamicFields": {"title": "Dynamic Fields", "description": ""}, "validJsonFormat": {"title": "<PERSON><PERSON><PERSON> d<PERSON> h<PERSON> l<PERSON>"}, "name": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "autoClose": {"title": "<PERSON><PERSON> động tắt", "description": ""}, "at": {"title": "Tại", "description": ""}, "after": {"title": "Sau", "description": ""}, "verifiedSubmit": {"title": "Verified submit", "description": ""}, "attribute": {"title": "Attribute", "description": ""}, "where": {"title": "Where", "description": ""}, "refineByAttribute": {"title": "Refine by attribute", "description": ""}, "fieldNotExist": {"title": "Tr<PERSON><PERSON><PERSON> này không tồn tại", "description": ""}, "trackingClick": {"title": "Tracking Click", "description": ""}, "syncDataToBO": {"title": "Sync data to Business Object", "description": ""}, "templateName": {"titile": "<PERSON><PERSON>n mẫu"}, "richMenuSettings": {"title": "Cài đặt Rich Menu"}, "areaAction": {"title": "Area Action"}, "richMenuDetails": {"title": "<PERSON> ti<PERSON>t <PERSON>", "menuName": "<PERSON><PERSON><PERSON>", "aliasID": "Alias ID", "setAtDefault": "Đặt làm Rich Menu mặc định"}, "areaLayout": {"title": "Bố cục khu vực", "customizedLayout": "<PERSON><PERSON> cục tùy chỉnh", "numOfColumns": "Số cột", "numOfRows": "Số dòng", "maxAreaError": "<PERSON><PERSON> <PERSON> không đ<PERSON><PERSON><PERSON> lớn hơn {{max}}"}, "menuImage": {"title": "Hình <PERSON>nh menu"}, "chatBar": {"title": "<PERSON><PERSON> tr<PERSON> ch<PERSON>"}, "action": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>", "actionType": "<PERSON><PERSON><PERSON> hành động", "uriType": "Loại URI", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "single": {"title": "Đơn"}, "multiple": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "addNewMenu": {"title": "Thêm menu mới"}, "templateListing": {"emptyDescription": "<PERSON><PERSON><PERSON>ng có sẵn mẫu", "blankTemplate": "Tạo mẫu trống", "useTemplate": "<PERSON><PERSON>ng mẫu", "newTemplate": "Mẫu mới", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch"}, "noProvider": {"label": {"title": "<PERSON><PERSON><PERSON>ng có sẵn nhà cung cấp"}, "content": {"title": "Bạn chưa kết nối với nhà cung cấp dịch vụ OTP nào. Vui lòng liên hệ đội ngũ hỗ trợ để được trợ giúp thêm tại đây."}}, "switchMode": {"title": "<PERSON><PERSON><PERSON><PERSON> chế độ", "content": "Vi<PERSON><PERSON> chuyển chế độ sẽ làm mất toàn bộ thiết kế hiện tại của bạn. Bạn có chắc chắn muốn tiếp tục không?", "description": ""}, "noSupportMobile": {"title": "Loại hiển thị này không hỗ trợ trên ứng dụng di động, chỉ áp dụng trên website.", "description": ""}, "displayArea": {"title": "<PERSON><PERSON><PERSON> hiển thị", "description": ""}, "safeArea": {"title": "<PERSON><PERSON><PERSON> an toàn", "description": ""}, "fullBleed": {"title": "<PERSON><PERSON><PERSON> màn hình", "description": ""}, "warnDisplayArea": {"title": "Chỉ áp dụng cho ứng dụng di động", "description": ""}}