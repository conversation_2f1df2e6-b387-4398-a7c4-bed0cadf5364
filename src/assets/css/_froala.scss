.fr-element.fr-view {
  @apply ants-cursor-text;

  ol,
  ul {
    list-style: revert;
    margin: 0;
    padding: revert;
  }
}

.fr-popup {
  @apply ants-overflow-hidden;

  &.fr-active {
    z-index: 2147483630 !important;
  }

  .fr-action-buttons {
    button {
      color: var(--primary-color) !important;
    }
  }

  .fr-layer.fr-color-hex-layer {
    .fr-action-buttons {
      @apply ants-w-12;

      .fr-submit {
        button {
          @apply ants-w-12;
        }
      }
    }

    .fr-input-line {
      width: calc(100% - 60px);
    }
  }

  button[id*='linkList'] {
    display: none !important;
  }
}

.fr-svg {
  * {
    fill: var(--primary-color) !important;
  }
}

.fr-toolbar {
  @apply ants-w-[819px] ants-absolute ants-top-[auto] ants-pl-3;

  .fr-btn-grp {
    margin-left: 0 !important;
    margin-right: 0 !important;

    .fr-command.fr-btn {
      @apply ants-mr-[5px] focus:ants-bg-transparent;

      &.fr-active {
        @apply ants-bg-button-primary-active;
      }
    }
  }
}

.fr-font-search {
  outline: none;
  border: none;
  padding: 10px 20px;
  padding-top: 10px;
  position: sticky;
  top: 0;
  border-bottom: 1px solid var(--border-box-color);
}

.fr-front-dropdown-list {
  height: 230px;
  overflow: auto;
}
