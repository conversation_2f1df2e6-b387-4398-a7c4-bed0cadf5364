/*----------  Dropdown  ----------*/
.ant-dropdown-menu {
  @apply ants-w-[160px] ants-max-h-[200px] ants-overflow-auto;

  .ant-dropdown-menu-item {
    @apply ants-text-cus-base ants-text-normal;

    &.ant-dropdown-menu-item-option-selected:not(.ant-select-item-option-disabled) {
      @apply ants-bg-button-primary-active ants-font-normal ants-text-cus-base;
    }

    &.ant-dropdown-menu-item-option-disabled {
      @apply ants-text-cus-disable;
    }

    &.selected {
      @apply ants-bg-button-primary-active;
    }
  }
}

.ant-select-dropdown {
  .ant-select-item {
    @apply ants-text-cus-base ants-text-normal;

    &.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      @apply ants-bg-button-primary-active ants-font-normal ants-text-cus-base;
    }

    &.ant-select-item-option-disabled {
      @apply ants-text-cus-disable;
    }
  }
}

/*----------  Tree <PERSON>de  ----------*/
.ant-select-tree-treenode {
  .ant-select-tree-node-content-wrapper {
    @apply ants-min-h-[32px] ants-text-xs ants-leading-[30px];

    &.ant-select-tree-node-selected {
      @apply ants-bg-button-primary-active;
    }
  }

  &.filter-node {
    &:not(.ant-select-tree-treenode-disabled) {
      .ant-select-tree-title {
        font-weight: 400 !important;
      }
    }
  }
}

/*----------  Button  ----------*/
.ant-btn {
  @apply ants-flex ants-items-center ants-justify-center ants-space-x-2 ants-rounded-button ants-px-10px ants-border ants-border-button-outline-border ants-text-normal ants-text-primary ants-font-roboto ants-transition-colors ants-duration-300 ants-font-bold;
  background: white;
  line-height: 1;

  i {
    font-size: 20px;
  }

  .ant-btn-loading-icon {
    @apply ants-flex ants-items-center;
  }

  &:hover,
  &:focus {
    @apply ants-bg-button-primary-hover ants-border-button-outline-border ants-text-primary;
  }

  &:active {
    @apply ants-bg-button-primary-active;
  }

  &:disabled {
    pointer-events: none !important;
    background-color: var(--accent-1) !important;
    color: var(--accent-3) !important;
  }

  &.ant-btn-primary {
    @apply ants-bg-primary ants-border-none ants-text-cus-primary;

    &:hover,
    &:focus {
      @apply ants-bg-button-hover;
    }

    &:disabled {
      pointer-events: none !important;
      background-color: var(--accent-2) !important;
      color: var(--accent-4) !important;
    }

    &:active {
      @apply ants-bg-button-active;
    }
  }

  &.ant-btn-text {
    @apply ants-border-none ants-text-primary;
  }

  &.ant-btn-link {
    @apply ants-border-0 ants-bg-transparent ants-font-normal;
  }

  &.ant-btn-lg {
    @apply ants-h-10 ants-text-medium;
  }

  &.ant-btn-round {
    @apply ants-h-10 ants-text-medium;
  }

  &.ant-btn-circle {
    @apply ants-rounded-[50%];
  }
}

/*----------  Modal  ----------*/
.ant-modal-root .ant-modal-confirm {
  .ant-modal-body {
    @apply ants-p-5;

    .ant-modal-confirm-body {
      .ant-modal-confirm-title {
        @apply ants-text-cus-base;
      }

      .ant-modal-confirm-content {
        @apply ants-text-cus-base ants-text-normal;
      }
    }

    .ant-modal-confirm-btns {
      @apply ants-flex ants-flex-row-reverse ants-float-left;

      .ant-btn + .ant-btn {
        @apply ants-mr-2 ants-ml-0;
      }
    }
  }
}

/*----------  Table  ----------*/
.ant-table-container {
  .ant-table-content {
    table {
      @apply ants-text-xs;

      thead.ant-table-thead {
        .ant-checkbox-indeterminate .ant-checkbox-inner::after {
          @apply ants-bg-primary;
        }

        > tr {
          > th {
            @apply ants-px-[10px] ants-py-[8px];
            @apply ants-bg-table-header-bg-color ants-border ants-border-table-header-border-color;
            @apply ants-font-bold ants-whitespace-nowrap;
            @apply ants-select-none;

            border-left: 0 !important;
            border-right: 1px solid var(--table-header-border-color) !important;

            &:last-child {
              border-right: 0 !important;
            }

            &.ant-table-cell-ellipsis {
              @apply ants-max-w-0;
            }
          }
        }
      }

      tr.ant-table-row-selected {
        td {
          @apply ants-bg-white;
        }
      }

      td.ant-table-cell {
        @apply ants-h-[40px] ants-min-h-[40px] ants-px-[10px] ants-py-[5.520px];

        &.ant-table-cell-ellipsis {
          @apply ants-max-w-0;
        }
      }
    }
  }

  .ant-checkbox-checked {
    .ant-checkbox-inner {
      @apply ants-bg-primary ants-border-primary;
    }
  }
}

/*----------  Message  ----------*/
.ant-message-custom-content {
  @apply ants-flex ants-items-center ants-text-normal ants-text-cus-base;
}

/*----------  Tooltip  ----------*/
.ant-tooltip {
  .ant-tooltip-content {
    .ant-tooltip-inner {
      @apply ants-text-normal;
    }
  }
}

/*----------  Popover  ----------*/
.ant-popover {
  &.no-arrow {
    .ant-popover-arrow {
      @apply ants-hidden;
    }
  }

  &.no-inner-padding {
    .ant-popover-inner-content {
      @apply ants-p-0;
    }
  }

  &.title-bg-blue {
    .ant-popover-title {
      @apply ants-bg-blue-second ants-border-table-header-border-color ants-border-b;
    }
  }
}

/*----------  Empty description  ----------*/
.ant-empty {
  .ant-empty-description {
    @apply ants-text-normal;
  }
}

/*----------  Checkbox Group ----------*/
.ant-checkbox-group {
  .ant-checkbox-wrapper {
    @apply ants-font-roboto ants-text-cus-base ants-text-normal;

    &:hover {
      .ant-checkbox-inner {
        @apply ants-border-primary;
      }
    }

    .ant-checkbox {
      &:hover {
        .ant-checkbox-inner {
          @apply ants-border-primary;
        }
      }

      .ant-checkbox-input {
        &:focus {
          & + .ant-checkbox-inner {
            @apply ants-border-primary;
          }
        }
      }
    }

    .ant-checkbox-checked {
      .ant-checkbox-inner {
        @apply ants-bg-primary ants-border-primary;
      }
    }

    & + .ant-checkbox-wrapper {
      margin-left: 0px !important;
    }
  }
}

/*----------  Tree Select ----------*/
.ant-select-tree-list {
  .ant-select-tree-node-content-wrapper {
    padding: 0px 12px;
  }

  .ant-select-tree-switcher {
    @apply ants-absolute ants-right-1 ants-z-10;
  }
}

/*----------  Select ----------*/

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: #f2f9ff;
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #deeffe !important;
}
