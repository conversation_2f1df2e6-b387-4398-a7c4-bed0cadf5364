/*GLOBAL_STYLE_MARKER*/
/* Variables */
@import '@antscorp/icons/main.css';

@import '_variables';

/* Fonts */
@import 'fonts';

/* Base */
@import 'base';

/* Antd */
@import 'antd';

/* Froala */
@import 'froala';

/* Animate */
@import 'animate';

/* Tailwind */
@tailwind base;

@tailwind components;

@tailwind utilities;

@layer base {
  img {
    @apply ants-inline-block;
  }
}

img {
  -khtml-user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

html,
body {
  @apply ants-h-full ants-w-full;

  font-family: var(--font-family) !important;
}

body {
  @apply ants-font-roboto ants-bg-background;
}

p,
label {
  line-height: 1.5em;
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
}

/* heading */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply ants-font-bold ants-text-cus-base;
}

h6 {
  font-size: 20px;
}

h5 {
  font-size: 24px;
}

/*----------  Scroll bar  ----------*/
/* width */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--accent-6);
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: transparent !important;
}

/*----------  Container queries  ----------*/

.preview-container {
  container-type: inline-size;
  container-name: previewContainer;
}

/* Responsive */
@container preview-container size(max-width: 640px) {
  .row__block {
    flex-wrap: wrap !important;
  }

  .column__block {
    width: 100% !important;
  }
}

/* Responsive */
// @container previewContainer (max-width: 768px) {
//   .row__block {
//     flex-wrap: wrap !important;
//   }

//   .column__block {
//     width: 50% !important;

//     &[data-columnslength='1'] {
//       width: 100% !important;
//     }
//   }

//   #block-wrapper-uwu2i86v5bmeob8098ap > .content-block {
//     background-color: blue !important;
//   }
// }

/*----------  Text editor  ----------*/
.fr-box {
  .fr-wrapper {
    .fr-element {
      * {
        word-break: break-word;
      }
    }
  }
}

/*-----------  Tree select ----------*/
.ant-select-tree {
  &-list {
    &-holder {
      &-inner {
        > .ant-select-tree-treenode {
          position: relative;
        }
      }
    }
  }
}

.scrollbar-hidden {
  -ms-overflow-style: none; /* for Internet Explorer, Edge */
  scrollbar-width: none; /* for Firefox */

  &::-webkit-scrollbar {
    display: none;
  }
}

.label-required {
  &::after {
    display: inline-block;
    margin-inline-start: 4px;
    color: #cf1825;
    font-size: 12px;
    line-height: 1;
    content: '*';
  }
}
