<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <script src="//sandbox-st-platform.anthill.vn/js/libs/ants-tech-navigation/ants-tech-navigation.min.js"></script>
    <!-- <script src="https://unpkg.com/container-query-polyfill/cqfill.iife.min.js"></script> -->
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <meta property="og:title" content="React Boilerplate Example App" />
    <meta property="og:description" content="" />

    <meta name="description" content="" />

    <script>
      var ENV = 'development',
        NODE_ENV = 'development',
        PRODUCTION = false,
        U_OGS = 'uogs_33167',
        SITE_URL = 'https://sandbox-media-template.antsomi.com',
        STATIC_DOMAIN = 'https://sandbox-st-media-template.antsomi.com',
        LOGO_MAIN = '',
        LOGO_SUB = 'undefined',
        ASSETS_URL = 'https://sandbox-media-template.antsomi.com',
        API_VERSION = 'v1',
        API_LOGGING = 'https://sandbox-adx.ants.vn/logging',
        API_HOST = 'https://sandbox-media-template.antsomi.com/cdp',
        // API_HOST = 'http://localhost:3000',
        CDP_DOMAIN = 'https://sandbox-app.cdp.asia/hub',
        UPLOAD_URL = 'https://sandbox-st-media-template.antsomi.com',
        NETWORK_ID = '33167',
        ST_VERSION = '12312311',
        PROJECT_ID = 'kekekekekekeke',
        PERMISSION_APP_CODE = 'MEDIA_TEMPLATE',
        APPLICATION_ENV = 'development',
        PERMISSION_DOMAIN = 'https://permission.antsomi.com',
        AUTH_ADX_DOMAIN = 'https://iam.ants.tech/',
        COLUMN_DOMAIN = 'https://column.ants.tech',
        LOGIN_URL =
          'https://permission.antsomi.com/33167#/login?continue=https://sandbox-media-template.antsomi.com/cdp',
        LOGOUT_URL =
          'https://permission.antsomi.com/33167#/logout?continue=https://sandbox-media-template.antsomi.com/cdp',
        PROFILE_URL = 'https://permission.antsomi.com/33167#/account/profile/',
        URL_SOCKET = 'https://ws.ants.tech',
        CDP_APP_DOMAIN = '//sandbox-cdp.antsomi.com',
        FROALA_API_KEY = 'XAG4eF3G3B10A8A5C4F3A-9qbjadhpB5gnhdoB8dtF-11cxtbrB3xtoorsvC7bmnxE2F2G2E1B10B2D1E6C1A1==';
    </script>
  </head>
  <body style="height: 100vh; overflow: hidden">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <div
      id="workspace-export-root"
      style="width: 1536px; height: 1070px; position: fixed; right: -100%; top: 100%; visibility: hidden"
    ></div>
  </body>
</html>
